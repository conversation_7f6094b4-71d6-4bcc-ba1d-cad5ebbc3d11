#!/usr/bin/env python3
"""
数据转换脚本：将videos.json中的字符串类型数值字段转换为整数
- duration: "15:30" -> 930 (秒)
- playCount: "1.2万" -> 12000
"""

import json
import re
from pathlib import Path

def duration_to_seconds(duration_str):
    """将 "15:30" 格式转换为秒数"""
    if ':' in duration_str:
        parts = duration_str.split(':')
        if len(parts) == 2:
            minutes, seconds = int(parts[0]), int(parts[1])
            return minutes * 60 + seconds
        elif len(parts) == 3:
            hours, minutes, seconds = int(parts[0]), int(parts[1]), int(parts[2])
            return hours * 3600 + minutes * 60 + seconds
    return 0

def playcount_to_int(playcount_str):
    """将中文数字格式转换为整数"""
    if not playcount_str:
        return 0
    
    # 移除空格
    playcount_str = playcount_str.strip()
    
    # 处理万、千等单位
    if '万' in playcount_str:
        number = float(playcount_str.replace('万', ''))
        return int(number * 10000)
    elif '千' in playcount_str:
        number = float(playcount_str.replace('千', ''))
        return int(number * 1000)
    else:
        # 纯数字
        try:
            return int(playcount_str)
        except ValueError:
            return 0

def convert_videos_data():
    """转换videos.json数据"""
    data_dir = Path(__file__).resolve().parent.parent / "data"
    videos_file = data_dir / "videos.json"
    
    with open(videos_file, 'r', encoding='utf-8') as f:
        videos = json.load(f)
    
    for video in videos:
        # 转换duration
        if 'duration' in video and isinstance(video['duration'], str):
            video['duration'] = duration_to_seconds(video['duration'])
        
        # 转换playCount
        if 'playCount' in video and isinstance(video['playCount'], str):
            video['playCount'] = playcount_to_int(video['playCount'])
    
    # 写回文件
    with open(videos_file, 'w', encoding='utf-8') as f:
        json.dump(videos, f, ensure_ascii=False, indent=2)
    
    print(f"已转换 {len(videos)} 个视频数据")

if __name__ == "__main__":
    convert_videos_data() 