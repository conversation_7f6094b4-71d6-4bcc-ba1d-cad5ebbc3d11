package com.shuimu.course.data.provider;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class PresetDataProvider_Factory implements Factory<PresetDataProvider> {
  @Override
  public PresetDataProvider get() {
    return newInstance();
  }

  public static PresetDataProvider_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static PresetDataProvider newInstance() {
    return new PresetDataProvider();
  }

  private static final class InstanceHolder {
    private static final PresetDataProvider_Factory INSTANCE = new PresetDataProvider_Factory();
  }
}
