package com.shuimu.course.presentation.ui.components.display

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.PlayCircle
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.presentation.ui.components.base.BadgeStyle
import com.shuimu.course.presentation.ui.components.base.VideoProgressBar
import com.shuimu.course.presentation.ui.components.base.WatchCountBadge
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

// 下载中状态的颜色
private val downloadingColor = Color(0xFF9333EA) // 紫色
// 已缓存状态的颜色
private val cachedColor = Color(0xFF16A34A) // 绿色

/**
 * VideoItem组件 - 按照预览3方案的ConstraintLayout布局
 * 显示逻辑：根据视频的购买状态和缓存状态决定显示内容
 * - 已购买或免费: 显示进度条、百分比、徽章
 * - 未购买且非免费: 显示锁图标
 * 缓存状态显示：
 * - DOWNLOADING: 标题后显示 "(下载中...)"
 * - CACHED: 标题后显示 "(已缓存)"，绿色字体
 * - NOT_CACHED: 无额外显示
 */
@Composable
fun VideoItem(
    video: Video,
    cachedVideo: com.shuimu.course.domain.model.CachedVideo? = null, // 新增：缓存信息
    isPurchasable: Boolean, // 🔥 权限由外部传入，作为最终守卫
    onPlay: (Video) -> Unit,
    onShowCacheDialog: (Video) -> Unit,
    onLockedClick: (Video) -> Unit = {},
    modifier: Modifier = Modifier
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 12.dp) // 预留徽章空间
            .graphicsLayer { clip = false }  // VideoItem容器禁用裁剪
    ) {
        ConstraintLayout(
            modifier = modifier
                .fillMaxWidth()
                .defaultMinSize(minHeight = 40.dp)
                .clickable { 
                when {
                    !isPurchasable -> onLockedClick(video)
                    video.cacheStatus == CacheStatus.DOWNLOADED -> onPlay(video)
                    video.cacheStatus == CacheStatus.NOT_CACHED -> onShowCacheDialog(video)
                    video.cacheStatus == CacheStatus.DOWNLOADING -> {
                        // 可以添加提示"正在缓存中"或不做任何操作
                    }
                }
            }
            .padding(vertical = 0.dp)
    ) {
        // 创建约束引用
        val (playIcon, titleRow, progressBar, textRow) = createRefs()

        // 左侧播放图标
        Box(
            modifier = Modifier
                .constrainAs(playIcon) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    bottom.linkTo(parent.bottom)
                }
                .size(24.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.primary),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "播放视频",
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }

        // 标题区 - 包含缓存状态显示
        Row(
            modifier = Modifier
                .constrainAs(titleRow) {
                    top.linkTo(parent.top)
                    start.linkTo(playIcon.end, margin = 12.dp)
                    end.linkTo(parent.end)
                    width = Dimension.fillToConstraints
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 标题文本 - 分段加粗，包含缓存状态
            val titleText = buildAnnotatedString {
                // 标题部分 - 加粗
                withStyle(style = SpanStyle(fontWeight = FontWeight.Medium)) {
                    append(video.title)
                }
                
                // 缓存状态部分 - 正常字重
                if (isPurchasable) {
                    // 🔥 防御点 #3: 只有在可购买时才显示缓存/下载状态
                    when (video.cacheStatus) {
                        CacheStatus.DOWNLOADED -> {
                            withStyle(style = SpanStyle(color = cachedColor, fontWeight = FontWeight.Normal)) {
                                append(" (已缓存)")
                            }
                        }
                        CacheStatus.DOWNLOADING -> {
                            // ✅ 修复：使用cachedVideo的下载进度，而不是video.progress（观看进度）
                            val downloadProgress = cachedVideo?.getProgressPercentage() ?: 0
                            val progressText = if (downloadProgress > 0) {
                                " (${downloadProgress}%)"
                            } else {
                                " (下载中...)"
                            }
                            withStyle(style = SpanStyle(color = downloadingColor)) {
                                append(progressText)
                            }
                        }
                        CacheStatus.FAILED -> {
                            withStyle(style = SpanStyle(color = Color.Red)) {
                                append(" (下载失败)")
                            }
                        }
                        CacheStatus.PENDING -> {
                            withStyle(style = SpanStyle(color = Color(0xFFFF9800))) {
                                append(" (等待下载)")
                            }
                        }
                        CacheStatus.WAITING_NETWORK -> {
                            withStyle(style = SpanStyle(color = Color(0xFFFF5722))) {
                                append(" (等待网络)")
                            }
                        }
                        CacheStatus.PAUSED -> {
                            withStyle(style = SpanStyle(color = Color.Gray)) {
                                append(" (已暂停)")
                            }
                        }
                        else -> { /* NOT_CACHED or FAILED states, do nothing */ }
                    }
                }
            }
            
            Text(
                text = titleText,
                style = MaterialTheme.typography.bodyLarge,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f)
            )

            // 右侧内容：根据购买状态显示不同内容
            if (!isPurchasable) {
                // 未购买且非免费：显示锁图标（Emoji）
                Text(
                    text = "🔒",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // 进度条和百分比、徽章（仅在已购买或免费时显示）
        if (isPurchasable) {
            // 进度条容器 - 零间距贴近标题
            Box(
                modifier = Modifier
                    .constrainAs(progressBar) {
                        top.linkTo(titleRow.bottom, margin = 0.dp) // 零间距
                        start.linkTo(playIcon.end, margin = 12.dp)
                        end.linkTo(textRow.start, margin = 5.dp)
                        width = Dimension.fillToConstraints
                        height = Dimension.value(4.dp)
                    }
            ) {
                VideoProgressBar(
                    progress = video.progress,
                    height = 1.dp,
                    backgroundColor = Color.Transparent,
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.TopStart)
                )
            }
            
            // 百分比文字 - 精确定位
            Text(
                text = "${(video.progress * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF6B7280),
                modifier = Modifier
                    .constrainAs(textRow) {
                        top.linkTo(progressBar.top)
                        end.linkTo(parent.end, margin = 43.dp)
                    }
                    .offset(y = (-4).dp)
            )
            
            // 徽章已移至外层Box中进行绝对定位
        }
        }
        
        // 悬浮的徽章 - 在外层Box中绝对定位，不受padding限制
        if (isPurchasable && video.watchCount != null) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .offset(x = (-10).dp, y = 13.dp) // 徽章向下13dp位置（原始）
                    .wrapContentSize(unbounded = true)
                    .graphicsLayer { clip = false }
            ) {
                WatchCountBadge(
                    watchCount = video.watchCount!!,
                    style = BadgeStyle.MIXED,
                    modifier = Modifier.graphicsLayer { clip = false }
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun VideoItemPreview() {
    ShuimuCourseTheme {
        Column(modifier = Modifier.padding(16.dp)) {
            val sampleVideo = Video(
                id = "1", title = "自信的建立", duration = 930, description = "...",
                categoryId = "cat1", watchCount = 1200, playCount = 3400L,
                cloudUrl = "", localPath = null, cacheStatus = CacheStatus.NOT_CACHED,
                progress = 0.7f, isPurchasable = true
            )
            
            Text("可播放/可缓存", style = MaterialTheme.typography.labelMedium)
            VideoItem(
                video = sampleVideo,
                cachedVideo = null,
                isPurchasable = true,
                onPlay = {}, onShowCacheDialog = {}, onLockedClick = {}
            )
            
            Spacer(modifier = Modifier.height(16.dp))

            Text("已缓存", style = MaterialTheme.typography.labelMedium)
            VideoItem(
                video = sampleVideo.copy(cacheStatus = CacheStatus.DOWNLOADED),
                cachedVideo = null,
                isPurchasable = true,
                onPlay = {}, onShowCacheDialog = {}, onLockedClick = {}
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text("下载中 (75%)", style = MaterialTheme.typography.labelMedium)
            VideoItem(
                video = sampleVideo.copy(cacheStatus = CacheStatus.DOWNLOADING),
                cachedVideo = com.shuimu.course.domain.model.CachedVideo(
                    videoId = "1",
                    title = "自信的建立",
                    localPath = null,
                    downloadedBytes = 75 * 1024 * 1024L, // 75MB
                    totalBytes = 100 * 1024 * 1024L, // 100MB
                    status = CacheStatus.DOWNLOADING,
                    progress = 75,
                    cloudUrl = "",
                    updatedAt = System.currentTimeMillis(),
                    priority = 0,
                    retryCount = 0,
                    lastRetryTime = 0L
                ),
                isPurchasable = true,
                onPlay = {}, onShowCacheDialog = {}, onLockedClick = {}
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text("未购买 (锁定)", style = MaterialTheme.typography.labelMedium)
            VideoItem(
                video = sampleVideo.copy(isPurchasable = false, cacheStatus = CacheStatus.DOWNLOADED), // 即使状态是已缓存
                cachedVideo = null,
                isPurchasable = false,
                onPlay = {}, onShowCacheDialog = {}, onLockedClick = {}
            )
        }
    }
}

@Preview(showBackground = true, name = "已购买 - 进行中")
@Composable
fun VideoItemPurchasedInProgressPreview() {
    ShuimuCourseTheme {
        VideoItem(
            video = Video(
                id = "1",
                title = "01. Introduction to Jetpack Compose Animation",
                duration = 930,
                description = "动画介绍",
                categoryId = "cat1",
                watchCount = 8,
                playCount = 23000L,
                cloudUrl = "url1",
                localPath = null,
                cacheStatus = CacheStatus.DOWNLOADED,
                progress = 0.65f
            ),
            cachedVideo = null,
            isPurchasable = true,
            onPlay = {},
            onShowCacheDialog = {},
            onLockedClick = {}
        )
    }
}

@Preview(showBackground = true, name = "已购买 - 未开始")
@Composable
fun VideoItemPurchasedNotStartedPreview() {
    ShuimuCourseTheme {
        VideoItem(
            video = Video(
                id = "2",
                title = "02. Mastering State Management",
                duration = 930,
                description = "状态管理",
                categoryId = "cat1",
                watchCount = 0,
                playCount = 18000L,
                cloudUrl = "url2",
                localPath = null,
                cacheStatus = CacheStatus.DOWNLOADED,
                progress = 0f
            ),
            cachedVideo = null,
            isPurchasable = true,
            onPlay = {},
            onShowCacheDialog = {},
            onLockedClick = {}
        )
    }
}

@Preview(showBackground = true, name = "已购买 - 高观看数")
@Composable
fun VideoItemPurchasedHighCountPreview() {
    ShuimuCourseTheme {
        VideoItem(
            video = Video(
                id = "3",
                title = "03. Advanced Layouts and Modifiers",
                duration = 930,
                description = "高级布局",
                categoryId = "cat1",
                watchCount = 25,
                playCount = 57000L,
                cloudUrl = "url3",
                localPath = null,
                cacheStatus = CacheStatus.DOWNLOADED,
                progress = 1.0f
            ),
            cachedVideo = null,
            isPurchasable = true,
            onPlay = {},
            onShowCacheDialog = {},
            onLockedClick = {}
        )
    }
}

@Preview(showBackground = true, name = "未购买 (锁定)")
@Composable
fun VideoItemLockedPreview() {
    ShuimuCourseTheme {
        VideoItem(
            video = Video(
                id = "4",
                title = "04. Building a Full App with MVVM",
                duration = 930,
                description = "完整应用构建",
                categoryId = "cat1",
                watchCount = null,
                playCount = null,
                cloudUrl = "url4",
                localPath = null,
                cacheStatus = CacheStatus.NOT_CACHED,
                progress = 0f
            ),
            cachedVideo = null,
            isPurchasable = false,
            onPlay = {},
            onShowCacheDialog = {},
            onLockedClick = {}
        )
    }
} 