# Claude CLI v2.0.0 - 完整使用指南

## 🚀 功能特性

- ✅ **全局可用性** - 在任何目录都能使用 `claude` 命令
- ✅ **真实API连接** - 连接到 Anthropic Claude API
- ✅ **参数增强** - 支持模型选择、温度控制、令牌限制等
- ✅ **管道支持** - 支持与其他命令组合使用
- ✅ **配置文件** - 持久化保存设置
- ✅ **跨平台** - Windows PowerShell 和 WSL 完全兼容

## 📦 安装状态

✅ Claude CLI 已安装并配置完成
- Windows: `C:\Users\<USER>\claude.bat`
- WSL: `/home/<USER>/bin/claude`
- 配置文件: `/home/<USER>/.claude/config.json`

## 🎯 基本使用

### 1. 查看版本和帮助
```bash
claude --version          # 显示版本
claude --help            # 显示帮助信息
claude                   # 显示简要使用说明
```

### 2. 直接对话
```bash
claude "解释什么是人工智能"
claude "Write a Python function to sort a list"
```

### 3. 使用参数
```bash
# 指定提示
claude -p "写一首关于春天的诗"

# 控制创造性
claude -p "解释量子物理" --temperature 0.2  # 更严谨
claude -p "写个故事" --temperature 0.9      # 更有创意

# 选择模型
claude -p "分析这段代码" --model claude-3-opus-20240229

# 限制回复长度
claude -p "总结人工智能发展史" --max-tokens 200

# 设置系统角色
claude -p "翻译这句话：Hello World" --system "你是专业翻译员"
```

## 🔧 配置管理

### 设置API密钥
```bash
# 设置你的 Anthropic API 密钥
claude --config api_key sk-ant-your-actual-api-key-here

# 查看当前API密钥
claude --config api_key
```

### 其他配置选项
```bash
# 设置默认模型
claude --config default_model claude-3-haiku-20240307

# 设置默认温度
claude --config default_temperature 0.8

# 设置默认最大令牌数
claude --config default_max_tokens 1500

# 查看配置文件位置
claude --help  # 会显示配置文件路径
```

## 🔄 管道操作

### 基础管道
```bash
# 翻译文本
echo "Hello, how are you?" | claude -p "Translate to Chinese"

# 处理文件内容
type article.txt | claude -p "请总结这篇文章"

# 代码审查
type mycode.js | claude -p "请审查这段代码，指出潜在问题"
```

### 高级工作流
```bash
# 多步处理
echo "写一篇关于AI的文章大纲" | claude > outline.txt
type outline.txt | claude -p "根据大纲写完整文章" > article.txt

# 与其他工具组合
dir *.md | foreach { type $_ | claude -p "提取关键词" >> keywords.txt }

# 批量翻译
Get-Content english_files.txt | ForEach-Object { 
    type $_ | claude -p "Translate to Chinese" > "chinese_$_"
}
```

## 🎨 实际应用场景

### 1. 代码开发助手
```bash
# 代码解释
type complex_algorithm.py | claude -p "解释这个算法的工作原理"

# 代码优化
type slow_function.js | claude -p "优化这个函数的性能"

# 生成文档
type api_functions.py | claude -p "为这些函数生成API文档"

# Bug 修复
git diff | claude -p "分析这些代码变更可能引入的问题"
```

### 2. 内容创作
```bash
# 文章写作
claude -p "写一篇关于机器学习的入门文章" --temperature 0.7 > ml_article.md

# 创意写作
claude -p "写一个科幻短故事" --temperature 0.9 --max-tokens 2000

# 内容优化
type draft_article.md | claude -p "润色这篇文章，提高可读性"
```

### 3. 数据分析
```bash
# 数据解释
type sales_data.csv | claude -p "分析这些销售数据的趋势"

# 报告生成
type metrics.json | claude -p "基于这些指标生成月度报告"
```

### 4. 学习助手
```bash
# 概念解释
claude -p "用简单的语言解释区块链技术" --system "你是一位耐心的老师"

# 学习计划
claude -p "制定一个学习Python的30天计划"

# 练习题生成
claude -p "为JavaScript基础知识生成10道练习题"
```

## ⚙️ 高级功能

### 模型选择指南
- `claude-3-haiku-20240307`: 快速响应，适合简单任务
- `claude-3-sonnet-20240229`: 平衡性能，适合大多数任务（默认）
- `claude-3-opus-20240229`: 最强性能，适合复杂任务

### 温度参数指南
- `0.0-0.3`: 高度一致性，适合事实性任务
- `0.4-0.7`: 平衡创造性和一致性（默认0.7）
- `0.8-1.0`: 高创造性，适合创意写作

### 令牌管理
- 默认最大令牌: 1000
- 短回复: 100-300 tokens
- 中等回复: 500-1000 tokens  
- 长回复: 1500-4000 tokens

## 🔍 故障排除

### 常见问题

1. **命令找不到**
   ```bash
   # 确保PATH包含claude命令
   echo $env:PATH  # Windows
   echo $PATH      # WSL
   ```

2. **API密钥错误**
   ```bash
   # 重新设置API密钥
   claude --config api_key your-correct-api-key
   ```

3. **网络连接问题**
   ```bash
   # 检查网络连接
   curl -I https://api.anthropic.com
   ```

4. **配置文件权限**
   ```bash
   # WSL中检查配置文件
   ls -la ~/.claude/config.json
   ```

## 📝 获取API密钥

1. 访问 [Anthropic Console](https://console.anthropic.com/)
2. 注册/登录账户
3. 创建新的API密钥
4. 使用 `claude --config api_key YOUR_KEY` 设置

## 🎉 现在开始使用！

你的Claude CLI已经完全配置好了，可以开始享受AI助手的强大功能：

```bash
# 试试这个
claude "写一个关于编程的励志金句"
```

享受使用 Claude CLI v2.0.0！🚀 