package com.shuimu.course.di;

import com.shuimu.course.data.remote.interceptors.UserIdInterceptor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import javax.net.ssl.HostnameVerifier;
import okhttp3.Dns;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideOkHttpClientFactory implements Factory<OkHttpClient> {
  private final Provider<HttpLoggingInterceptor> loggingInterceptorProvider;

  private final Provider<Interceptor> hostInterceptorProvider;

  private final Provider<HostnameVerifier> hostnameVerifierProvider;

  private final Provider<Dns> customDnsProvider;

  private final Provider<UserIdInterceptor> userIdInterceptorProvider;

  public NetworkModule_ProvideOkHttpClientFactory(
      Provider<HttpLoggingInterceptor> loggingInterceptorProvider,
      Provider<Interceptor> hostInterceptorProvider,
      Provider<HostnameVerifier> hostnameVerifierProvider, Provider<Dns> customDnsProvider,
      Provider<UserIdInterceptor> userIdInterceptorProvider) {
    this.loggingInterceptorProvider = loggingInterceptorProvider;
    this.hostInterceptorProvider = hostInterceptorProvider;
    this.hostnameVerifierProvider = hostnameVerifierProvider;
    this.customDnsProvider = customDnsProvider;
    this.userIdInterceptorProvider = userIdInterceptorProvider;
  }

  @Override
  public OkHttpClient get() {
    return provideOkHttpClient(loggingInterceptorProvider.get(), hostInterceptorProvider.get(), hostnameVerifierProvider.get(), customDnsProvider.get(), userIdInterceptorProvider.get());
  }

  public static NetworkModule_ProvideOkHttpClientFactory create(
      Provider<HttpLoggingInterceptor> loggingInterceptorProvider,
      Provider<Interceptor> hostInterceptorProvider,
      Provider<HostnameVerifier> hostnameVerifierProvider, Provider<Dns> customDnsProvider,
      Provider<UserIdInterceptor> userIdInterceptorProvider) {
    return new NetworkModule_ProvideOkHttpClientFactory(loggingInterceptorProvider, hostInterceptorProvider, hostnameVerifierProvider, customDnsProvider, userIdInterceptorProvider);
  }

  public static OkHttpClient provideOkHttpClient(HttpLoggingInterceptor loggingInterceptor,
      Interceptor hostInterceptor, HostnameVerifier hostnameVerifier, Dns customDns,
      UserIdInterceptor userIdInterceptor) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideOkHttpClient(loggingInterceptor, hostInterceptor, hostnameVerifier, customDns, userIdInterceptor));
  }
}
