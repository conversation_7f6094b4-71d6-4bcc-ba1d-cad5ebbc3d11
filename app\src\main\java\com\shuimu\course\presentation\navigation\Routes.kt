package com.shuimu.course.presentation.navigation

object Routes {
    const val SPLASH = "splash"  // 🔥 新增：启动页路由
    const val HOME = "home"
    const val SERIES_DETAIL = "series_detail"
    const val VIDEO_PLAYER = "video_player"
    const val LOGIN = "login"
    const val PROFILE = "profile"
    const val ORDERS = "orders"
    const val CACHE_MANAGER = "cache_manager"
    const val PAYMENT = "payment/{itemId}/{itemType}/{title}/{price}"
    const val SHARE_EARNINGS = "share_earnings"
    const val SHARE_RANKING = "share_ranking"
    const val SHARE_MATERIALS = "share_materials"

    fun paymentScreen(itemId: String, itemType: String, title: String, price: String): String {
        return "payment/$itemId/$itemType/$title/$price"
    }
} 