package com.shuimu.course.data.repository;

import com.shuimu.course.data.remote.api.SearchApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SearchRepositoryImpl_Factory implements Factory<SearchRepositoryImpl> {
  private final Provider<SearchApi> searchApiProvider;

  public SearchRepositoryImpl_Factory(Provider<SearchApi> searchApiProvider) {
    this.searchApiProvider = searchApiProvider;
  }

  @Override
  public SearchRepositoryImpl get() {
    return newInstance(searchApiProvider.get());
  }

  public static SearchRepositoryImpl_Factory create(Provider<SearchApi> searchApiProvider) {
    return new SearchRepositoryImpl_Factory(searchApiProvider);
  }

  public static SearchRepositoryImpl newInstance(SearchApi searchApi) {
    return new SearchRepositoryImpl(searchApi);
  }
}
