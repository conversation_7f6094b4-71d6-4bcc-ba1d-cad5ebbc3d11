package com.shuimu.course.data.local.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomDatabaseKt;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.RelationUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.shuimu.course.data.local.entities.CategoryEntity;
import com.shuimu.course.data.local.entities.SeriesEntity;
import com.shuimu.course.data.local.entities.VideoEntity;
import com.shuimu.course.data.local.relations.CategoryWithVideos;
import com.shuimu.course.data.local.relations.SeriesWithCategories;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class SeriesDao_Impl implements SeriesDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<SeriesEntity> __insertionAdapterOfSeriesEntity;

  private final SharedSQLiteStatement __preparedStmtOfClearAll;

  public SeriesDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSeriesEntity = new EntityInsertionAdapter<SeriesEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `series` (`id`,`title`,`icon`,`price`,`is_free`,`is_purchased`,`is_package`,`default_expanded`) VALUES (?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SeriesEntity entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        if (entity.getIcon() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getIcon());
        }
        statement.bindLong(4, entity.getPrice());
        final int _tmp = entity.isFree() ? 1 : 0;
        statement.bindLong(5, _tmp);
        final int _tmp_1 = entity.isPurchased() ? 1 : 0;
        statement.bindLong(6, _tmp_1);
        final int _tmp_2 = entity.isPackage() ? 1 : 0;
        statement.bindLong(7, _tmp_2);
        final int _tmp_3 = entity.getDefaultExpanded() ? 1 : 0;
        statement.bindLong(8, _tmp_3);
      }
    };
    this.__preparedStmtOfClearAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM series";
        return _query;
      }
    };
  }

  @Override
  public Object insertSeries(final List<SeriesEntity> series,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSeriesEntity.insert(series);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAllData(final List<SeriesEntity> seriesEntities,
      final List<CategoryEntity> categoryEntities, final List<VideoEntity> videoEntities,
      final CategoryDao categoryDao, final VideoDao videoDao,
      final Continuation<? super Unit> $completion) {
    return RoomDatabaseKt.withTransaction(__db, (__cont) -> SeriesDao.DefaultImpls.insertAllData(SeriesDao_Impl.this, seriesEntities, categoryEntities, videoEntities, categoryDao, videoDao, __cont), $completion);
  }

  @Override
  public Object clearAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SeriesWithCategories>> getSeriesWithCategories() {
    final String _sql = "SELECT * FROM series";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"videos", "categories",
        "series"}, new Callable<List<SeriesWithCategories>>() {
      @Override
      @NonNull
      public List<SeriesWithCategories> call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "icon");
            final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
            final int _cursorIndexOfIsFree = CursorUtil.getColumnIndexOrThrow(_cursor, "is_free");
            final int _cursorIndexOfIsPurchased = CursorUtil.getColumnIndexOrThrow(_cursor, "is_purchased");
            final int _cursorIndexOfIsPackage = CursorUtil.getColumnIndexOrThrow(_cursor, "is_package");
            final int _cursorIndexOfDefaultExpanded = CursorUtil.getColumnIndexOrThrow(_cursor, "default_expanded");
            final ArrayMap<String, ArrayList<CategoryWithVideos>> _collectionCategoriesWithVideos = new ArrayMap<String, ArrayList<CategoryWithVideos>>();
            while (_cursor.moveToNext()) {
              final String _tmpKey;
              _tmpKey = _cursor.getString(_cursorIndexOfId);
              if (!_collectionCategoriesWithVideos.containsKey(_tmpKey)) {
                _collectionCategoriesWithVideos.put(_tmpKey, new ArrayList<CategoryWithVideos>());
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipcategoriesAscomShuimuCourseDataLocalRelationsCategoryWithVideos(_collectionCategoriesWithVideos);
            final List<SeriesWithCategories> _result = new ArrayList<SeriesWithCategories>(_cursor.getCount());
            while (_cursor.moveToNext()) {
              final SeriesWithCategories _item;
              final SeriesEntity _tmpSeries;
              final String _tmpId;
              _tmpId = _cursor.getString(_cursorIndexOfId);
              final String _tmpTitle;
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
              final String _tmpIcon;
              if (_cursor.isNull(_cursorIndexOfIcon)) {
                _tmpIcon = null;
              } else {
                _tmpIcon = _cursor.getString(_cursorIndexOfIcon);
              }
              final int _tmpPrice;
              _tmpPrice = _cursor.getInt(_cursorIndexOfPrice);
              final boolean _tmpIsFree;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsFree);
              _tmpIsFree = _tmp != 0;
              final boolean _tmpIsPurchased;
              final int _tmp_1;
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsPurchased);
              _tmpIsPurchased = _tmp_1 != 0;
              final boolean _tmpIsPackage;
              final int _tmp_2;
              _tmp_2 = _cursor.getInt(_cursorIndexOfIsPackage);
              _tmpIsPackage = _tmp_2 != 0;
              final boolean _tmpDefaultExpanded;
              final int _tmp_3;
              _tmp_3 = _cursor.getInt(_cursorIndexOfDefaultExpanded);
              _tmpDefaultExpanded = _tmp_3 != 0;
              _tmpSeries = new SeriesEntity(_tmpId,_tmpTitle,_tmpIcon,_tmpPrice,_tmpIsFree,_tmpIsPurchased,_tmpIsPackage,_tmpDefaultExpanded);
              final ArrayList<CategoryWithVideos> _tmpCategoriesWithVideosCollection;
              final String _tmpKey_1;
              _tmpKey_1 = _cursor.getString(_cursorIndexOfId);
              _tmpCategoriesWithVideosCollection = _collectionCategoriesWithVideos.get(_tmpKey_1);
              _item = new SeriesWithCategories(_tmpSeries,_tmpCategoriesWithVideosCollection);
              _result.add(_item);
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private void __fetchRelationshipvideosAscomShuimuCourseDataLocalEntitiesVideoEntity(
      @NonNull final ArrayMap<String, ArrayList<VideoEntity>> _map) {
    final Set<String> __mapKeySet = _map.keySet();
    if (__mapKeySet.isEmpty()) {
      return;
    }
    if (_map.size() > RoomDatabase.MAX_BIND_PARAMETER_CNT) {
      RelationUtil.recursiveFetchArrayMap(_map, true, (map) -> {
        __fetchRelationshipvideosAscomShuimuCourseDataLocalEntitiesVideoEntity(map);
        return Unit.INSTANCE;
      });
      return;
    }
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT `id`,`title`,`duration`,`description`,`category_id`,`watch_count`,`cloud_url`,`local_path`,`cache_status`,`progress` FROM `videos` WHERE `category_id` IN (");
    final int _inputSize = __mapKeySet.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _stmt = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    for (String _item : __mapKeySet) {
      _stmt.bindString(_argIndex, _item);
      _argIndex++;
    }
    final Cursor _cursor = DBUtil.query(__db, _stmt, false, null);
    try {
      final int _itemKeyIndex = CursorUtil.getColumnIndex(_cursor, "category_id");
      if (_itemKeyIndex == -1) {
        return;
      }
      final int _cursorIndexOfId = 0;
      final int _cursorIndexOfTitle = 1;
      final int _cursorIndexOfDuration = 2;
      final int _cursorIndexOfDescription = 3;
      final int _cursorIndexOfCategoryId = 4;
      final int _cursorIndexOfWatchCount = 5;
      final int _cursorIndexOfCloudUrl = 6;
      final int _cursorIndexOfLocalPath = 7;
      final int _cursorIndexOfCacheStatus = 8;
      final int _cursorIndexOfProgress = 9;
      while (_cursor.moveToNext()) {
        final String _tmpKey;
        _tmpKey = _cursor.getString(_itemKeyIndex);
        final ArrayList<VideoEntity> _tmpRelation = _map.get(_tmpKey);
        if (_tmpRelation != null) {
          final VideoEntity _item_1;
          final String _tmpId;
          _tmpId = _cursor.getString(_cursorIndexOfId);
          final String _tmpTitle;
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
          final int _tmpDuration;
          _tmpDuration = _cursor.getInt(_cursorIndexOfDuration);
          final String _tmpDescription;
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
          final String _tmpCategoryId;
          _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
          final Integer _tmpWatchCount;
          if (_cursor.isNull(_cursorIndexOfWatchCount)) {
            _tmpWatchCount = null;
          } else {
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
          }
          final String _tmpCloudUrl;
          _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
          final String _tmpLocalPath;
          if (_cursor.isNull(_cursorIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
          }
          final String _tmpCacheStatus;
          _tmpCacheStatus = _cursor.getString(_cursorIndexOfCacheStatus);
          final float _tmpProgress;
          _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
          _item_1 = new VideoEntity(_tmpId,_tmpTitle,_tmpDuration,_tmpDescription,_tmpCategoryId,_tmpWatchCount,_tmpCloudUrl,_tmpLocalPath,_tmpCacheStatus,_tmpProgress);
          _tmpRelation.add(_item_1);
        }
      }
    } finally {
      _cursor.close();
    }
  }

  private void __fetchRelationshipcategoriesAscomShuimuCourseDataLocalRelationsCategoryWithVideos(
      @NonNull final ArrayMap<String, ArrayList<CategoryWithVideos>> _map) {
    final Set<String> __mapKeySet = _map.keySet();
    if (__mapKeySet.isEmpty()) {
      return;
    }
    if (_map.size() > RoomDatabase.MAX_BIND_PARAMETER_CNT) {
      RelationUtil.recursiveFetchArrayMap(_map, true, (map) -> {
        __fetchRelationshipcategoriesAscomShuimuCourseDataLocalRelationsCategoryWithVideos(map);
        return Unit.INSTANCE;
      });
      return;
    }
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT `id`,`title`,`series_id`,`price`,`is_free`,`is_purchased`,`default_expanded` FROM `categories` WHERE `series_id` IN (");
    final int _inputSize = __mapKeySet.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _stmt = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    for (String _item : __mapKeySet) {
      _stmt.bindString(_argIndex, _item);
      _argIndex++;
    }
    final Cursor _cursor = DBUtil.query(__db, _stmt, true, null);
    try {
      final int _itemKeyIndex = CursorUtil.getColumnIndex(_cursor, "series_id");
      if (_itemKeyIndex == -1) {
        return;
      }
      final int _cursorIndexOfId = 0;
      final int _cursorIndexOfTitle = 1;
      final int _cursorIndexOfSeriesId = 2;
      final int _cursorIndexOfPrice = 3;
      final int _cursorIndexOfIsFree = 4;
      final int _cursorIndexOfIsPurchased = 5;
      final int _cursorIndexOfDefaultExpanded = 6;
      final ArrayMap<String, ArrayList<VideoEntity>> _collectionVideos = new ArrayMap<String, ArrayList<VideoEntity>>();
      while (_cursor.moveToNext()) {
        final String _tmpKey;
        _tmpKey = _cursor.getString(_cursorIndexOfId);
        if (!_collectionVideos.containsKey(_tmpKey)) {
          _collectionVideos.put(_tmpKey, new ArrayList<VideoEntity>());
        }
      }
      _cursor.moveToPosition(-1);
      __fetchRelationshipvideosAscomShuimuCourseDataLocalEntitiesVideoEntity(_collectionVideos);
      while (_cursor.moveToNext()) {
        final String _tmpKey_1;
        _tmpKey_1 = _cursor.getString(_itemKeyIndex);
        final ArrayList<CategoryWithVideos> _tmpRelation = _map.get(_tmpKey_1);
        if (_tmpRelation != null) {
          final CategoryWithVideos _item_1;
          final CategoryEntity _tmpCategory;
          final String _tmpId;
          _tmpId = _cursor.getString(_cursorIndexOfId);
          final String _tmpTitle;
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
          final String _tmpSeriesId;
          _tmpSeriesId = _cursor.getString(_cursorIndexOfSeriesId);
          final Integer _tmpPrice;
          if (_cursor.isNull(_cursorIndexOfPrice)) {
            _tmpPrice = null;
          } else {
            _tmpPrice = _cursor.getInt(_cursorIndexOfPrice);
          }
          final boolean _tmpIsFree;
          final int _tmp;
          _tmp = _cursor.getInt(_cursorIndexOfIsFree);
          _tmpIsFree = _tmp != 0;
          final Boolean _tmpIsPurchased;
          final Integer _tmp_1;
          if (_cursor.isNull(_cursorIndexOfIsPurchased)) {
            _tmp_1 = null;
          } else {
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsPurchased);
          }
          _tmpIsPurchased = _tmp_1 == null ? null : _tmp_1 != 0;
          final boolean _tmpDefaultExpanded;
          final int _tmp_2;
          _tmp_2 = _cursor.getInt(_cursorIndexOfDefaultExpanded);
          _tmpDefaultExpanded = _tmp_2 != 0;
          _tmpCategory = new CategoryEntity(_tmpId,_tmpTitle,_tmpSeriesId,_tmpPrice,_tmpIsFree,_tmpIsPurchased,_tmpDefaultExpanded);
          final ArrayList<VideoEntity> _tmpVideosCollection;
          final String _tmpKey_2;
          _tmpKey_2 = _cursor.getString(_cursorIndexOfId);
          _tmpVideosCollection = _collectionVideos.get(_tmpKey_2);
          _item_1 = new CategoryWithVideos(_tmpCategory,_tmpVideosCollection);
          _tmpRelation.add(_item_1);
        }
      }
    } finally {
      _cursor.close();
    }
  }
}
