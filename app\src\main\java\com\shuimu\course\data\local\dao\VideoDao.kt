package com.shuimu.course.data.local.dao

import androidx.room.*
import com.shuimu.course.data.local.entities.VideoEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface VideoDao {
    @Query("SELECT * FROM videos")
    fun getAllVideos(): Flow<List<VideoEntity>>

    @Query("SELECT * FROM videos WHERE id = :id LIMIT 1")
    suspend fun getVideoById(id: String): VideoEntity?

    @Query("SELECT * FROM videos WHERE id = :id LIMIT 1")
    suspend fun getVideoByIdSync(id: String): VideoEntity?

    @Query("SELECT * FROM videos WHERE category_id = :categoryId")
    fun getVideosForCategory(categoryId: String): Flow<List<VideoEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVideos(videos: List<VideoEntity>)

    @Update
    suspend fun updateVideo(video: VideoEntity)
    
    @Query("UPDATE videos SET cache_status = :cacheStatus, local_path = :localPath WHERE id = :videoId")
    suspend fun updateVideoCacheStatus(videoId: String, cacheStatus: String, localPath: String?)
    
    @Query("DELETE FROM videos")
    suspend fun clearAll()
} 