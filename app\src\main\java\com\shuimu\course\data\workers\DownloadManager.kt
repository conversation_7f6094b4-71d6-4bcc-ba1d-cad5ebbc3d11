package com.shuimu.course.data.workers

import android.content.Context
import androidx.work.*
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.CacheRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.channels.awaitClose
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 视频下载管理器
 * 负责统一管理所有下载任务
 */
@Singleton
class DownloadManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val cacheRepository: CacheRepository,
    private val smartDownloadScheduler: SmartDownloadScheduler,
    private val storageManager: com.shuimu.course.data.storage.StorageManager,
    private val downloadErrorHandler: com.shuimu.course.data.error.DownloadErrorHandler
) {

    private val workManager = WorkManager.getInstance(context)

    /**
     * 启动视频下载（使用智能调度器）
     */
    suspend fun startDownload(videoId: String, cloudUrl: String, videoTitle: String = "", priority: Int = 50, estimatedSizeBytes: Long = 100 * 1024 * 1024): Result<String> {
        return try {
            android.util.Log.d("DownloadManager", "=== 使用智能调度器开始下载 ===")
            android.util.Log.d("DownloadManager", "videoId: $videoId")
            android.util.Log.d("DownloadManager", "cloudUrl: $cloudUrl")
            android.util.Log.d("DownloadManager", "videoTitle: $videoTitle")
            android.util.Log.d("DownloadManager", "priority: $priority")

            // 检查存储空间
            if (!storageManager.hasEnoughSpaceForDownload(estimatedSizeBytes)) {
                android.util.Log.w("DownloadManager", "存储空间不足，尝试自动清理")

                // 尝试自动清理
                val cleanupResult = storageManager.autoCleanupIfNeeded()
                if (cleanupResult.isSuccess) {
                    val result = cleanupResult.getOrNull()
                    android.util.Log.d("DownloadManager", "自动清理完成: 删除${result?.deletedCount}个文件, 释放${result?.freedSpaceBytes?.div(1024 * 1024)}MB")

                    // 重新检查空间
                    if (!storageManager.hasEnoughSpaceForDownload(estimatedSizeBytes)) {
                        return Result.failure(Exception("存储空间不足，请手动清理缓存"))
                    }
                } else {
                    return Result.failure(Exception("存储空间不足且清理失败"))
                }
            }

            // 使用智能调度器添加任务
            smartDownloadScheduler.addDownloadTask(
                videoId = videoId,
                cloudUrl = cloudUrl,
                videoTitle = videoTitle,
                priority = priority,
                requireWifi = false // 可以根据用户设置调整
            )
        } catch (e: Exception) {
            android.util.Log.e("DownloadManager", "下载启动失败", e)
            Result.failure(e)
        }
    }

    /**
     * 设置下载优先级
     */
    suspend fun setDownloadPriority(videoId: String, priority: Int): Result<Unit> {
        return smartDownloadScheduler.setTaskPriority(videoId, priority)
    }

    /**
     * 取消下载
     */
    suspend fun cancelDownload(videoId: String): Result<Unit> {
        return smartDownloadScheduler.cancelDownloadTask(videoId)
    }

    /**
     * 暂停下载
     */
    suspend fun pauseDownload(videoId: String): Result<Unit> {
        return smartDownloadScheduler.pauseDownloadTask(videoId)
    }

    /**
     * 恢复下载
     */
    suspend fun resumeDownload(videoId: String): Result<Unit> {
        return smartDownloadScheduler.resumeDownloadTask(videoId)
    }

    /**
     * 获取存储信息
     */
    suspend fun getStorageInfo(): com.shuimu.course.data.storage.StorageManager.StorageInfo {
        return storageManager.getStorageInfo()
    }

    /**
     * 清理缓存
     */
    suspend fun cleanupCache(): Result<com.shuimu.course.data.storage.StorageManager.CleanupResult> {
        return storageManager.autoCleanupIfNeeded()
    }

    /**
     * 手动LRU清理
     */
    suspend fun performLRUCleanup(targetSizeBytes: Long? = null): Result<com.shuimu.course.data.storage.StorageManager.CleanupResult> {
        return storageManager.performLRUCleanup(targetSizeBytes)
    }

    /**
     * 清理失效缓存
     */
    suspend fun cleanupInvalidCaches(): Result<com.shuimu.course.data.storage.StorageManager.CleanupResult> {
        return storageManager.cleanupInvalidCaches()
    }

    /**
     * 批量重试失败的下载
     */
    suspend fun batchRetryFailedDownloads(): Result<com.shuimu.course.data.error.DownloadErrorHandler.BatchRetryResult> {
        return downloadErrorHandler.batchRetryFailedDownloads()
    }

    /**
     * 获取错误统计信息
     */
    suspend fun getErrorStatistics(): com.shuimu.course.data.error.DownloadErrorHandler.ErrorStatistics {
        return downloadErrorHandler.getErrorStatistics()
    }

    /**
     * 重试单个失败的下载
     */
    suspend fun retryFailedDownload(videoId: String): Result<Unit> {
        return downloadErrorHandler.executeRetry(videoId) {
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo != null) {
                smartDownloadScheduler.addDownloadTask(
                    videoId = cacheInfo.videoId,
                    cloudUrl = cacheInfo.cloudUrl ?: "",
                    videoTitle = cacheInfo.title,
                    priority = cacheInfo.priority
                )
            } else {
                Result.failure(Exception("缓存信息不存在"))
            }
        }
    }

    /**
     * 重试下载
     */
    suspend fun retryDownload(videoId: String): Result<String> {
        return try {
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo == null) {
                return Result.failure(Exception("缓存信息不存在"))
            }

            // 清理失败状态
            cacheRepository.updateDownloadStatus(videoId, CacheStatus.NOT_CACHED)
            
            // 重新启动下载
            startDownload(videoId, cacheInfo.cloudUrl ?: "", cacheInfo.title)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取下载进度
     */
    fun getDownloadProgress(videoId: String): Flow<WorkInfo?> {
        return workManager.getWorkInfosByTagLiveData("video_$videoId")
            .asFlow()
            .map { workInfoList ->
                workInfoList.firstOrNull { 
                    it.state == WorkInfo.State.RUNNING || it.state == WorkInfo.State.ENQUEUED 
                }
            }
    }

    /**
     * 获取所有下载任务状态
     */
    fun getAllDownloadStatus(): Flow<List<WorkInfo>> {
        return workManager.getWorkInfosByTagLiveData("video_download")
            .asFlow()
            .map { it }
    }

    /**
     * 清理所有失败的下载任务
     */
    suspend fun clearFailedDownloads(): Result<Unit> {
        return try {
            // 取消所有失败的 Work
            workManager.cancelAllWorkByTag("video_download")
            
            // 清理数据库中的失败记录
            cacheRepository.clearFailedCaches()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 删除缓存文件和记录
     */
    suspend fun deleteCache(videoId: String): Result<Unit> {
        return try {
            // 取消下载任务（如果正在进行）
            workManager.cancelAllWorkByTag("video_$videoId")
            
            // 删除缓存记录和文件
            cacheRepository.deleteCacheInfo(videoId)
            
            // 同步到服务端
            cacheRepository.syncCacheStateToServer(videoId, false)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

// 扩展函数：LiveData 转 Flow
fun <T> androidx.lifecycle.LiveData<T>.asFlow(): Flow<T> = kotlinx.coroutines.flow.callbackFlow {
    val observer = androidx.lifecycle.Observer<T> { value ->
        trySend(value)
    }
    observeForever(observer)
    awaitClose { removeObserver(observer) }
} 