package com.shuimu.course.data.remote.dto

import com.google.gson.annotations.SerializedName

data class UserDto(
    @SerializedName("id") val id: String,
    @SerializedName("username") val username: String,
    @SerializedName("nickname") val nickname: String,
    @SerializedName("avatar_url") val avatarUrl: String?,
    @SerializedName("token") val token: String?,
    @SerializedName("learning_progress") val learningProgress: Int,
    @SerializedName("share_earnings") val shareEarnings: Double
) 