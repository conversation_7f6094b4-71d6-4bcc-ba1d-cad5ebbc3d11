package com.shuimu.course.data.local.dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Upsert
import com.shuimu.course.data.local.entities.PlayProgressEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface PlayProgressDao {
    @Upsert
    suspend fun upsertProgress(progress: PlayProgressEntity)

    @Query("SELECT * FROM play_progress WHERE video_id = :videoId")
    fun getProgressForVideo(videoId: String): Flow<PlayProgressEntity?>

    @Query("SELECT * FROM play_progress")
    fun getAllProgress(): Flow<List<PlayProgressEntity>>
} 