package com.shuimu.course.presentation.ui.components.base

import androidx.compose.animation.animateColor
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import kotlin.math.sin
import androidx.compose.ui.graphics.SolidColor

enum class BadgeStyle {
    ORIGINAL,
    MIXED
}

@Composable
fun WatchCountBadge(
    modifier: Modifier = Modifier,
    watchCount: Int,
    style: BadgeStyle = BadgeStyle.MIXED
) {
    val level = watchCountToLevel(watchCount)
    val badgeConfig = getBadgeConfig(style, level)

    val infiniteTransition = rememberInfiniteTransition(label = "badge_infinite_transition")

    val legendaryBrush = if (badgeConfig.isLegendary) {
        val gradientColors = listOf(Color(0xFFFBBF24), Color(0xFFF59E0B), Color(0xFFD97706), Color(0xFF92400E))
        val animatedGradient by infiniteTransition.animateFloat(
            initialValue = 0f,
            targetValue = 1f,
            animationSpec = infiniteRepeatable(tween(3000, easing = LinearEasing), RepeatMode.Reverse),
            label = "legendary_gradient"
        )
        Brush.horizontalGradient(
            colors = gradientColors,
            startX = -100f * animatedGradient,
            endX = 400f * animatedGradient
        )
    } else {
        null
    }

    val animatedBackgroundColor by infiniteTransition.animateColor(
        initialValue = badgeConfig.backgroundColorStart,
        targetValue = badgeConfig.backgroundColorEnd,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = badgeConfig.animationDuration, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "background_color_animation"
    )

    Box(
        modifier = modifier
            .wrapContentSize(unbounded = true)
            .graphicsLayer { clip = false }  // 徽章容器禁用裁剪
    ) {
        // Main badge content
        Box(
            modifier = Modifier
                .shadow(if (badgeConfig.isLegendary) 8.dp else 0.dp, RoundedCornerShape(10.dp), clip = false)
                .clip(RoundedCornerShape(10.dp))
                .background(
                    brush = legendaryBrush ?: SolidColor(animatedBackgroundColor)
                )
                .padding(horizontal = 8.dp, vertical = 2.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "×${watchCount}",
                color = badgeConfig.textColor,
                fontSize = if (badgeConfig.isLegendary) 12.sp else 11.sp,
                fontWeight = if (badgeConfig.isLegendary) FontWeight.Bold else FontWeight.W500
            )
        }

        // Decorative Icons
        badgeConfig.icon1?.let {
            DecorIcon(
                icon = it,
                animationType = badgeConfig.icon1Animation,
                size = badgeConfig.icon1Size,
                xOffset = badgeConfig.icon1XOffset,
                yOffset = badgeConfig.icon1YOffset
            )
        }
        badgeConfig.icon2?.let {
            DecorIcon(
                icon = it,
                animationType = badgeConfig.icon2Animation,
                size = badgeConfig.icon2Size,
                xOffset = badgeConfig.icon2XOffset,
                yOffset = badgeConfig.icon2YOffset
            )
        }
        badgeConfig.icon3?.let {
            DecorIcon(
                icon = it,
                animationType = badgeConfig.icon3Animation,
                size = badgeConfig.icon3Size,
                xOffset = badgeConfig.icon3XOffset,
                yOffset = badgeConfig.icon3YOffset
            )
        }
    }
}

enum class IconAnimationType {
    NONE,
    DIAMOND_SPARKLE_LEFT,
    DIAMOND_SPARKLE_RIGHT,
    CROWN_SPARKLE,
    GIRL_SPARKLE,
    STARFISH_TWINKLE,
    SPARKLE_TWINKLE,
}

@Composable
private fun DecorIcon(
    icon: String,
    animationType: IconAnimationType,
    size: Dp,
    xOffset: Dp,
    yOffset: Dp
) {
    if (animationType == IconAnimationType.NONE) {
        // No animation, just show the icon
        Box(modifier = Modifier.offset(x = xOffset, y = yOffset)) {
            Text(text = icon, fontSize = size.value.sp, color = Color.Black)
        }
        return
    }

    val infiniteTransition = rememberInfiniteTransition(label = "icon_transition_${icon}_${animationType}")

    val duration = getAnimationDuration(animationType)
    val easeInOut = CubicBezierEasing(0.42f, 0f, 0.58f, 1f) // Standard ease-in-out

    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = keyframes {
                durationMillis = duration
                getRotationKeyframes(animationType).forEach { (key, value) ->
                    value at (duration * key).toInt() with easeInOut
                }
            },
            repeatMode = RepeatMode.Restart
        ),
        label = "icon_rotation"
    )

    val scale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1f, // Target is not used in keyframes
        animationSpec = infiniteRepeatable(
            animation = keyframes {
                durationMillis = duration
                getScaleKeyframes(animationType).forEach { (key, value) ->
                    value at (duration * key).toInt() with easeInOut
                }
            },
            repeatMode = RepeatMode.Restart
        ),
        label = "icon_scale"
    )

    val opacity by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1f, // Target is not used in keyframes
        animationSpec = infiniteRepeatable(
            animation = keyframes {
                durationMillis = duration
                getOpacityKeyframes(animationType).forEach { (key, value) ->
                    value at (duration * key).toInt() with easeInOut
                }
            },
            repeatMode = RepeatMode.Restart
        ),
        label = "icon_opacity"
    )

    Box(
        modifier = Modifier
            .offset(x = xOffset, y = yOffset)
            .graphicsLayer(
                scaleX = scale,
                scaleY = scale,
                rotationZ = rotation,
                alpha = opacity
            )
    ) {
        Text(text = icon, fontSize = size.value.sp, color = Color.Black)
    }
}

private fun getAnimationDuration(animationType: IconAnimationType): Int {
    return when (animationType) {
        IconAnimationType.DIAMOND_SPARKLE_LEFT -> 2800
        IconAnimationType.DIAMOND_SPARKLE_RIGHT -> 2000
        IconAnimationType.CROWN_SPARKLE -> 2000
        IconAnimationType.GIRL_SPARKLE -> 2000
        IconAnimationType.STARFISH_TWINKLE -> 2800
        IconAnimationType.SPARKLE_TWINKLE -> 2200
        IconAnimationType.NONE -> 0
    }
}

private fun getRotationKeyframes(animationType: IconAnimationType): Map<Float, Float> {
    return when (animationType) {
        IconAnimationType.DIAMOND_SPARKLE_LEFT -> mapOf(
            0f to 0f, 0.2f to 72f, 0.4f to 144f, 0.6f to 216f, 0.8f to 288f, 1f to 360f
        )
        IconAnimationType.DIAMOND_SPARKLE_RIGHT -> mapOf(
            0f to 0f, 0.2f to -72f, 0.4f to -144f, 0.6f to -216f, 0.8f to -288f, 1f to -360f
        )
        IconAnimationType.CROWN_SPARKLE -> mapOf(
            0f to 0f, 0.25f to 10f, 0.5f to 0f, 0.75f to -10f, 1f to 0f
        )
        IconAnimationType.GIRL_SPARKLE -> mapOf(
            0f to 0f, 0.25f to -8f, 0.5f to 0f, 0.75f to 8f, 1f to 0f
        )
        IconAnimationType.STARFISH_TWINKLE -> mapOf(
            0f to 0f, 0.25f to 15f, 0.5f to 0f, 0.75f to -15f, 1f to 0f
        )
        IconAnimationType.SPARKLE_TWINKLE -> mapOf(
            0f to 0f, 0.25f to 90f, 0.5f to 180f, 0.75f to 270f, 1f to 360f
        )
        IconAnimationType.NONE -> emptyMap()
    }
}

private fun getScaleKeyframes(animationType: IconAnimationType): Map<Float, Float> {
    return when (animationType) {
        IconAnimationType.DIAMOND_SPARKLE_LEFT -> mapOf(
            0f to 0.8f, 0.2f to 1.1f, 0.4f to 0.9f, 0.6f to 1.2f, 0.8f to 1.0f, 1f to 0.8f
        )
        IconAnimationType.DIAMOND_SPARKLE_RIGHT -> mapOf(
             0f to 0.8f, 0.2f to 1.1f, 0.4f to 0.9f, 0.6f to 1.2f, 0.8f to 1.0f, 1f to 0.8f
        )
        IconAnimationType.CROWN_SPARKLE -> mapOf(
            0f to 0.9f, 0.25f to 1.1f, 0.5f to 1.0f, 0.75f to 1.1f, 1f to 0.9f
        )
        IconAnimationType.GIRL_SPARKLE -> mapOf(
            0f to 0.9f, 0.25f to 1.1f, 0.5f to 1.0f, 0.75f to 1.1f, 1f to 0.9f
        )
        IconAnimationType.STARFISH_TWINKLE -> mapOf(
            0f to 0.8f, 0.25f to 1.1f, 0.5f to 1.2f, 0.75f to 1.0f, 1f to 0.8f
        )
        IconAnimationType.SPARKLE_TWINKLE -> mapOf(
            0f to 0.8f, 0.25f to 1.2f, 0.5f to 1.0f, 0.75f to 1.1f, 1f to 0.8f
        )
        IconAnimationType.NONE -> emptyMap()
    }
}

private fun getOpacityKeyframes(animationType: IconAnimationType): Map<Float, Float> {
    return when (animationType) {
        IconAnimationType.DIAMOND_SPARKLE_LEFT, IconAnimationType.DIAMOND_SPARKLE_RIGHT -> mapOf(
            0f to 0.3f, 0.2f to 1f, 0.4f to 0.6f, 0.6f to 1f, 0.8f to 0.7f, 1f to 0.3f
        )
        IconAnimationType.CROWN_SPARKLE, IconAnimationType.GIRL_SPARKLE -> mapOf(
            0f to 0.9f, 0.25f to 1f, 0.5f to 0.95f, 0.75f to 1f, 1f to 0.9f
        )
        IconAnimationType.STARFISH_TWINKLE -> mapOf(
            0f to 0.4f, 0.25f to 0.9f, 0.5f to 1f, 0.75f to 0.8f, 1f to 0.4f
        )
        IconAnimationType.SPARKLE_TWINKLE -> mapOf(
            0f to 0.4f, 0.25f to 1f, 0.5f to 0.7f, 0.75f to 1f, 1f to 0.4f
        )
        IconAnimationType.NONE -> emptyMap()
    }
}

private data class BadgeConfig(
    val icon1: String? = null,
    val icon1Animation: IconAnimationType = IconAnimationType.NONE,
    val icon1Size: Dp = 0.dp,
    val icon1XOffset: Dp = 0.dp,
    val icon1YOffset: Dp = 0.dp,

    val icon2: String? = null,
    val icon2Animation: IconAnimationType = IconAnimationType.NONE,
    val icon2Size: Dp = 0.dp,
    val icon2XOffset: Dp = 0.dp,
    val icon2YOffset: Dp = 0.dp,

    val icon3: String? = null,
    val icon3Animation: IconAnimationType = IconAnimationType.NONE,
    val icon3Size: Dp = 0.dp,
    val icon3XOffset: Dp = 0.dp,
    val icon3YOffset: Dp = 0.dp,

    val backgroundColorStart: Color,
    val backgroundColorEnd: Color,
    val textColor: Color,
    val isLegendary: Boolean = false,
    val animationDuration: Int = 1700
)

private fun watchCountToLevel(watchCount: Int): Int {
    return when {
        watchCount == 0 -> 0
        watchCount in 1..9 -> watchCount
        else -> 10 // 10+
    }
}

private fun getBadgeConfig(style: BadgeStyle, level: Int): BadgeConfig {
    val greenText = Color(0xFF166534)
    val whiteText = Color.White

    return if (style == BadgeStyle.ORIGINAL) {
        when (level) {
            0 -> BadgeConfig(backgroundColorStart = Color(0xFFF7FEF8), backgroundColorEnd = Color(0xFFF7FEF8), textColor = greenText, animationDuration = 5000)
            1 -> BadgeConfig(backgroundColorStart = Color(0xFFE6F9EA), backgroundColorEnd = Color(0xFFDCFCE7), textColor = greenText, animationDuration = 1700)
            2 -> BadgeConfig(backgroundColorStart = Color(0xFFD1F2DB), backgroundColorEnd = Color(0xFFBBF7D0), textColor = greenText, animationDuration = 1500)
            3 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 8.dp, icon1XOffset = (-6).dp, icon1YOffset = (-6).dp, backgroundColorStart = Color(0xFFB8EBC5), backgroundColorEnd = Color(0xFF9EE4AF), textColor = greenText, animationDuration = 3100)
            4 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 11.dp, icon1XOffset = (-9).dp, icon1YOffset = (-9).dp, backgroundColorStart = Color(0xFF9EE4AF), backgroundColorEnd = Color(0xFF84DD99), textColor = greenText, animationDuration = 2900)
            5 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 14.dp, icon1XOffset = (-12).dp, icon1YOffset = (-12).dp, backgroundColorStart = Color(0xFF84DD99), backgroundColorEnd = Color(0xFF6AD683), textColor = whiteText, animationDuration = 2700)
            6 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 8.dp, icon1XOffset = (-6).dp, icon1YOffset = (-6).dp, icon2 = "💎", icon2Animation = IconAnimationType.DIAMOND_SPARKLE_RIGHT, icon2Size = 8.dp, icon2XOffset = 28.dp, icon2YOffset = (-6).dp, backgroundColorStart = Color(0xFF6AD683), backgroundColorEnd = Color(0xFF50CF6D), textColor = whiteText, animationDuration = 2500)
            7 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 11.dp, icon1XOffset = (-9).dp, icon1YOffset = (-9).dp, icon2 = "💎", icon2Animation = IconAnimationType.DIAMOND_SPARKLE_RIGHT, icon2Size = 11.dp, icon2XOffset = 34.dp, icon2YOffset = (-9).dp, backgroundColorStart = Color(0xFF50CF6D), backgroundColorEnd = Color(0xFF36C857), textColor = whiteText, animationDuration = 2400)
            8 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 14.dp, icon1XOffset = (-12).dp, icon1YOffset = (-12).dp, icon2 = "💎", icon2Animation = IconAnimationType.DIAMOND_SPARKLE_RIGHT, icon2Size = 14.dp, icon2XOffset = 38.dp, icon2YOffset = (-12).dp, backgroundColorStart = Color(0xFF36C857), backgroundColorEnd = Color(0xFF2CC157), textColor = whiteText, animationDuration = 2300)
            9 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 17.dp, icon1XOffset = (-15).dp, icon1YOffset = (-15).dp, icon2 = "💎", icon2Animation = IconAnimationType.DIAMOND_SPARKLE_RIGHT, icon2Size = 17.dp, icon2XOffset = 42.dp, icon2YOffset = (-15).dp, backgroundColorStart = Color(0xFF2CC157), backgroundColorEnd = Color(0xFF22C55E), textColor = whiteText, animationDuration = 2200)
            else -> BadgeConfig(icon1 = "👑", icon1Animation = IconAnimationType.CROWN_SPARKLE, icon1Size = 20.dp, icon1XOffset = (-12).dp, icon1YOffset = (-20).dp, icon2 = "👧", icon2Animation = IconAnimationType.GIRL_SPARKLE, icon2Size = 18.dp, icon2XOffset = 30.dp, icon2YOffset = (-20).dp, backgroundColorStart = Color.Transparent, backgroundColorEnd = Color.Transparent, textColor = whiteText, isLegendary = true)
        }
    } else { // MIXED
        when (level) {
            0 -> BadgeConfig(backgroundColorStart = Color(0xFFF7FEF8), backgroundColorEnd = Color(0xFFF7FEF8), textColor = greenText, animationDuration = 5000)
            1 -> BadgeConfig(icon1 = "⭐", icon1Animation = IconAnimationType.STARFISH_TWINKLE, icon1Size = 11.dp, icon1XOffset = (-9).dp, icon1YOffset = (-9).dp, backgroundColorStart = Color(0xFFF0FDF4), backgroundColorEnd = Color(0xFFDCFCE7), textColor = greenText, animationDuration = 2500)
            2 -> BadgeConfig(icon1 = "⭐", icon1Animation = IconAnimationType.STARFISH_TWINKLE, icon1Size = 11.dp, icon1XOffset = (-10).dp, icon1YOffset = (-7).dp, icon2 = "⭐", icon2Animation = IconAnimationType.STARFISH_TWINKLE, icon2Size = 11.dp, icon2XOffset = 32.dp, icon2YOffset = (-7).dp, backgroundColorStart = Color(0xFFE6F9EA), backgroundColorEnd = Color(0xFFDCFCE7), textColor = greenText, animationDuration = 2300)
            3 -> BadgeConfig(icon1 = "✨", icon1Animation = IconAnimationType.SPARKLE_TWINKLE, icon1Size = 16.dp, icon1XOffset = (-14).dp, icon1YOffset = (-14).dp, backgroundColorStart = Color(0xFFB8EBC5), backgroundColorEnd = Color(0xFF9EE4AF), textColor = greenText, animationDuration = 2000)
            4 -> BadgeConfig(icon1 = "✨", icon1Animation = IconAnimationType.SPARKLE_TWINKLE, icon1Size = 16.dp, icon1XOffset = (-15).dp, icon1YOffset = (-12).dp, icon2 = "✨", icon2Animation = IconAnimationType.SPARKLE_TWINKLE, icon2Size = 16.dp, icon2XOffset = 38.dp, icon2YOffset = (-12).dp, backgroundColorStart = Color(0xFF9EE4AF), backgroundColorEnd = Color(0xFF84DD99), textColor = greenText, animationDuration = 1800)
            5 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 16.dp, icon1XOffset = (-14).dp, icon1YOffset = (-14).dp, backgroundColorStart = Color(0xFF84DD99), backgroundColorEnd = Color(0xFF6AD683), textColor = whiteText, animationDuration = 2200)
            6 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 16.dp, icon1XOffset = (-15).dp, icon1YOffset = (-12).dp, icon2 = "💎", icon2Animation = IconAnimationType.DIAMOND_SPARKLE_RIGHT, icon2Size = 16.dp, icon2XOffset = 38.dp, icon2YOffset = (-12).dp, backgroundColorStart = Color(0xFF6AD683), backgroundColorEnd = Color(0xFF50CF6D), textColor = whiteText, animationDuration = 2000)
            7 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 16.dp, icon1XOffset = (-14).dp, icon1YOffset = (-14).dp, backgroundColorStart = Color(0xFF50CF6D), backgroundColorEnd = Color(0xFF36C857), textColor = whiteText, animationDuration = 1800)
            8 -> BadgeConfig(icon1 = "💎", icon1Animation = IconAnimationType.DIAMOND_SPARKLE_LEFT, icon1Size = 16.dp, icon1XOffset = (-15).dp, icon1YOffset = (-12).dp, icon2 = "💎", icon2Animation = IconAnimationType.DIAMOND_SPARKLE_RIGHT, icon2Size = 16.dp, icon2XOffset = 38.dp, icon2YOffset = (-12).dp, backgroundColorStart = Color(0xFF36C857), backgroundColorEnd = Color(0xFF2CC157), textColor = whiteText, animationDuration = 1600)
            9 -> BadgeConfig(icon1 = "👑", icon1Animation = IconAnimationType.CROWN_SPARKLE, icon1Size = 20.dp, icon1XOffset = (-16).dp, icon1YOffset = (-19).dp, backgroundColorStart = Color(0xFF2CC157), backgroundColorEnd = Color(0xFF22C55E), textColor = whiteText, animationDuration = 800)
            else -> BadgeConfig(icon1 = "👑", icon1Animation = IconAnimationType.CROWN_SPARKLE, icon1Size = 20.dp, icon1XOffset = (-12).dp, icon1YOffset = (-20).dp, icon2 = "👧", icon2Animation = IconAnimationType.GIRL_SPARKLE, icon2Size = 18.dp, icon2XOffset = 30.dp, icon2YOffset = (-20).dp, backgroundColorStart = Color.Transparent, backgroundColorEnd = Color.Transparent, textColor = whiteText, isLegendary = true)
        }
    }
}

@Preview(showBackground = true, widthDp = 300)
@Composable
fun WatchCountBadgeOriginalPreview() {
    ShuimuCourseTheme {
        Column(verticalArrangement = Arrangement.spacedBy(24.dp), modifier = Modifier.padding(16.dp)) {
            Text("Original Style", fontWeight = FontWeight.Bold)
            WatchCountBadge(watchCount = 0, style = BadgeStyle.ORIGINAL)    // Level 0
            WatchCountBadge(watchCount = 1, style = BadgeStyle.ORIGINAL)    // Level 1
            WatchCountBadge(watchCount = 2, style = BadgeStyle.ORIGINAL)    // Level 2
            WatchCountBadge(watchCount = 3, style = BadgeStyle.ORIGINAL)    // Level 3
            WatchCountBadge(watchCount = 4, style = BadgeStyle.ORIGINAL)    // Level 4
            WatchCountBadge(watchCount = 5, style = BadgeStyle.ORIGINAL)    // Level 5
            WatchCountBadge(watchCount = 6, style = BadgeStyle.ORIGINAL)    // Level 6
            WatchCountBadge(watchCount = 7, style = BadgeStyle.ORIGINAL)    // Level 7
            WatchCountBadge(watchCount = 8, style = BadgeStyle.ORIGINAL)    // Level 8
            WatchCountBadge(watchCount = 9, style = BadgeStyle.ORIGINAL)    // Level 9
            WatchCountBadge(watchCount = 10, style = BadgeStyle.ORIGINAL)   // Level 10
            WatchCountBadge(watchCount = 100, style = BadgeStyle.ORIGINAL) // Level 10
        }
    }
}

@Preview(showBackground = true, widthDp = 300)
@Composable
fun WatchCountBadgeMixedPreview() {
    ShuimuCourseTheme {
        Column(verticalArrangement = Arrangement.spacedBy(24.dp), modifier = Modifier.padding(16.dp)) {
            Text("Mixed Style", fontWeight = FontWeight.Bold)
            WatchCountBadge(watchCount = 0, style = BadgeStyle.MIXED)   // level 0
            WatchCountBadge(watchCount = 1, style = BadgeStyle.MIXED)   // level 1
            WatchCountBadge(watchCount = 2, style = BadgeStyle.MIXED)   // level 2
            WatchCountBadge(watchCount = 3, style = BadgeStyle.MIXED)   // level 3
            WatchCountBadge(watchCount = 4, style = BadgeStyle.MIXED)   // level 4
            WatchCountBadge(watchCount = 5, style = BadgeStyle.MIXED)   // level 5
            WatchCountBadge(watchCount = 6, style = BadgeStyle.MIXED)   // level 6
            WatchCountBadge(watchCount = 7, style = BadgeStyle.MIXED)   // level 7
            WatchCountBadge(watchCount = 8, style = BadgeStyle.MIXED)   // level 8
            WatchCountBadge(watchCount = 9, style = BadgeStyle.MIXED)   // level 9
            WatchCountBadge(watchCount = 10, style = BadgeStyle.MIXED)  // level 10
            WatchCountBadge(watchCount = 100, style = BadgeStyle.MIXED) // level 10
        }
    }
}