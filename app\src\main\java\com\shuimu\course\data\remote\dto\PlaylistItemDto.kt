package com.shuimu.course.data.remote.dto

import com.google.gson.annotations.SerializedName
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus

data class PlaylistItemDto(
    @SerializedName("id") val id: String,
    @SerializedName("title") val title: String,
    @SerializedName("duration") val duration: Int, // 视频总时长，单位：秒
    @SerializedName("description") val description: String = "",
    @SerializedName("cloudUrl") val cloudUrl: String,
    @SerializedName("thumbnailUrl") val thumbnailUrl: String? = null,
    @SerializedName("order") val order: Int = 0,
    
    // Video specific fields
    @SerializedName("categoryId") val categoryId: String? = null,
    @SerializedName("watch_count") val watchCount: Int? = null,
    @SerializedName("cache_status") val cacheStatus: String? = null,
    @SerializedName("progress") val progress: Float = 0.0f,
    
    // Playlist specific fields  
    @SerializedName("playlistId") val playlistId: String? = null,
    @SerializedName("playCount") val playCount: Long? = null
) {
    fun toVideo(categoryId: String): Video {
        return Video(
            id = id,
            title = title,
            duration = duration,
            description = description,
            categoryId = categoryId,
            watchCount = watchCount,
            playCount = playCount,
            cloudUrl = cloudUrl,
            localPath = null,
            cacheStatus = CacheStatus.NOT_CACHED,
            progress = progress,
            isPurchasable = false
        )
    }
} 