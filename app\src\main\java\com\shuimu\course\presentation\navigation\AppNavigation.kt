package com.shuimu.course.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.shuimu.course.presentation.ui.screens.CacheManagerScreen
import com.shuimu.course.presentation.ui.screens.HomeScreen
import com.shuimu.course.presentation.ui.screens.LoginScreen
import com.shuimu.course.presentation.ui.screens.PaymentScreen
import com.shuimu.course.presentation.ui.screens.SplashScreen
import com.shuimu.course.presentation.ui.screens.VideoPlayerScreen
import com.shuimu.course.presentation.ui.screens.orders.OrdersScreen
import com.shuimu.course.presentation.ui.screens.profile.ProfileScreen
import com.shuimu.course.presentation.ui.screens.share.ShareEarningsScreen
import com.shuimu.course.presentation.ui.screens.share.ShareMaterialsScreen
import com.shuimu.course.presentation.ui.screens.share.ShareRankingScreen

// Placeholder screens
@Composable
fun SeriesDetailScreen(seriesId: String) { /* TODO */ }

@Composable
fun AppNavigation() {
    val navController = rememberNavController()
    NavHost(navController = navController, startDestination = Routes.SPLASH) {
        // 🔥 新增：启动页 - 仿美团模式
        composable(Routes.SPLASH) {
            SplashScreen(
                onNavigateToHome = {
                    navController.navigate(Routes.HOME) {
                        popUpTo(Routes.SPLASH) { inclusive = true }
                    }
                }
            )
        }

        composable(Routes.HOME) {
            HomeScreen(navController = navController)
        }
        composable(
            route = "${Routes.SERIES_DETAIL}/{seriesId}",
            arguments = listOf(navArgument("seriesId") { type = NavType.StringType })
        ) { backStackEntry ->
            val seriesId = backStackEntry.arguments?.getString("seriesId") ?: ""
            SeriesDetailScreen(seriesId = seriesId)
        }
        composable(
            route = "${Routes.VIDEO_PLAYER}/{videoId}",
            arguments = listOf(navArgument("videoId") { type = NavType.StringType })
        ) {
            @OptIn(androidx.media3.common.util.UnstableApi::class)
            VideoPlayerScreen()
        }
        composable(Routes.LOGIN) {
            LoginScreen(
                onLoginSuccess = {
                    navController.navigate(Routes.HOME) {
                        popUpTo(Routes.LOGIN) { inclusive = true }
                    }
                }
            )
        }
        composable(Routes.PROFILE) {
            ProfileScreen(
                onNavigateTo = { route -> navController.navigate(route) }
            )
        }
        composable(Routes.ORDERS) {
            OrdersScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        composable(Routes.CACHE_MANAGER) {
            CacheManagerScreen(navController = navController)
        }
        composable(Routes.SHARE_EARNINGS) {
            ShareEarningsScreen()
        }
        composable(Routes.SHARE_RANKING) {
            ShareRankingScreen()
        }
        composable(Routes.SHARE_MATERIALS) {
            ShareMaterialsScreen()
        }
        composable(
            route = Routes.PAYMENT,
            arguments = listOf(
                navArgument("itemId") { type = NavType.StringType },
                navArgument("itemType") { type = NavType.StringType },
                navArgument("title") { type = NavType.StringType },
                navArgument("price") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val title = backStackEntry.arguments?.getString("title") ?: ""
            val price = backStackEntry.arguments?.getString("price") ?: ""
            PaymentScreen(
                title = title,
                price = price,
                onPaymentSuccess = { navController.popBackStack() }
            )
        }
    }
} 