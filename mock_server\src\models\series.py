from pydantic import BaseModel
from typing import List, Optional
from .category import Category

class Series(BaseModel):
    id: str
    title: str
    icon: Optional[str] = None
    iconColor: Optional[str] = None
    price: int  # 价格，单位：分（动态计算，等于未购买分类价格之和）
    isFree: bool
    isPurchased: bool = False
    isPackage: bool = False
    defaultExpanded: bool = False
    totalVideos: int
    categories: List[Category]
