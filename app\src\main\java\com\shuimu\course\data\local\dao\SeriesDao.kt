package com.shuimu.course.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.shuimu.course.data.local.entities.SeriesEntity
import com.shuimu.course.data.local.entities.CategoryEntity
import com.shuimu.course.data.local.entities.VideoEntity
import com.shuimu.course.data.local.relations.SeriesWithCategories
import kotlinx.coroutines.flow.Flow

@Dao
interface SeriesDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSeries(series: List<SeriesEntity>)

    @Transaction
    @Query("SELECT * FROM series")
    fun getSeriesWithCategories(): Flow<List<SeriesWithCategories>>

    @Query("DELETE FROM series")
    suspend fun clearAll()
    
    // 🔥 事务方法：原子性地插入所有层级数据
    @Transaction
    suspend fun insertAllData(
        seriesEntities: List<SeriesEntity>,
        categoryEntities: List<CategoryEntity>, 
        videoEntities: List<VideoEntity>,
        categoryDao: CategoryDao,
        videoDao: VideoDao
    ) {
        // 按顺序插入，确保外键约束
        insertSeries(seriesEntities)
        categoryDao.insertCategories(categoryEntities)
        videoDao.insertVideos(videoEntities)
    }
} 