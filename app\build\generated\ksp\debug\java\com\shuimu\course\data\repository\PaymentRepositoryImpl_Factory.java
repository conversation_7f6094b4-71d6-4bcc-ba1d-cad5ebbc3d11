package com.shuimu.course.data.repository;

import com.shuimu.course.data.remote.api.PaymentApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class PaymentRepositoryImpl_Factory implements Factory<PaymentRepositoryImpl> {
  private final Provider<PaymentApi> paymentApiProvider;

  public PaymentRepositoryImpl_Factory(Provider<PaymentApi> paymentApiProvider) {
    this.paymentApiProvider = paymentApiProvider;
  }

  @Override
  public PaymentRepositoryImpl get() {
    return newInstance(paymentApiProvider.get());
  }

  public static PaymentRepositoryImpl_Factory create(Provider<PaymentApi> paymentApiProvider) {
    return new PaymentRepositoryImpl_Factory(paymentApiProvider);
  }

  public static PaymentRepositoryImpl newInstance(PaymentApi paymentApi) {
    return new PaymentRepositoryImpl(paymentApi);
  }
}
