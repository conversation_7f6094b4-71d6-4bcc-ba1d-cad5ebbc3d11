package com.shuimu.course.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.EntityUpsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.shuimu.course.data.local.entities.CacheInfoEntity;
import com.shuimu.course.data.local.entities.CacheStatus;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalArgumentException;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CacheInfoDao_Impl implements CacheInfoDao {
  private final RoomDatabase __db;

  private final SharedSQLiteStatement __preparedStmtOfUpdateDownloadProgress;

  private final SharedSQLiteStatement __preparedStmtOfUpdateDownloadStatus;

  private final SharedSQLiteStatement __preparedStmtOfDeleteCacheInfo;

  private final SharedSQLiteStatement __preparedStmtOfClearFailedCaches;

  private final EntityUpsertionAdapter<CacheInfoEntity> __upsertionAdapterOfCacheInfoEntity;

  public CacheInfoDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__preparedStmtOfUpdateDownloadProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE cache_info SET status = ?, progress = ?, updated_at = ? WHERE video_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateDownloadStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE cache_info SET status = ?, error_message = ?, updated_at = ? WHERE video_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteCacheInfo = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cache_info WHERE video_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearFailedCaches = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM cache_info WHERE status = 'FAILED'";
        return _query;
      }
    };
    this.__upsertionAdapterOfCacheInfoEntity = new EntityUpsertionAdapter<CacheInfoEntity>(new EntityInsertionAdapter<CacheInfoEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT INTO `cache_info` (`video_id`,`local_path`,`downloaded_bytes`,`total_bytes`,`status`,`progress`,`cloud_url`,`updated_at`,`error_message`) VALUES (?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CacheInfoEntity entity) {
        statement.bindString(1, entity.getVideoId());
        if (entity.getLocalPath() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getLocalPath());
        }
        statement.bindLong(3, entity.getDownloadedBytes());
        statement.bindLong(4, entity.getTotalBytes());
        statement.bindString(5, __CacheStatus_enumToString(entity.getStatus()));
        statement.bindLong(6, entity.getProgress());
        if (entity.getCloudUrl() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getCloudUrl());
        }
        statement.bindLong(8, entity.getUpdatedAt());
        if (entity.getErrorMessage() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getErrorMessage());
        }
      }
    }, new EntityDeletionOrUpdateAdapter<CacheInfoEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE `cache_info` SET `video_id` = ?,`local_path` = ?,`downloaded_bytes` = ?,`total_bytes` = ?,`status` = ?,`progress` = ?,`cloud_url` = ?,`updated_at` = ?,`error_message` = ? WHERE `video_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CacheInfoEntity entity) {
        statement.bindString(1, entity.getVideoId());
        if (entity.getLocalPath() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getLocalPath());
        }
        statement.bindLong(3, entity.getDownloadedBytes());
        statement.bindLong(4, entity.getTotalBytes());
        statement.bindString(5, __CacheStatus_enumToString(entity.getStatus()));
        statement.bindLong(6, entity.getProgress());
        if (entity.getCloudUrl() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getCloudUrl());
        }
        statement.bindLong(8, entity.getUpdatedAt());
        if (entity.getErrorMessage() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getErrorMessage());
        }
        statement.bindString(10, entity.getVideoId());
      }
    });
  }

  @Override
  public Object updateDownloadProgress(final String videoId, final CacheStatus status,
      final int progress, final long updatedAt, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateDownloadProgress.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, __CacheStatus_enumToString(status));
        _argIndex = 2;
        _stmt.bindLong(_argIndex, progress);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, updatedAt);
        _argIndex = 4;
        _stmt.bindString(_argIndex, videoId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateDownloadProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDownloadStatus(final String videoId, final CacheStatus status,
      final String errorMessage, final long updatedAt,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateDownloadStatus.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, __CacheStatus_enumToString(status));
        _argIndex = 2;
        if (errorMessage == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, errorMessage);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, updatedAt);
        _argIndex = 4;
        _stmt.bindString(_argIndex, videoId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateDownloadStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCacheInfo(final String videoId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteCacheInfo.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, videoId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteCacheInfo.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearFailedCaches(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearFailedCaches.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearFailedCaches.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object upsertCacheInfo(final CacheInfoEntity info,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __upsertionAdapterOfCacheInfoEntity.upsert(info);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<CacheInfoEntity> getCacheInfoForVideo(final String videoId) {
    final String _sql = "SELECT * FROM cache_info WHERE video_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, videoId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache_info"}, new Callable<CacheInfoEntity>() {
      @Override
      @Nullable
      public CacheInfoEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "local_path");
          final int _cursorIndexOfDownloadedBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "downloaded_bytes");
          final int _cursorIndexOfTotalBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "total_bytes");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloud_url");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "error_message");
          final CacheInfoEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpLocalPath;
            if (_cursor.isNull(_cursorIndexOfLocalPath)) {
              _tmpLocalPath = null;
            } else {
              _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            }
            final long _tmpDownloadedBytes;
            _tmpDownloadedBytes = _cursor.getLong(_cursorIndexOfDownloadedBytes);
            final long _tmpTotalBytes;
            _tmpTotalBytes = _cursor.getLong(_cursorIndexOfTotalBytes);
            final CacheStatus _tmpStatus;
            _tmpStatus = __CacheStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            _result = new CacheInfoEntity(_tmpVideoId,_tmpLocalPath,_tmpDownloadedBytes,_tmpTotalBytes,_tmpStatus,_tmpProgress,_tmpCloudUrl,_tmpUpdatedAt,_tmpErrorMessage);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCacheInfoSync(final String videoId,
      final Continuation<? super CacheInfoEntity> $completion) {
    final String _sql = "SELECT * FROM cache_info WHERE video_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, videoId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CacheInfoEntity>() {
      @Override
      @Nullable
      public CacheInfoEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "local_path");
          final int _cursorIndexOfDownloadedBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "downloaded_bytes");
          final int _cursorIndexOfTotalBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "total_bytes");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloud_url");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "error_message");
          final CacheInfoEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpLocalPath;
            if (_cursor.isNull(_cursorIndexOfLocalPath)) {
              _tmpLocalPath = null;
            } else {
              _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            }
            final long _tmpDownloadedBytes;
            _tmpDownloadedBytes = _cursor.getLong(_cursorIndexOfDownloadedBytes);
            final long _tmpTotalBytes;
            _tmpTotalBytes = _cursor.getLong(_cursorIndexOfTotalBytes);
            final CacheStatus _tmpStatus;
            _tmpStatus = __CacheStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            _result = new CacheInfoEntity(_tmpVideoId,_tmpLocalPath,_tmpDownloadedBytes,_tmpTotalBytes,_tmpStatus,_tmpProgress,_tmpCloudUrl,_tmpUpdatedAt,_tmpErrorMessage);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CacheInfoEntity>> getCachesByStatus(final CacheStatus status) {
    final String _sql = "SELECT * FROM cache_info WHERE status = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, __CacheStatus_enumToString(status));
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache_info"}, new Callable<List<CacheInfoEntity>>() {
      @Override
      @NonNull
      public List<CacheInfoEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "local_path");
          final int _cursorIndexOfDownloadedBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "downloaded_bytes");
          final int _cursorIndexOfTotalBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "total_bytes");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloud_url");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "error_message");
          final List<CacheInfoEntity> _result = new ArrayList<CacheInfoEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheInfoEntity _item;
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpLocalPath;
            if (_cursor.isNull(_cursorIndexOfLocalPath)) {
              _tmpLocalPath = null;
            } else {
              _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            }
            final long _tmpDownloadedBytes;
            _tmpDownloadedBytes = _cursor.getLong(_cursorIndexOfDownloadedBytes);
            final long _tmpTotalBytes;
            _tmpTotalBytes = _cursor.getLong(_cursorIndexOfTotalBytes);
            final CacheStatus _tmpStatus;
            _tmpStatus = __CacheStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            _item = new CacheInfoEntity(_tmpVideoId,_tmpLocalPath,_tmpDownloadedBytes,_tmpTotalBytes,_tmpStatus,_tmpProgress,_tmpCloudUrl,_tmpUpdatedAt,_tmpErrorMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CacheInfoEntity>> getAllCompletedCaches() {
    final String _sql = "SELECT * FROM cache_info WHERE status = 'DOWNLOADED'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache_info"}, new Callable<List<CacheInfoEntity>>() {
      @Override
      @NonNull
      public List<CacheInfoEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "local_path");
          final int _cursorIndexOfDownloadedBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "downloaded_bytes");
          final int _cursorIndexOfTotalBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "total_bytes");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloud_url");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "error_message");
          final List<CacheInfoEntity> _result = new ArrayList<CacheInfoEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheInfoEntity _item;
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpLocalPath;
            if (_cursor.isNull(_cursorIndexOfLocalPath)) {
              _tmpLocalPath = null;
            } else {
              _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            }
            final long _tmpDownloadedBytes;
            _tmpDownloadedBytes = _cursor.getLong(_cursorIndexOfDownloadedBytes);
            final long _tmpTotalBytes;
            _tmpTotalBytes = _cursor.getLong(_cursorIndexOfTotalBytes);
            final CacheStatus _tmpStatus;
            _tmpStatus = __CacheStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            _item = new CacheInfoEntity(_tmpVideoId,_tmpLocalPath,_tmpDownloadedBytes,_tmpTotalBytes,_tmpStatus,_tmpProgress,_tmpCloudUrl,_tmpUpdatedAt,_tmpErrorMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CacheInfoEntity>> getAllDownloadingCaches() {
    final String _sql = "SELECT * FROM cache_info WHERE status = 'DOWNLOADING'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache_info"}, new Callable<List<CacheInfoEntity>>() {
      @Override
      @NonNull
      public List<CacheInfoEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "local_path");
          final int _cursorIndexOfDownloadedBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "downloaded_bytes");
          final int _cursorIndexOfTotalBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "total_bytes");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloud_url");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "error_message");
          final List<CacheInfoEntity> _result = new ArrayList<CacheInfoEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheInfoEntity _item;
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpLocalPath;
            if (_cursor.isNull(_cursorIndexOfLocalPath)) {
              _tmpLocalPath = null;
            } else {
              _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            }
            final long _tmpDownloadedBytes;
            _tmpDownloadedBytes = _cursor.getLong(_cursorIndexOfDownloadedBytes);
            final long _tmpTotalBytes;
            _tmpTotalBytes = _cursor.getLong(_cursorIndexOfTotalBytes);
            final CacheStatus _tmpStatus;
            _tmpStatus = __CacheStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            _item = new CacheInfoEntity(_tmpVideoId,_tmpLocalPath,_tmpDownloadedBytes,_tmpTotalBytes,_tmpStatus,_tmpProgress,_tmpCloudUrl,_tmpUpdatedAt,_tmpErrorMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CacheInfoEntity>> getAllPendingCaches() {
    final String _sql = "SELECT * FROM cache_info WHERE status = 'PENDING'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache_info"}, new Callable<List<CacheInfoEntity>>() {
      @Override
      @NonNull
      public List<CacheInfoEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "local_path");
          final int _cursorIndexOfDownloadedBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "downloaded_bytes");
          final int _cursorIndexOfTotalBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "total_bytes");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloud_url");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "error_message");
          final List<CacheInfoEntity> _result = new ArrayList<CacheInfoEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheInfoEntity _item;
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpLocalPath;
            if (_cursor.isNull(_cursorIndexOfLocalPath)) {
              _tmpLocalPath = null;
            } else {
              _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            }
            final long _tmpDownloadedBytes;
            _tmpDownloadedBytes = _cursor.getLong(_cursorIndexOfDownloadedBytes);
            final long _tmpTotalBytes;
            _tmpTotalBytes = _cursor.getLong(_cursorIndexOfTotalBytes);
            final CacheStatus _tmpStatus;
            _tmpStatus = __CacheStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            _item = new CacheInfoEntity(_tmpVideoId,_tmpLocalPath,_tmpDownloadedBytes,_tmpTotalBytes,_tmpStatus,_tmpProgress,_tmpCloudUrl,_tmpUpdatedAt,_tmpErrorMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getDownloadingCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM cache_info WHERE status = 'DOWNLOADING'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CacheInfoEntity>> getAllCacheInfo() {
    final String _sql = "SELECT * FROM cache_info";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"cache_info"}, new Callable<List<CacheInfoEntity>>() {
      @Override
      @NonNull
      public List<CacheInfoEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfLocalPath = CursorUtil.getColumnIndexOrThrow(_cursor, "local_path");
          final int _cursorIndexOfDownloadedBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "downloaded_bytes");
          final int _cursorIndexOfTotalBytes = CursorUtil.getColumnIndexOrThrow(_cursor, "total_bytes");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfCloudUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "cloud_url");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "error_message");
          final List<CacheInfoEntity> _result = new ArrayList<CacheInfoEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CacheInfoEntity _item;
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final String _tmpLocalPath;
            if (_cursor.isNull(_cursorIndexOfLocalPath)) {
              _tmpLocalPath = null;
            } else {
              _tmpLocalPath = _cursor.getString(_cursorIndexOfLocalPath);
            }
            final long _tmpDownloadedBytes;
            _tmpDownloadedBytes = _cursor.getLong(_cursorIndexOfDownloadedBytes);
            final long _tmpTotalBytes;
            _tmpTotalBytes = _cursor.getLong(_cursorIndexOfTotalBytes);
            final CacheStatus _tmpStatus;
            _tmpStatus = __CacheStatus_stringToEnum(_cursor.getString(_cursorIndexOfStatus));
            final int _tmpProgress;
            _tmpProgress = _cursor.getInt(_cursorIndexOfProgress);
            final String _tmpCloudUrl;
            if (_cursor.isNull(_cursorIndexOfCloudUrl)) {
              _tmpCloudUrl = null;
            } else {
              _tmpCloudUrl = _cursor.getString(_cursorIndexOfCloudUrl);
            }
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            _item = new CacheInfoEntity(_tmpVideoId,_tmpLocalPath,_tmpDownloadedBytes,_tmpTotalBytes,_tmpStatus,_tmpProgress,_tmpCloudUrl,_tmpUpdatedAt,_tmpErrorMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private String __CacheStatus_enumToString(@NonNull final CacheStatus _value) {
    switch (_value) {
      case NOT_CACHED: return "NOT_CACHED";
      case WAITING_NETWORK: return "WAITING_NETWORK";
      case DOWNLOADING: return "DOWNLOADING";
      case DOWNLOADED: return "DOWNLOADED";
      case PAUSED: return "PAUSED";
      case FAILED: return "FAILED";
      case PENDING: return "PENDING";
      default: throw new IllegalArgumentException("Can't convert enum to string, unknown enum value: " + _value);
    }
  }

  private CacheStatus __CacheStatus_stringToEnum(@NonNull final String _value) {
    switch (_value) {
      case "NOT_CACHED": return CacheStatus.NOT_CACHED;
      case "WAITING_NETWORK": return CacheStatus.WAITING_NETWORK;
      case "DOWNLOADING": return CacheStatus.DOWNLOADING;
      case "DOWNLOADED": return CacheStatus.DOWNLOADED;
      case "PAUSED": return CacheStatus.PAUSED;
      case "FAILED": return CacheStatus.FAILED;
      case "PENDING": return CacheStatus.PENDING;
      default: throw new IllegalArgumentException("Can't convert value to enum, unknown value: " + _value);
    }
  }
}
