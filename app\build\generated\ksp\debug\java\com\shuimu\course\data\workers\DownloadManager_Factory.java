package com.shuimu.course.data.workers;

import android.content.Context;
import com.shuimu.course.data.error.DownloadErrorHandler;
import com.shuimu.course.data.storage.StorageManager;
import com.shuimu.course.domain.repository.CacheRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DownloadManager_Factory implements Factory<DownloadManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CacheRepository> cacheRepositoryProvider;

  private final Provider<SmartDownloadScheduler> smartDownloadSchedulerProvider;

  private final Provider<StorageManager> storageManagerProvider;

  private final Provider<DownloadErrorHandler> downloadErrorHandlerProvider;

  public DownloadManager_Factory(Provider<Context> contextProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<SmartDownloadScheduler> smartDownloadSchedulerProvider,
      Provider<StorageManager> storageManagerProvider,
      Provider<DownloadErrorHandler> downloadErrorHandlerProvider) {
    this.contextProvider = contextProvider;
    this.cacheRepositoryProvider = cacheRepositoryProvider;
    this.smartDownloadSchedulerProvider = smartDownloadSchedulerProvider;
    this.storageManagerProvider = storageManagerProvider;
    this.downloadErrorHandlerProvider = downloadErrorHandlerProvider;
  }

  @Override
  public DownloadManager get() {
    return newInstance(contextProvider.get(), cacheRepositoryProvider.get(), smartDownloadSchedulerProvider.get(), storageManagerProvider.get(), downloadErrorHandlerProvider.get());
  }

  public static DownloadManager_Factory create(Provider<Context> contextProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<SmartDownloadScheduler> smartDownloadSchedulerProvider,
      Provider<StorageManager> storageManagerProvider,
      Provider<DownloadErrorHandler> downloadErrorHandlerProvider) {
    return new DownloadManager_Factory(contextProvider, cacheRepositoryProvider, smartDownloadSchedulerProvider, storageManagerProvider, downloadErrorHandlerProvider);
  }

  public static DownloadManager newInstance(Context context, CacheRepository cacheRepository,
      SmartDownloadScheduler smartDownloadScheduler, StorageManager storageManager,
      DownloadErrorHandler downloadErrorHandler) {
    return new DownloadManager(context, cacheRepository, smartDownloadScheduler, storageManager, downloadErrorHandler);
  }
}
