package com.shuimu.course.data.workers;

import android.content.Context;
import com.shuimu.course.domain.repository.CacheRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DownloadManager_Factory implements Factory<DownloadManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CacheRepository> cacheRepositoryProvider;

  public DownloadManager_Factory(Provider<Context> contextProvider,
      Provider<CacheRepository> cacheRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.cacheRepositoryProvider = cacheRepositoryProvider;
  }

  @Override
  public DownloadManager get() {
    return newInstance(contextProvider.get(), cacheRepositoryProvider.get());
  }

  public static DownloadManager_Factory create(Provider<Context> contextProvider,
      Provider<CacheRepository> cacheRepositoryProvider) {
    return new DownloadManager_Factory(contextProvider, cacheRepositoryProvider);
  }

  public static DownloadManager newInstance(Context context, CacheRepository cacheRepository) {
    return new DownloadManager(context, cacheRepository);
  }
}
