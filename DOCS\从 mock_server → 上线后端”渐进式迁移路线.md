## 从 mock_server → 上线后端"渐进式迁移路线

> 本文档描述如何从当前的 JSON 驱动 mock_server 平滑演进到具备生产可用性的后端服务。目标是在**不阻断前端联调**的前提下，引入持久化、迁移脚本、监控与运维能力。

---
### 阶段 0 · 当前状态（纯 JSON）

```
mock_server/src/data/
├─ series.json          # 系列目录
├─ categories.json      # 分类目录
├─ videos.json          # 视频目录（含全局 playCount）
└─ user_data/
   ├─ user_001.json     # 私有：购买、进度、缓存
   ├─ user_002.json
   └─ ...
```

• 目录文件只读，所有用户共用。  
• `user_xxx.json` 存放个性化字段（购买、进度、缓存）。

---
### 阶段 1 · 加入 ORM，JSON 仍是真实数据源

1. 选型：`FastAPI + SQLAlchemy`（或 SQLModel）。  
2. **Model & Repository** 层：先写 ORM Model；Repository 依旧从 JSON 读取。  
3. 提供一次性脚本：
```bash
python scripts/seed_json.py --from-json mock_server/src/data --sqlite db.sqlite
```
将 JSON 导入 `SQLite` 以便后续对比。  
4. 接口返回结构保持不变；前端无感知。

---
### 阶段 2 · 切换 SQLite / Docker-Postgres 为主数据源

1. Repository 改读写数据库；JSON 仅作冷启动种子。  
2. 引入 Alembic 迁移脚本，版本管理 Schema。  
3. 新增 `docker-compose.yml`：
```yaml
services:
  api:
    build: .
    ports: ["8000:8000"]
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: shuimu
      POSTGRES_USER: suim
      POSTGRES_PASSWORD: pwd
```
4. CI 启动数据库服务后跑单元 / 集成测试。  
5. 前端仍使用 `http://localhost:8000/api/...`，无需改代码。

---
### 阶段 3 · 目录表 & 用户态表拆分 + 索引 / 缓存

| 类型 | 表 / 结构 | 说明 |
|------|-----------|------|
| 目录 (只读) | `series` / `categories` / `videos` | 低频改动，放价格、标题、icon、全局 playCount 等 |
| 用户态 (高写) | `purchases`, `watch_progress`, `video_cache` | 按 `(user_id, item_id)` 建复合索引 |
| 统计热字段 | Redis / Kafka 流 | 如 `playCount += 1`，异步汇总回写 MySQL/Postgres |

---
### 阶段 4 · 上线：云托管 & 灰度切流

1. **数据库**：迁移到云 RDS（AWS/GCP/阿里云），开启自动备份。  
2. **API 层**：Docker 镜像 → K8s/ECS 部署，Gunicorn+Uvicorn Worker。  
3. **监控**：Prometheus + Grafana 监控 QPS、99th-latency、错误率。  
4. **鉴权**：JWT/OAuth2，中间件将 `userId` 注入 `request.state`。  
5. **灰度**：
   * 初期实例开启"双写"：写数据库 & 备份 JSON。
   * 比对两边数据一致后，彻底停用 JSON 读写，改为只读目录 JSON。  
6. 最后将 `user_data/` 目录留作备份或删除。

---
### 阶段 5 · 运维与持续演进

1. **CI/CD**：每次合并主干自动运行 Alembic 迁移；若失败则回滚。  
2. **自动备份**：日备份 RDS、周导出对象存储。  
3. **Data Lake**：日志 & 事件进入 ClickHouse / BigQuery 便于 BI。  
4. **扩容**：热点数据走 Redis；根据 CPU/QPS 自动水平扩容。

---
## 快速回顾

| 阶段 | 核心目的 |
|------|-----------|
| 0 | 前端可立刻联调；超快迭代 |
| 1 | 提前写 ORM，保证模型 & 迁移正确 |
| 2 | 让数据库成为真实源，CI 也能跑 |
| 3 | 按读写特征拆表，优化索引 & 缓存 |
| 4 | 上云部署，灰度切流，保障可用性 |
| 5 | 监控、备份、自动扩缩容 |

这样就能在**不打断开发**的情况下，逐步获得安全、伸缩、运维与监控能力。
