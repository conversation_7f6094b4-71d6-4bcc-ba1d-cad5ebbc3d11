import json
from fastapi import APIRouter, HTTPException
from typing import Dict
from ..models.user import UserLogin
from pathlib import Path

router = APIRouter()

# Resolve the JSON data path relative to this file to avoid issues with different working directories
DATA_DIR = Path(__file__).resolve().parent.parent / "data"
with open(DATA_DIR / "users.json", "r", encoding="utf-8") as f:
    users_db = json.load(f)

@router.post("/auth/login", response_model=Dict[str, str])
def login(user_credentials: UserLogin):
    """
    Authenticates a user and returns a token.
    """
    for user in users_db:
        if user["username"] == user_credentials.username and user["password"] == user_credentials.password:
            return {"token": user["token"], "message": "Login successful"}
    
    raise HTTPException(status_code=401, detail="Invalid username or password")
