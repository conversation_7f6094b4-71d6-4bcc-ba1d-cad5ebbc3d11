package com.shuimu.course.data.remote.api

import com.shuimu.course.data.remote.dto.EarningsResponseDto
import com.shuimu.course.data.remote.dto.UserDto
import com.shuimu.course.data.remote.dto.UserDataDto
import com.shuimu.course.data.remote.dto.orders.OrderDto
import com.shuimu.course.data.remote.dto.profile.ProfileDataDto
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

// Dummy response DTOs for other share endpoints
data class ShareLinkDto(val share_link: String)
data class RankingDto(val rank: Int, val username: String, val earnings: Double)
data class MaterialDto(val posters: List<Any>, val copywriting: List<Any>)


interface UserApi {

    @GET("user/profile_data")
    suspend fun getProfileData(): ProfileDataDto

    @GET("user/data")
    suspend fun getUserData(): UserDataDto

    @GET("user/orders")
    suspend fun getOrders(): List<OrderDto>

    @GET("user/purchases")
    suspend fun getUserPurchases(@Header("Authorization") token: String): Response<List<Any>> // Define PurchaseDto later

    @GET("share/earnings")
    suspend fun getShareEarnings(@Header("Authorization") token: String): Response<EarningsResponseDto>

    @POST("share/create")
    suspend fun createShareLink(@Header("Authorization") token: String): Response<ShareLinkDto>

    @GET("share/ranking")
    suspend fun getShareRanking(): Response<List<RankingDto>>

    @GET("share/materials")
    suspend fun getShareMaterials(): Response<MaterialDto>
}
