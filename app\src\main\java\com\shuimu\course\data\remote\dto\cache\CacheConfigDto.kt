package com.shuimu.course.data.remote.dto.cache

import com.google.gson.annotations.SerializedName
import com.shuimu.course.domain.repository.CacheConfig

/**
 * 缓存配置 DTO
 */
data class CacheConfigDto(
    @SerializedName("maxConcurrentDownloads")
    val maxConcurrentDownloads: Int,

    @SerializedName("cacheDirectory")
    val cacheDirectory: String,

    @SerializedName("supportedFormats")
    val supportedFormats: List<String>
)

/**
 * 转换为Domain模型
 */
fun CacheConfigDto.toDomain(): CacheConfig {
    return CacheConfig(
        maxConcurrentDownloads = this.maxConcurrentDownloads,
        cacheDirectory = this.cacheDirectory,
        supportedFormats = this.supportedFormats
    )
}