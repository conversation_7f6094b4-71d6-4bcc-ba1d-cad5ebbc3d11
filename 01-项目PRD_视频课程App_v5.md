# 水幕 视频课程App 产品需求文档（PRD）v5

---

## 📋 项目概述

**项目名称**：水幕视频课程App  
**项目类型**：付费视频课程学习平台  
**核心特色**：云盘存储 + 强制本地缓存 + 离线播放  
**业务模式**：课程付费购买，支持免费试学，分享分成机制

## 🏗️ 技术架构概要

### 整体架构
- **四端协同架构**：Android App + Python管理端 + Cloudflare Workers API + FastAPI Mock Server
- **Android技术栈**：Kotlin + Jetpack Compose + Clean Architecture + MVVM
- **开发环境**：Mock Server本地开发 + UI组件100%还原 + ComponentRegistry管理
- **开发理念**：快速迭代开发，功能实现优先，架构清晰可维护，

### 技术约束
- **最低Android版本**：API 26（Android 8.0）
- **UI框架**：Jetpack Compose（现代化声明式UI）
- **视频播放**：AndroidX Media3 ExoPlayer（最新视频播放架构，支持离线播放和DRM）+ 网盘直链缓存架构
- **支付集成**：支付宝SDK + 微信SDK
- **数据存储**：Room数据库 + DataStore配置
- **视频存储**：本地强制缓存，网盘直链下载

### 详细技术方案
- **整体系统设计**：参见《01-完整技术方案说明文档.md》
- **Android架构标准**：参见《02-项目架构标准文档.md》
- **UI组件使用规范**：参见《组件使用保障规范.md》
- **Cursor开发指令**：参见《Cursor开发指令文档_水幕视频课程App_升级版.md》

---

## 📱 产品功能结构

### 页面架构总览

#### 核心页面（4个）
1. **首页**：课程列表展示，系列分类浏览
2. **视频播放页**：视频播放器，学习进度管理
3. **我的页面**：个人中心，学习统计展示
4. **支付页面**：全屏支付流程，订单确认

#### 功能页面（10个）
5. **登录页面**：用户登录认证
6. **缓存管理页面**：本地视频管理
7. **购买记录页面**：购买历史查看
8. **学习报告页面**：详细学习数据分析
9. **设置页面**：应用配置和用户偏好
10. **帮助与反馈页面**：用户支持和问题反馈
11. **关于我们页面**：应用信息和版本说明
12. **分享收益页面**：分成明细和收益统计
13. **分享排行页面**：排行榜和激励机制
14. **分享素材页面**：分享文案和素材选择

---

## 🏠 首页功能需求

### 布局设计
- **响应式布局**：最大宽度480dp，居中显示
- **顶部导航**：LOGO + 用户昵称/登录按钮 + 搜索功能
- **搜索功能**：点击弹出搜索弹窗（占屏幕60%高度）
- **内容组织**：支持纵向分组或横向分栏切换

### 课程结构展示

#### 一级类（系列）
- **免费体验系列**：排在最前，显示"免费"标签，无价格显示
- **付费系列**：显示系列名 + 价格，支持折叠/展开
- **全套选项**：动态价格，为所有未购买系列的总价，全部购买后自动隐藏
- **购买状态**：已购买系列不显示价格，只显示系列名

#### 二级类（分类）
- **展示方式**：分类名 + 价格（未购买时），支持折叠/展开
- **视频列表**：展开后显示固定高度（3个视频高度），超出可滚动
- **智能定位**：展开时自动滚动到上次观看的视频并高亮
- **交互优化**：支持自动收起其他分类（可配置）

#### 视频项（通用组件）
- **使用场景**：首页分类列表、搜索结果列表、视频播放页分类列表框、全屏播放分类列表框
- **基础显示**：播放图标 + 视频标题
  - **已购买状态展示**： 缓存状态显示 + 播放进度条 + 进度百分比 + 观看次数徽章（×N格式）
  - **未购买状态展示**：锁定图标（🔒）

- **交互逻辑**：
  - 未购买：底部弹出支付框，点击"立即支付"按钮跳转到支付页面
  - 已购买：进入视频播放页
- **视觉反馈**：悬浮高亮、上次观看高亮（统一浅蓝色背景）
- **状态管理**：本地记录缓存状态、学习进度、最后观看位置

### 数据获取逻辑

#### 数据来源
- **服务器实时获取**：所有课程结构信息（系列、分类、视频、价格、观看次数、进度）
- **更新时机**：App启动、用户登录、支付成功后自动刷新
- **动态结构**：支持服务器端课程结构的灵活调整，不硬编码任何课程信息

#### 个性化展示
- **未登录用户**：显示完整课程目录和所有价格信息
- **已登录用户**：根据购买状态个性化显示，已购买内容不显示价格

---

## 🎬 视频播放功能

### 播放器要求
- **播放器架构**：基于AndroidX Media3 ExoPlayer最新架构
- **核心功能**：支持本地离线播放，强制缓存模式
- **播放控制**：播放/暂停、进度控制、快进/快退15s、倍速播放、全屏切换、播放上一个/下一个、自动播放下一个
- **学习功能**：自动记录播放进度、支持断点续播
- **安全防护**：防录屏水印、动态水印显示、DRM内容保护支持

### 视频播放架构（重要）
- **视频资源来源**：通过网盘直链提供视频文件
- **播放流程**：用户点击视频 → 判断购买状态 → 已购买则缓存到本地 → 播放本地缓存文件
- **离线支持**：完全支持离线播放，不直接播放网络视频
- **缓存策略**：强制本地缓存，确保播放流畅性和离线可用性

### AndroidX Media3技术优势
- **现代化架构**：替代已废弃的ExoPlayer独立包（com.google.android.exoplayer2）
- **统一API设计**：Media3统一了音视频播放的API接口，简化开发复杂度
- **性能优化**：更好的内存管理和播放性能，支持4K视频流畅播放
- **官方长期支持**：Google官方维护，确保与最新Android版本兼容
- **DRM支持增强**：内置Widevine DRM支持，提供企业级内容保护
- **版本要求**：使用1.7.1+最新稳定版本

### 缓存机制
- **强制缓存**：所有视频必须先下载到本地才能观看
- **缓存管理**：支持选择性缓存、批量下载、存储空间管理
- **缓存状态**：实时显示下载进度、缓存完成状态
- **存储位置**：本地应用私有目录，确保安全性

---

## 👤 用户系统功能

### 登录认证
- **登录方式**：用户名 + 密码
- **登录状态**：本地持久化存储，支持自动登录
- **安全控制**：单设备在线，后登录踢前登录
- **异常处理**：登录失败、账号被禁用等明确提示

### 个人中心
- **用户信息**：头像、昵称、购买状态显示
- **学习统计**：已购课程数量、学习时长、完成进度
- **分享数据**：分享次数、转化人数、累计收益（仅已购买用户）
- **功能入口**：购买记录、缓存管理、学习报告、设置等

---

## 💰 支付购买功能

### 支付流程
- **触发方式**：点击未购买的系列/分类
- **支付界面**：底部弹出支付框（屏幕高度1/4）
- **支付方式**：支付宝 + 微信支付
- **支付状态**：实时反馈支付结果，成功后自动刷新权限

### 购买逻辑
- **购买单位**：支持单个分类购买或整个系列购买
- **价格计算**：全套价格为未购买部分的动态总价
- **权限管理**：购买后立即获得观看权限，支持离线缓存

### 分享分成
- **分成比例**：30%分成收益
- **分享机制**：已购买用户可分享获得收益
- **收益统计**：实时统计分享数据和收益明细
- **客服支持**：提供客服微信，验证身份后拉入学习群

---

## 📊 数据统计功能

### 学习数据
- **学习进度**：每个视频的观看进度百分比
- **学习时长**：累计学习时间统计
- **学习报告**：详细的学习数据分析和可视化展示

### 用户行为
- **观看记录**：记录用户的观看历史和偏好
- **搜索记录**：保存搜索历史，提供搜索建议
- **操作记录**：记录用户的关键操作，用于优化体验

---

## 🔧 系统功能需求

### 缓存管理
- **存储信息**：显示已用空间/总空间，进度条展示
- **视频列表**：紧凑布局显示缓存的视频，支持批量删除
- **交互优化**：支持长按多选、滑动删除、点击播放

### 设置功能
- **播放设置**：默认播放质量、自动播放下一集等
- **缓存设置**：WiFi自动缓存、存储位置选择等
- **通知设置**：课程更新通知、学习提醒等

### 帮助支持
- **帮助文档**：常见问题解答、使用指南
- **反馈渠道**：问题反馈、建议提交
- **关于信息**：应用版本、隐私政策、用户协议

---

## 🎨 UI/UX设计要求

### 设计风格
- **主色调**：绿色系渐变主题
- **设计语言**：现代化扁平设计，简洁清晰
- **交互反馈**：统一的高亮效果（浅蓝色背景）
- **响应式**：适配不同屏幕尺寸，最佳宽度480dp

### 用户体验
- **加载状态**：所有异步操作都有加载提示
- **错误处理**：友好的错误提示和重试机制
- **流畅动画**：页面切换和状态变化的平滑动画
- **无障碍**：支持基本的无障碍访问功能

---

## 🔒 安全与性能要求

### 安全防护
- **防录屏**：使用FLAG_SECURE防止录屏截图
- **动态水印**：视频播放时显示用户信息水印
- **数据加密**：敏感数据本地加密存储
- **网络安全**：HTTPS通信，证书校验

### 性能要求
- **启动速度**：应用启动时间 ≤ 3秒
- **页面切换**：页面切换响应时间 ≤ 500ms
- **内存使用**：正常使用内存占用 ≤ 200MB
- **APK大小**：安装包大小控制在 ≤ 50MB

---

## 📈 数据结构规范

### 课程数据结构
```json
{
  "series": [
    {
      "id": "series_id",
      "title": "系列名称",
      "icon": "图标URL",
      "iconColor": "图标颜色",
      "price": "价格",
      "isFree": false,
      "totalVideos": 10,
      "categories": [
        {
          "id": "category_id",
          "title": "分类名称",
          "seriesId": "所属系列ID",
          "price": "分类价格",
          "isFree": false,
          "totalVideos": 5,
          "videos": [
            {
              "id": "video_id",
              "title": "视频标题",
              "duration": "22:30",
              "description": "视频描述",
              "categoryId": "所属分类ID",
              "playCount": "5.2万",
              "defaultProgress": 0,
              "defaultWatchCount": 0,
              "cloudUrl": "网盘直链URL",
              "localPath": "本地缓存路径",
              "cacheStatus": "cached|downloading|not_cached"
            }
          ]
        }
      ]
    }
  ]
}
```

### 视频播放架构数据结构（重要）
```json
{
  "videoPlayback": {
    "architecture": "网盘直链+本地缓存+离线播放",
    "playerFramework": "AndroidX Media3 ExoPlayer",
    "playerVersion": "1.7.1+",
    "workflow": [
      "1. 用户点击视频",
      "2. 检查购买状态",
      "3. 已购买则检查本地缓存状态",
      "4. 如未缓存，从网盘直链下载到本地",
      "5. 播放本地缓存文件",
      "6. 完全支持离线播放"
    ],
    "cachePolicy": {
      "mandatory": true,
      "description": "强制本地缓存，不支持直接播放网络视频",
      "storageLocation": "应用私有目录",
      "offlineSupport": true
    },
    "techSpecs": {
      "framework": "AndroidX Media3",
      "drmSupport": "Widevine",
      "formatSupport": ["MP4", "HLS", "DASH"],
      "maxResolution": "4K",
      "offlinePlayback": true
    }
  }
}
```

### 用户数据结构
```json
{
  "user": {
    "id": "user_id",
    "username": "用户名",
    "email": "邮箱",
    "avatar": "头像URL",
    "badgeLevel": 1,
    "totalWatchTime": 0,
    "totalVideosWatched": 0,
    "totalDaysActive": 0,
    "streakDays": 0,
    "lastActiveDate": "2024-01-01"
  },
  "purchases": [
    {
      "id": "purchase_id",
      "userId": "用户ID",
      "itemId": "购买项目ID",
      "itemType": "series|category",
      "itemTitle": "购买项目名称",
      "price": "购买价格",
      "paymentMethod": "支付方式",
      "purchaseDate": 1704067200,
      "status": "completed"
    }
  ]
}
```

---

## 🎨 开发环境准备

### Mock Server环境
- **技术选型**：FastAPI + Python
- **运行环境**：localhost:8000
- **核心功能**：模拟完整的后端API服务
- **数据管理**：支持动态数据场景切换
- **开发优势**：前端开发不依赖后端进度，支持完整的业务逻辑测试

### UI组件系统
- **组件来源**：UI Prototype文件夹中的23个核心组件
- **管理工具**：ComponentRegistry.kt统一注册和管理
- **复用原则**：严格使用现有组件，禁止重复创建
- **还原标准**：与UI原型100%像素级一致
- **验证机制**：组件使用规范验证和复用度统计

---

## 🎯 UI组件设计规范

### 核心组件架构（23个组件）
```
components/
├── base/           # 基础组件（5个）
│   ├── ShuimuButton.kt
│   ├── ShuimuCard.kt
│   ├── ShuimuTextField.kt
│   ├── BadgeComponent.kt      # 观看次数徽章系统（×N格式）
│   └── ProgressBar.kt
├── display/        # 展示组件（4个）
│   ├── VideoItem.kt           # 通用视频项组件（多场景复用）
│   ├── CategoryCard.kt        # 分类卡片
│   ├── SearchItem.kt          # 搜索结果项
│   └── SeriesCard.kt          # 系列卡片
├── navigation/     # 导航组件（2个）
├── dialog/         # 弹窗组件（3个）
├── video/          # 视频组件（2个）
├── user/           # 用户组件（4个）
├── state/          # 状态组件（2个）
└── ComponentRegistry.kt
```

### 设计还原要求
- **像素级精确**：所有组件必须与UI原型完全一致
- **状态完整**：支持购买状态、缓存状态、进度状态等所有业务状态
- **交互一致**：悬浮效果、高亮状态、动画过渡完全按照原型实现
- **徽章系统**：BadgeComponent专用于显示观看次数（×N格式），样式和数值计算规范

### 组件复用机制
- **开发前必查**：使用ComponentRegistry.findComponentsByUseCase()查找可复用组件
- **扩展优先**：通过参数扩展现有组件而非创建新组件
- **依赖管理**：清晰的组件依赖关系和版本管理
- **统计分析**：定期生成组件使用报告和复用度统计

---

## 🚀 开发优先级

### 前期准备阶段：环境搭建
1. **UI组件提取**：从UI Prototype中提取23个核心组件
2. **Mock Server搭建**：建立完整的本地开发环境
3. **ComponentRegistry创建**：建立组件管理和验证体系

### 第一阶段：核心功能（MVP）
1. **首页课程展示**：系列、分类、视频的基本展示和交互
2. **视频播放**：基于AndroidX Media3的基本播放功能和进度记录
3. **用户登录**：基础的用户认证和状态管理
4. **支付购买**：支付流程和权限管理

**重要提醒**：确保使用AndroidX Media3最新版本（1.7.1+），不要使用已废弃的独立ExoPlayer包（com.google.android.exoplayer2）

### 第二阶段：完善功能
1. **缓存系统**：视频下载和离线播放（本地缓存状态管理）
2. **个人中心**：用户信息和学习统计（严格使用UserProfileHeader等组件）
3. **搜索功能**：课程搜索和历史记录（使用SearchItem组件）
4. **设置管理**：应用配置和用户偏好（使用SettingItem组件）

### 第三阶段：高级功能
1. **分享分成**：分享机制和收益统计（使用专门的分享收益展示组件）
2. **学习报告**：详细的数据分析和可视化（包含多种图表和统计展示）
3. **安全防护**：防录屏和水印功能
4. **性能优化**：加载速度和内存优化

---

## 📋 验收标准

### 功能完整性
- [ ] 14个页面功能完整实现
- [ ] 课程浏览、购买、播放流程完整
- [ ] 用户登录、权限管理正常
- [ ] 支付流程和分享分成功能正常
- [ ] Mock Server集成完整，所有API对接正常

### UI设计还原度
- [ ] 所有界面与UI原型100%像素级一致
- [ ] 23个核心组件严格按照原型实现
- [ ] 徽章系统、购买状态卡片等关键组件完全还原
- [ ] 交互效果（悬浮、高亮、动画）与原型一致

### 组件使用规范
- [ ] ComponentRegistry验证通过，无重复组件
- [ ] 核心组件正确复用（BadgeComponent用于显示观看次数徽章）
- [ ] 所有UI开发都使用ComponentRegistry查找现有组件
- [ ] 组件使用统计报告显示高复用率

### 开发环境集成
- [ ] Mock Server正常运行，模拟所有后端API接口
- [ ] 数据获取机制正常（App ↔ Mock Server API调用）
- [ ] 支持动态数据场景切换（不同用户状态、购买情况）
- [ ] 本地缓存和离线功能完整

### 用户体验
- [ ] 界面美观，交互流畅
- [ ] 加载速度满足性能要求
- [ ] 错误处理友好，异常情况有明确提示
- [ ] 离线功能正常，缓存管理便捷

### 技术质量
- [ ] 代码架构清晰，符合Clean Architecture原则
- [ ] 核心功能有测试覆盖
- [ ] 安全防护措施有效
- [ ] 性能指标达到要求

---

## 📚 相关文档

- **《01-完整技术方案说明文档.md》**：四端协同架构详细设计
- **《02-项目架构标准文档.md》**：Android端具体实现标准
- **《项目初始化完整工作流程模板.md》**：项目搭建和开发流程指导
- **UI原型文件**：详细的界面设计和交互原型

---

## 📚 相关文档索引

### 开发指导文档
- **UI组件规范**：《组件使用保障规范.md》- 23个核心组件的使用规范和复用机制
- **开发指令**：《Cursor开发指令文档_水幕视频课程App_升级版.md》- 32个结构化开发指令
- **技术架构**：《01-完整技术方案说明文档.md》- 整体系统设计和技术选型
- **架构标准**：《02-项目架构标准文档.md》- Android开发架构标准和规范

### 设计资源
- **UI原型**：`UI Prototype/` - 14个页面的完整原型设计
- **组件素材**：`UI Prototype/data/` - 核心组件的样式和模板文件
- **设计规范**：徽章系统、购买状态卡片等关键组件的设计标准

### 开发环境
- **Mock Server**：本地FastAPI服务，模拟完整后端API
- **数据管理**：支持多种用户场景和业务状态切换
- **组件管理**：ComponentRegistry.kt统一管理23个核心组件

---

*本PRD v5版本重点强化了UI组件100%还原、Mock Server集成和开发流程规范化，确保项目开发的一致性和高质量交付。* 