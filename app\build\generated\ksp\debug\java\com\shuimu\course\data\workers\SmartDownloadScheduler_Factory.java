package com.shuimu.course.data.workers;

import android.content.Context;
import com.shuimu.course.domain.repository.CacheRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SmartDownloadScheduler_Factory implements Factory<SmartDownloadScheduler> {
  private final Provider<Context> contextProvider;

  private final Provider<CacheRepository> cacheRepositoryProvider;

  private final Provider<EnhancedDownloadEngine> enhancedDownloadEngineProvider;

  public SmartDownloadScheduler_Factory(Provider<Context> contextProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<EnhancedDownloadEngine> enhancedDownloadEngineProvider) {
    this.contextProvider = contextProvider;
    this.cacheRepositoryProvider = cacheRepositoryProvider;
    this.enhancedDownloadEngineProvider = enhancedDownloadEngineProvider;
  }

  @Override
  public SmartDownloadScheduler get() {
    return newInstance(contextProvider.get(), cacheRepositoryProvider.get(), enhancedDownloadEngineProvider.get());
  }

  public static SmartDownloadScheduler_Factory create(Provider<Context> contextProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<EnhancedDownloadEngine> enhancedDownloadEngineProvider) {
    return new SmartDownloadScheduler_Factory(contextProvider, cacheRepositoryProvider, enhancedDownloadEngineProvider);
  }

  public static SmartDownloadScheduler newInstance(Context context, CacheRepository cacheRepository,
      EnhancedDownloadEngine enhancedDownloadEngine) {
    return new SmartDownloadScheduler(context, cacheRepository, enhancedDownloadEngine);
  }
}
