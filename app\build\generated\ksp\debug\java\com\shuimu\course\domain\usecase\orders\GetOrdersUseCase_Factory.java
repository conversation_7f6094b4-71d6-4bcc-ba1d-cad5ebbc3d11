package com.shuimu.course.domain.usecase.orders;

import com.shuimu.course.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class GetOrdersUseCase_Factory implements Factory<GetOrdersUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetOrdersUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetOrdersUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetOrdersUseCase_Factory create(Provider<UserRepository> userRepositoryProvider) {
    return new GetOrdersUseCase_Factory(userRepositoryProvider);
  }

  public static GetOrdersUseCase newInstance(UserRepository userRepository) {
    return new GetOrdersUseCase(userRepository);
  }
}
