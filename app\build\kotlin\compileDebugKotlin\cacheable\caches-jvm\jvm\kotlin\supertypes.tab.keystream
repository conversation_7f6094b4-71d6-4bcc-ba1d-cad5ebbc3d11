#com.shuimu.course.CourseApplicationcom.shuimu.course.MainActivity1com.shuimu.course.data.local.database.AppDatabase1com.shuimu.course.data.local.entities.CacheStatus<com.shuimu.course.data.remote.interceptors.UserIdInterceptor5com.shuimu.course.data.repository.CacheRepositoryImpl7com.shuimu.course.data.repository.PaymentRepositoryImpl<com.shuimu.course.data.repository.PlayProgressRepositoryImpl8com.shuimu.course.data.repository.PlaylistRepositoryImpl6com.shuimu.course.data.repository.SearchRepositoryImpl6com.shuimu.course.data.repository.SeriesRepositoryImpl5com.shuimu.course.data.repository.ShareRepositoryImpl>com.shuimu.course.data.repository.UserPreferenceRepositoryImpl4com.shuimu.course.data.repository.UserRepositoryImpl5com.shuimu.course.data.repository.VideoRepositoryImpl2com.shuimu.course.data.workers.VideoDownloadWorker*com.shuimu.course.domain.model.CacheStatusAcom.shuimu.course.domain.usecase.cache.VideoClickAction.PlayVideoGcom.shuimu.course.domain.usecase.cache.VideoClickAction.ShowCacheDialogNcom.shuimu.course.domain.usecase.cache.VideoClickAction.ShowDownloadingMessageHcom.shuimu.course.domain.usecase.cache.VideoClickAction.ShowResumeDialogGcom.shuimu.course.domain.usecase.cache.VideoClickAction.ShowRetryDialog.com.shuimu.course.domain.util.Resource.Success,com.shuimu.course.domain.util.Resource.Error.com.shuimu.course.domain.util.Resource.Loading(com.shuimu.course.player.PlaybackService<com.shuimu.course.presentation.ui.components.base.BadgeStyleCcom.shuimu.course.presentation.ui.components.base.IconAnimationType>com.shuimu.course.presentation.viewmodel.CacheManagerViewModel:com.shuimu.course.presentation.viewmodel.EarningsViewModel6com.shuimu.course.presentation.viewmodel.HomeViewModel7com.shuimu.course.presentation.viewmodel.LoginViewModel9com.shuimu.course.presentation.viewmodel.PaymentViewModel9com.shuimu.course.presentation.viewmodel.ProfileViewModel8com.shuimu.course.presentation.viewmodel.SearchViewModel5com.shuimu.course.presentation.viewmodel.PlayerStatus=com.shuimu.course.presentation.viewmodel.VideoPlayerViewModel?com.shuimu.course.presentation.viewmodel.orders.OrdersViewModelGcom.shuimu.course.presentation.viewmodel.profile.ProfileUiState.LoadingGcom.shuimu.course.presentation.viewmodel.profile.ProfileUiState.SuccessEcom.shuimu.course.presentation.viewmodel.profile.ProfileUiState.ErrorAcom.shuimu.course.presentation.viewmodel.profile.ProfileViewModel+com.shuimu.course.data.manager.SyncPriority+com.shuimu.course.data.manager.NetworkState                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   