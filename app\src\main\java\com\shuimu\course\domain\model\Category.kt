package com.shuimu.course.domain.model

data class Category(
    val id: String,
    val title: String,
    val seriesId: String, // 所属系列ID
    val price: Int?, // 价格，单位：分
    val isFree: Boolean,
    val isPurchased: Boolean? = null,
    val defaultExpanded: Boolean = false,
    val videos: List<Video>,
    
    // 🔥 计算字段
    val displayTitle: String, // 显示标题（包含价格信息）
    val watchCount: Int?, // 分类总观看次数 (所有视频的个人观看次数累计，用于徽章显示)
    
    // 🔥 统一进度参数：分类完成度 (0.0-1.0，用于进度条显示和百分比计算)
    val progress: Float?
) 