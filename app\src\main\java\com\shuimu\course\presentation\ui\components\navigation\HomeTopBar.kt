package com.shuimu.course.presentation.ui.components.navigation

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.components.share.ShareButton
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import androidx.compose.animation.core.*
import androidx.compose.runtime.getValue
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import com.shuimu.course.presentation.ui.components.base.ShuimuLogo

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeTopBar(
    modifier: Modifier = Modifier,
    userName: String,
    onSearchClick: () -> Unit,
    onShareClick: () -> Unit,
    onUserClick: () -> Unit
) {
    TopAppBar(
        modifier = modifier,
        title = {
            Row(verticalAlignment = Alignment.CenterVertically) {
                ShuimuLogo(size = 40.dp) // 🔥 增大Logo尺寸，符合原型设计
                Spacer(modifier = Modifier.width(8.dp))
                Text(text = "水幕", style = MaterialTheme.typography.titleLarge)
            }
        },
        actions = {
            IconButton(onClick = onSearchClick) {
                Icon(imageVector = Icons.Default.Search, contentDescription = "Search")
            }

            // Animated Share Icon
            val infiniteTransition = rememberInfiniteTransition(label = "share-icon-transition")
            val scale by infiniteTransition.animateFloat(
                initialValue = 1f,
                targetValue = 1.1f,
                animationSpec = infiniteRepeatable(
                    animation = tween(1000, easing = LinearEasing),
                    repeatMode = RepeatMode.Reverse
                ),
                label = "share-icon-scale"
            )
            val shareBrush = Brush.linearGradient(
                colors = listOf(Color(0xFF3B82F6), Color(0xFF10B981)),
            )
            IconButton(onClick = onShareClick) {
                Icon(
                    imageVector = Icons.Default.Share,
                    contentDescription = "Share",
                    modifier = Modifier
                        .scale(scale)
                        .drawWithCache {
                            onDrawWithContent {
                                drawContent()
                                drawRect(shareBrush, blendMode = BlendMode.SrcAtop)
                            }
                        },
                    tint = Color.White // This is necessary to render the icon shape
                )
            }

            Spacer(modifier = Modifier.width(4.dp))
            TextButton(onClick = onUserClick) {
                Icon(imageVector = Icons.Default.Person, contentDescription = null, tint = Color.White)
                Spacer(modifier = Modifier.width(4.dp))
                Text(text = userName, color = Color.White)
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.primary,
            titleContentColor = Color.White,
            actionIconContentColor = Color.White
        )
    )
}

@Preview
@Composable
fun HomeTopBarPreview() {
    ShuimuCourseTheme {
        HomeTopBar(userName = "张三", onSearchClick = {}, onShareClick = {}, onUserClick = {})
    }
} 