#!/usr/bin/node

const fs = require('fs');
const path = require('path');
const os = require('os');

// 配置文件路径
const configDir = path.join(os.homedir(), '.claude');
const configFile = path.join(configDir, 'config.json');

// 从stdin读取输入
function readStdin() {
    return new Promise((resolve) => {
        let input = '';
        
        if (process.stdin.isTTY) {
            resolve('');
            return;
        }
        
        process.stdin.setEncoding('utf8');
        
        process.stdin.on('readable', () => {
            let chunk;
            while ((chunk = process.stdin.read()) !== null) {
                input += chunk;
            }
        });
        
        process.stdin.on('end', () => {
            resolve(input.trim());
        });
    });
}

// 解析命令行参数
function parseArgs(args) {
    const options = {
        prompt: '',
        help: false,
        version: false,
        translate: null,
        count: false,
        format: null,
        extract: null
    };
    
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        
        switch (arg) {
            case '--help':
            case '-h':
                options.help = true;
                break;
            case '--version':
            case '-v':
                options.version = true;
                break;
            case '-p':
            case '--prompt':
                if (i + 1 < args.length) {
                    options.prompt = args[++i];
                }
                break;
            case '--translate':
                if (i + 1 < args.length) {
                    options.translate = args[++i];
                }
                break;
            case '--count':
                options.count = true;
                break;
            case '--format':
                if (i + 1 < args.length) {
                    options.format = args[++i];
                }
                break;
            case '--extract':
                if (i + 1 < args.length) {
                    options.extract = args[++i];
                }
                break;
            default:
                if (!arg.startsWith('-') && !options.prompt) {
                    options.prompt = arg;
                }
        }
    }
    
    return options;
}

// 显示帮助信息
function showHelp() {
    console.log(`Claude CLI v2.0.0 - Offline Version (离线版)

Usage: claude [options] [prompt]

离线功能 (Offline Features):
  -p, --prompt <text>      显示提示和输入内容
  --translate <lang>       简单翻译提示 (en/zh)
  --count                  统计文本字符和单词数
  --format <type>          格式化文本 (upper/lower/title)
  --extract <type>         提取信息 (emails/urls/numbers)
  -h, --help              显示帮助信息
  -v, --version           显示版本

Examples (示例):
  claude "Hello World"                    # 显示文本
  claude --translate zh "Hello World"     # 简单英译中
  claude --translate en "你好世界"        # 简单中译英
  echo "some text" | claude --count       # 统计字数
  echo "HELLO world" | claude --format title  # 格式化为标题格式
  echo "联系我：<EMAIL>" | claude --extract emails  # 提取邮箱

注意: 这是离线版本，提供基本文本处理功能。
要使用完整AI功能，请获取API密钥并使用在线版本。`);
}

// 简单翻译功能
function simpleTranslate(text, targetLang) {
    const translations = {
        'zh': {
            'hello': '你好',
            'world': '世界',
            'good': '好',
            'morning': '早上',
            'afternoon': '下午',
            'evening': '晚上',
            'night': '夜晚',
            'thank you': '谢谢',
            'please': '请',
            'yes': '是',
            'no': '不',
            'computer': '电脑',
            'programming': '编程',
            'code': '代码',
            'function': '函数',
            'variable': '变量'
        },
        'en': {
            '你好': 'hello',
            '世界': 'world',
            '早上': 'morning',
            '下午': 'afternoon',
            '晚上': 'evening',
            '夜晚': 'night',
            '谢谢': 'thank you',
            '请': 'please',
            '是': 'yes',
            '不': 'no',
            '电脑': 'computer',
            '编程': 'programming',
            '代码': 'code',
            '函数': 'function',
            '变量': 'variable'
        }
    };
    
    let result = text.toLowerCase();
    const dict = translations[targetLang] || {};
    
    for (const [key, value] of Object.entries(dict)) {
        const regex = new RegExp(key, 'gi');
        result = result.replace(regex, value);
    }
    
    return result;
}

// 统计文本
function countText(text) {
    const chars = text.length;
    const words = text.trim().split(/\s+/).filter(w => w.length > 0).length;
    const lines = text.split('\n').length;
    
    console.log(`字符数 (Characters): ${chars}`);
    console.log(`单词数 (Words): ${words}`);
    console.log(`行数 (Lines): ${lines}`);
}

// 格式化文本
function formatText(text, format) {
    switch (format) {
        case 'upper':
            return text.toUpperCase();
        case 'lower':
            return text.toLowerCase();
        case 'title':
            return text.replace(/\w\S*/g, (txt) => 
                txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
            );
        default:
            return text;
    }
}

// 提取信息
function extractInfo(text, type) {
    const patterns = {
        emails: /[\w\.-]+@[\w\.-]+\.\w+/g,
        urls: /https?:\/\/[^\s]+/g,
        numbers: /\d+/g,
        phones: /\d{3}-\d{3}-\d{4}|\d{11}/g
    };
    
    const pattern = patterns[type];
    if (!pattern) {
        console.log('支持的提取类型: emails, urls, numbers, phones');
        return;
    }
    
    const matches = text.match(pattern) || [];
    if (matches.length > 0) {
        console.log(`找到 ${matches.length} 个 ${type}:`);
        matches.forEach((match, index) => {
            console.log(`${index + 1}. ${match}`);
        });
    } else {
        console.log(`没有找到 ${type}`);
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const options = parseArgs(args);
    
    // 处理版本显示
    if (options.version) {
        console.log('Claude CLI v2.0.0 - Offline Version (离线版)');
        return;
    }
    
    // 处理帮助显示
    if (options.help) {
        showHelp();
        return;
    }
    
    // 读取stdin输入（管道支持）
    const stdinInput = await readStdin();
    
    // 确定最终的输入
    let finalInput = options.prompt || stdinInput;
    
    if (!finalInput) {
        console.log('Claude CLI v2.0.0 - Offline Version (离线版)');
        console.log('这是离线版本，提供基本文本处理功能。');
        console.log('使用 --help 查看可用功能');
        console.log('');
        console.log('💡 要使用完整AI功能：');
        console.log('1. 访问 https://console.anthropic.com/');
        console.log('2. 获取API密钥');
        console.log('3. 运行: claude --config api_key YOUR_KEY');
        return;
    }
    
    // 处理各种功能
    if (options.translate) {
        const translated = simpleTranslate(finalInput, options.translate);
        console.log('翻译结果:', translated);
    } else if (options.count) {
        countText(finalInput);
    } else if (options.format) {
        const formatted = formatText(finalInput, options.format);
        console.log(formatted);
    } else if (options.extract) {
        extractInfo(finalInput, options.extract);
    } else {
        // 默认显示
        console.log('📝 输入内容:');
        console.log(finalInput);
        console.log('');
        console.log('💡 这是离线版本。要获得AI回复，请设置API密钥。');
        console.log('使用 --help 查看离线功能，或访问 https://console.anthropic.com/ 获取API密钥');
    }
}

// 运行主函数
main().catch(console.error); 