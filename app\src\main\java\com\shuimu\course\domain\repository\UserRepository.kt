package com.shuimu.course.domain.repository

import com.shuimu.course.domain.model.User
import com.shuimu.course.domain.model.orders.Order
import com.shuimu.course.domain.model.profile.UserProfile
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow

interface UserRepository {
    fun getUserState(): Flow<User?>

    suspend fun login(username: String, password: String): Flow<Resource<Unit>>

    suspend fun logout()

    suspend fun getProfileData(): Resource<UserProfile>

    suspend fun getOrders(): Resource<List<Order>>
} 