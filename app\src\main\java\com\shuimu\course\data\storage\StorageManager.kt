package com.shuimu.course.data.storage

import android.content.Context
import android.os.StatFs
import android.util.Log
import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.CacheRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import java.io.File
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 存储管理器
 * 负责存储空间检查、LRU缓存清理、文件完整性检查等
 */
@Singleton
class StorageManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val cacheRepository: CacheRepository
) {
    companion object {
        private const val TAG = "StorageManager"
        private const val DEFAULT_MAX_CACHE_SIZE_MB = 2048L // 2GB
        private const val MIN_FREE_SPACE_MB = 500L // 最小保留空间500MB
        private const val CACHE_DIR_NAME = "VideoCache"
    }
    
    private val cacheDir = File(context.getExternalFilesDir(null), CACHE_DIR_NAME)
    
    init {
        // 确保缓存目录存在
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
    }
    
    /**
     * 存储信息
     */
    data class StorageInfo(
        val totalSpaceBytes: Long,
        val freeSpaceBytes: Long,
        val usedSpaceBytes: Long,
        val cacheSpaceBytes: Long,
        val maxCacheSizeBytes: Long,
        val canDownload: Boolean,
        val needsCleanup: Boolean
    )
    
    /**
     * 文件完整性检查结果
     */
    data class IntegrityCheckResult(
        val isValid: Boolean,
        val expectedSize: Long,
        val actualSize: Long,
        val checksumMatch: Boolean = true
    )
    
    /**
     * 获取存储信息
     */
    suspend fun getStorageInfo(): StorageInfo {
        val statFs = StatFs(cacheDir.absolutePath)
        val totalSpace = statFs.totalBytes
        val freeSpace = statFs.availableBytes
        val usedSpace = totalSpace - freeSpace
        
        val cacheSpace = calculateCacheSize()
        val maxCacheSize = getMaxCacheSize()
        
        val canDownload = freeSpace > MIN_FREE_SPACE_MB * 1024 * 1024
        val needsCleanup = cacheSpace > maxCacheSize || freeSpace < MIN_FREE_SPACE_MB * 1024 * 1024
        
        return StorageInfo(
            totalSpaceBytes = totalSpace,
            freeSpaceBytes = freeSpace,
            usedSpaceBytes = usedSpace,
            cacheSpaceBytes = cacheSpace,
            maxCacheSizeBytes = maxCacheSize,
            canDownload = canDownload,
            needsCleanup = needsCleanup
        )
    }
    
    /**
     * 检查是否有足够空间下载
     */
    suspend fun hasEnoughSpaceForDownload(estimatedSizeBytes: Long): Boolean {
        val storageInfo = getStorageInfo()
        val requiredSpace = estimatedSizeBytes + MIN_FREE_SPACE_MB * 1024 * 1024
        
        return storageInfo.freeSpaceBytes >= requiredSpace
    }
    
    /**
     * 执行LRU缓存清理
     */
    suspend fun performLRUCleanup(targetSizeBytes: Long? = null): Result<CleanupResult> {
        return try {
            Log.d(TAG, "开始执行LRU缓存清理")
            
            val target = targetSizeBytes ?: getMaxCacheSize()
            val currentSize = calculateCacheSize()
            
            if (currentSize <= target) {
                Log.d(TAG, "当前缓存大小未超限，无需清理")
                return Result.success(CleanupResult(0, 0L, emptyList()))
            }
            
            val needToFree = currentSize - target
            Log.d(TAG, "需要释放空间: ${needToFree / 1024 / 1024}MB")
            
            // 获取所有已缓存的视频，按最后访问时间排序（LRU）
            val cachedVideos = cacheRepository.getCompletedCaches().first()
                .sortedBy { it.updatedAt } // 最久未访问的在前
            
            var freedSpace = 0L
            var deletedCount = 0
            val deletedVideos = mutableListOf<String>()
            
            for (video in cachedVideos) {
                if (freedSpace >= needToFree) break
                
                val fileSize = video.localPath?.let { File(it).length() } ?: 0L
                
                // 删除缓存文件和记录
                val deleteResult = deleteCacheFile(video.videoId)
                if (deleteResult.isSuccess) {
                    freedSpace += fileSize
                    deletedCount++
                    deletedVideos.add(video.videoId)
                    Log.d(TAG, "删除缓存: ${video.title}, 释放空间: ${fileSize / 1024 / 1024}MB")
                }
            }
            
            Log.d(TAG, "LRU清理完成: 删除${deletedCount}个文件, 释放${freedSpace / 1024 / 1024}MB空间")
            
            Result.success(CleanupResult(deletedCount, freedSpace, deletedVideos))
            
        } catch (e: Exception) {
            Log.e(TAG, "LRU清理失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 清理失效的缓存文件
     */
    suspend fun cleanupInvalidCaches(): Result<CleanupResult> {
        return try {
            Log.d(TAG, "开始清理失效缓存")
            
            var deletedCount = 0
            var freedSpace = 0L
            val deletedVideos = mutableListOf<String>()
            
            // 获取所有缓存记录
            val allCaches = cacheRepository.getAllCacheInfo().first()
            
            for (cache in allCaches) {
                val checkResult = checkFileIntegrity(cache)
                
                if (!checkResult.isValid) {
                    Log.d(TAG, "发现失效缓存: ${cache.title}")
                    
                    val fileSize = cache.localPath?.let { File(it).length() } ?: 0L
                    val deleteResult = deleteCacheFile(cache.videoId)
                    
                    if (deleteResult.isSuccess) {
                        freedSpace += fileSize
                        deletedCount++
                        deletedVideos.add(cache.videoId)
                    }
                }
            }
            
            // 清理孤儿文件（存在文件但没有数据库记录）
            val orphanFiles = findOrphanFiles()
            for (file in orphanFiles) {
                val fileSize = file.length()
                if (file.delete()) {
                    freedSpace += fileSize
                    deletedCount++
                    Log.d(TAG, "删除孤儿文件: ${file.name}")
                }
            }
            
            Log.d(TAG, "失效缓存清理完成: 删除${deletedCount}个文件, 释放${freedSpace / 1024 / 1024}MB空间")
            
            Result.success(CleanupResult(deletedCount, freedSpace, deletedVideos))
            
        } catch (e: Exception) {
            Log.e(TAG, "失效缓存清理失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 检查文件完整性
     */
    suspend fun checkFileIntegrity(cachedVideo: CachedVideo): IntegrityCheckResult {
        return try {
            val localPath = cachedVideo.localPath
            if (localPath.isNullOrEmpty()) {
                return IntegrityCheckResult(false, cachedVideo.totalBytes, 0L)
            }
            
            val file = File(localPath)
            if (!file.exists()) {
                return IntegrityCheckResult(false, cachedVideo.totalBytes, 0L)
            }
            
            val actualSize = file.length()
            val expectedSize = cachedVideo.totalBytes
            
            // 检查文件大小
            val sizeMatch = actualSize == expectedSize && actualSize > 0
            
            // 对于已完成的下载，文件大小必须匹配
            val isValid = if (cachedVideo.status == CacheStatus.DOWNLOADED) {
                sizeMatch
            } else {
                file.exists() && actualSize > 0
            }
            
            IntegrityCheckResult(
                isValid = isValid,
                expectedSize = expectedSize,
                actualSize = actualSize,
                checksumMatch = true // 暂时不做校验和检查
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "文件完整性检查失败: ${cachedVideo.videoId}", e)
            IntegrityCheckResult(false, 0L, 0L)
        }
    }
    
    /**
     * 删除缓存文件
     */
    suspend fun deleteCacheFile(videoId: String): Result<Unit> {
        return try {
            cacheRepository.deleteCacheInfo(videoId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 计算当前缓存大小
     */
    private fun calculateCacheSize(): Long {
        return try {
            cacheDir.walkTopDown()
                .filter { it.isFile }
                .map { it.length() }
                .sum()
        } catch (e: Exception) {
            Log.e(TAG, "计算缓存大小失败", e)
            0L
        }
    }
    
    /**
     * 获取最大缓存大小设置
     */
    private fun getMaxCacheSize(): Long {
        // 这里可以从SharedPreferences或其他配置源获取用户设置
        // 暂时使用默认值
        return DEFAULT_MAX_CACHE_SIZE_MB * 1024 * 1024
    }
    
    /**
     * 查找孤儿文件
     */
    private suspend fun findOrphanFiles(): List<File> {
        return try {
            val allCaches = cacheRepository.getAllCacheInfo().first()
            val validPaths = allCaches.mapNotNull { it.localPath }.toSet()
            
            cacheDir.walkTopDown()
                .filter { it.isFile }
                .filter { it.absolutePath !in validPaths }
                .toList()
        } catch (e: Exception) {
            Log.e(TAG, "查找孤儿文件失败", e)
            emptyList()
        }
    }
    
    /**
     * 清理结果
     */
    data class CleanupResult(
        val deletedCount: Int,
        val freedSpaceBytes: Long,
        val deletedVideoIds: List<String>
    )
    
    /**
     * 获取缓存目录
     */
    fun getCacheDirectory(): File = cacheDir
    
    /**
     * 格式化文件大小
     */
    fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / 1024 / 1024}MB"
            else -> "${bytes / 1024 / 1024 / 1024}GB"
        }
    }
    
    /**
     * 自动清理（当存储空间不足时）
     */
    suspend fun autoCleanupIfNeeded(): Result<CleanupResult> {
        val storageInfo = getStorageInfo()
        
        return if (storageInfo.needsCleanup) {
            Log.d(TAG, "存储空间不足，开始自动清理")
            
            // 先清理失效缓存
            val invalidCleanup = cleanupInvalidCaches().getOrNull()
            
            // 如果还需要更多空间，执行LRU清理
            val updatedStorageInfo = getStorageInfo()
            if (updatedStorageInfo.needsCleanup) {
                val lruCleanup = performLRUCleanup().getOrNull()
                
                // 合并清理结果
                val totalDeleted = (invalidCleanup?.deletedCount ?: 0) + (lruCleanup?.deletedCount ?: 0)
                val totalFreed = (invalidCleanup?.freedSpaceBytes ?: 0L) + (lruCleanup?.freedSpaceBytes ?: 0L)
                val totalVideoIds = (invalidCleanup?.deletedVideoIds ?: emptyList()) + (lruCleanup?.deletedVideoIds ?: emptyList())
                
                Result.success(CleanupResult(totalDeleted, totalFreed, totalVideoIds))
            } else {
                Result.success(invalidCleanup ?: CleanupResult(0, 0L, emptyList()))
            }
        } else {
            Result.success(CleanupResult(0, 0L, emptyList()))
        }
    }
}
