package com.shuimu.course.presentation.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.components.base.ShuimuCard
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.CacheManagerViewModel
import com.shuimu.course.presentation.viewmodel.CachedVideoItem

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CacheManagerScreen(
    navController: androidx.navigation.NavController? = null,
    viewModel: CacheManagerViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()

    Scaffold(
        topBar = { TopAppBar(title = { Text("缓存管理") }) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // 存储信息卡片
            state.storageInfo?.let { storageInfo ->
                EnhancedStorageInfoCard(
                    storageInfo = storageInfo,
                    totalCacheSizeMb = state.totalSizeMb,
                    onCleanupClick = { viewModel.cleanupCache() }
                )
            } ?: StorageInfoCard(totalSizeMb = state.totalSizeMb)

            Spacer(modifier = Modifier.height(16.dp))

            // 错误统计信息
            state.errorStatistics?.let { errorStats ->
                if (errorStats.totalErrors > 0) {
                    ErrorStatisticsCard(
                        errorStatistics = errorStats,
                        onBatchRetryClick = { viewModel.batchRetryFailedDownloads() }
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "已缓存视频 (${state.groupedVideos.values.flatten().size}个)",
                    style = MaterialTheme.typography.titleMedium
                )
                Row {
                    Button(
                        onClick = { viewModel.cleanupCache() },
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.secondary)
                    ) {
                        Text("清理")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = { viewModel.deleteAllCache() },
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
                    ) {
                        Text("清空全部")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))

            LazyColumn(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                state.groupedVideos.forEach { (seriesTitle, videos) ->
                    item {
                        Text(
                            text = seriesTitle,
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                    items(videos) { videoItem ->
                        EnhancedCachedVideoRow(
                            videoItem = videoItem,
                            onDelete = { viewModel.deleteVideoCache(it) },
                            onRetry = { viewModel.retryFailedDownload(it) },
                            onPriorityChange = { videoId, priority ->
                                viewModel.setDownloadPriority(videoId, priority)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun StorageInfoCard(totalSizeMb: Double) {
    ShuimuCard {
        Text("存储空间", style = MaterialTheme.typography.titleMedium)
        Spacer(Modifier.height(8.dp))
        // Placeholder for storage bar
        val progressFraction = ((totalSizeMb / 1024.0).coerceIn(0.0, 1.0)).toFloat()
        LinearProgressIndicator(
            progress = progressFraction, // Assuming 1GB total for preview
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(Modifier.height(8.dp))
        Text(String.format("已用空间: %.2f MB", totalSizeMb))
    }
}

@Composable
private fun EnhancedStorageInfoCard(
    storageInfo: com.shuimu.course.data.storage.StorageManager.StorageInfo,
    totalCacheSizeMb: Double,
    onCleanupClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "存储空间",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                if (storageInfo.needsCleanup) {
                    Button(
                        onClick = onCleanupClick,
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.secondary),
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("清理", fontSize = 12.sp)
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 存储空间进度条
            val usedPercentage = (storageInfo.usedSpaceBytes.toFloat() / storageInfo.totalSpaceBytes.toFloat())
            LinearProgressIndicator(
                progress = usedPercentage,
                modifier = Modifier.fillMaxWidth().height(8.dp),
                color = if (storageInfo.needsCleanup) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 存储信息详情
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "缓存: ${String.format("%.1f", totalCacheSizeMb)}MB",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "可用: ${storageInfo.freeSpaceBytes / 1024 / 1024}MB",
                    style = MaterialTheme.typography.bodySmall,
                    color = if (storageInfo.canDownload) Color.Green else MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

@Composable
private fun ErrorStatisticsCard(
    errorStatistics: com.shuimu.course.data.error.DownloadErrorHandler.ErrorStatistics,
    onBatchRetryClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "下载错误统计",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
                if (errorStatistics.retryableErrors > 0) {
                    Button(
                        onClick = onBatchRetryClick,
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary),
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("批量重试", fontSize = 12.sp)
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "总错误: ${errorStatistics.totalErrors}个, 可重试: ${errorStatistics.retryableErrors}个",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
        }
    }
}

@Composable
private fun EnhancedCachedVideoRow(
    videoItem: CachedVideoItem,
    onDelete: (String) -> Unit,
    onRetry: (String) -> Unit,
    onPriorityChange: (String, Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = videoItem.title,
                        style = MaterialTheme.typography.bodyLarge,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = String.format("%.1f MB", videoItem.sizeMb),
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        // 状态指示器
                        StatusIndicator(status = videoItem.status, progress = videoItem.progress)

                        if (videoItem.priority > 50) {
                            Spacer(modifier = Modifier.width(4.dp))
                            Icon(
                                Icons.Default.Star,
                                contentDescription = "高优先级",
                                tint = Color(0xFFFFD700),
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }

                    // 错误信息
                    if (!videoItem.errorMessage.isNullOrEmpty()) {
                        Text(
                            text = "错误: ${videoItem.errorMessage}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.error,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }

                    // 进度条（下载中时显示）
                    if (videoItem.status == com.shuimu.course.domain.model.CacheStatus.DOWNLOADING) {
                        Spacer(modifier = Modifier.height(4.dp))
                        LinearProgressIndicator(
                            progress = videoItem.progress / 100f,
                            modifier = Modifier.fillMaxWidth().height(4.dp)
                        )
                    }
                }

                // 操作按钮
                Row {
                    if (videoItem.canRetry) {
                        IconButton(onClick = { onRetry(videoItem.videoId) }) {
                            Icon(
                                Icons.Default.Refresh,
                                contentDescription = "重试",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }

                    IconButton(onClick = { onDelete(videoItem.videoId) }) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun StatusIndicator(
    status: com.shuimu.course.domain.model.CacheStatus,
    progress: Int
) {
    val (text, color) = when (status) {
        com.shuimu.course.domain.model.CacheStatus.DOWNLOADED -> "已完成" to Color.Green
        com.shuimu.course.domain.model.CacheStatus.DOWNLOADING -> "${progress}%" to Color.Blue
        com.shuimu.course.domain.model.CacheStatus.PENDING -> "等待中" to Color.Orange
        com.shuimu.course.domain.model.CacheStatus.PAUSED -> "已暂停" to Color.Gray
        com.shuimu.course.domain.model.CacheStatus.FAILED -> "失败" to Color.Red
        com.shuimu.course.domain.model.CacheStatus.WAITING_NETWORK -> "等待网络" to Color(0xFFFF5722)
        com.shuimu.course.domain.model.CacheStatus.NOT_CACHED -> "未缓存" to Color.Gray
    }

    Text(
        text = text,
        style = MaterialTheme.typography.bodySmall,
        color = color,
        fontWeight = FontWeight.Medium
    )
}

@Composable
private fun CachedVideoRow(videoItem: CachedVideoItem, onDelete: (String) -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(videoItem.title, style = MaterialTheme.typography.bodyLarge)
            Text(
                text = String.format("%.2f MB", videoItem.sizeMb),
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
        IconButton(onClick = { onDelete(videoItem.videoId) }) {
            Icon(Icons.Default.Delete, contentDescription = "Delete cache", tint = MaterialTheme.colorScheme.error)
        }
    }
}

@Preview
@Composable
fun CacheManagerScreenPreview() {
    ShuimuCourseTheme {
        CacheManagerScreen()
    }
} 