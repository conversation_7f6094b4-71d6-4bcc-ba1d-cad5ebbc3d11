package com.shuimu.course.presentation.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.components.base.ShuimuCard
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.CacheManagerViewModel
import com.shuimu.course.presentation.viewmodel.CachedVideoItem

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CacheManagerScreen(
    navController: androidx.navigation.NavController? = null,
    viewModel: CacheManagerViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()

    Scaffold(
        topBar = { TopAppBar(title = { Text("缓存管理") }) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // 存储信息卡片（暂时使用简化版本）
            StorageInfoCard(totalSizeMb = state.totalSizeMb)

            Spacer(modifier = Modifier.height(16.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "已缓存视频 (${state.groupedVideos.values.flatten().size}个)",
                    style = MaterialTheme.typography.titleMedium
                )
                Row {
                    Button(
                        onClick = { viewModel.cleanupCache() },
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.secondary)
                    ) {
                        Text("清理")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = { viewModel.deleteAllCache() },
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
                    ) {
                        Text("清空全部")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))

            LazyColumn(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                state.groupedVideos.forEach { (seriesTitle, videos) ->
                    item {
                        Text(
                            text = seriesTitle,
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                    items(videos) { videoItem ->
                        CachedVideoRow(videoItem = videoItem, onDelete = { viewModel.deleteVideoCache(it) })
                    }
                }
            }
        }
    }
}

@Composable
private fun StorageInfoCard(totalSizeMb: Double) {
    ShuimuCard {
        Text("存储空间", style = MaterialTheme.typography.titleMedium)
        Spacer(Modifier.height(8.dp))
        // Placeholder for storage bar
        val progressFraction = ((totalSizeMb / 1024.0).coerceIn(0.0, 1.0)).toFloat()
        LinearProgressIndicator(
            progress = progressFraction, // Assuming 1GB total for preview
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(Modifier.height(8.dp))
        Text(String.format("已用空间: %.2f MB", totalSizeMb))
    }
}

// 暂时注释掉这些组件，因为依赖的类还有编译问题
/*
@Composable
private fun EnhancedStorageInfoCard(...) { ... }

@Composable
private fun ErrorStatisticsCard(...) { ... }

@Composable
private fun EnhancedCachedVideoRow(...) { ... }

@Composable
private fun StatusIndicator(...) { ... }
*/

@Composable
private fun CachedVideoRow(videoItem: CachedVideoItem, onDelete: (String) -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(videoItem.title, style = MaterialTheme.typography.bodyLarge)
            Text(
                text = String.format("%.2f MB", videoItem.sizeMb),
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
        IconButton(onClick = { onDelete(videoItem.videoId) }) {
            Icon(Icons.Default.Delete, contentDescription = "Delete cache", tint = MaterialTheme.colorScheme.error)
        }
    }
}

@Preview
@Composable
fun CacheManagerScreenPreview() {
    ShuimuCourseTheme {
        CacheManagerScreen()
    }
} 