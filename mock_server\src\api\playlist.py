from fastapi import APIRouter
from typing import List
from ..models.playlist import PlaylistItem

router = APIRouter()

# Mock playlist data
PLAYLIST_DATA = [
    PlaylistItem(
        id="1",
        title="01. 恋爱心理学基础",
        duration="12:30",
        isPlaying=True,
        thumbnailUrl="https://example.com/thumb1.jpg"
    ),
    PlaylistItem(
        id="2", 
        title="02. 初次见面技巧",
        duration="15:45", 
        isPlaying=False,
        thumbnailUrl="https://example.com/thumb2.jpg"
    ),
    PlaylistItem(
        id="3",
        title="03. 深度对话艺术",
        duration="18:20",
        isPlaying=False,
        thumbnailUrl="https://example.com/thumb3.jpg"
    ),
    PlaylistItem(
        id="4",
        title="04. 肢体语言解读",
        duration="14:10",
        isPlaying=False,
        thumbnailUrl="https://example.com/thumb4.jpg"
    ),
    PlaylistItem(
        id="5",
        title="05. 情感表达技巧",
        duration="16:55",
        isPlaying=False,
        thumbnailUrl="https://example.com/thumb5.jpg"
    )
]

@router.get("/playlist", response_model=List[PlaylistItem])
def get_playlist():
    """获取当前播放列表"""
    return PLAYLIST_DATA

@router.get("/playlist/{video_id}")
def get_playlist_for_video(video_id: str):
    """获取指定视频的播放列表"""
    # 简单模拟：返回相同的播放列表，但标记当前播放的视频
    playlist = []
    for item in PLAYLIST_DATA:
        playlist_item = PlaylistItem(
            id=item.id,
            title=item.title, 
            duration=item.duration,
            isPlaying=(item.id == video_id),
            thumbnailUrl=item.thumbnailUrl
        )
        playlist.append(playlist_item)
    return playlist 