[{"id": "free-dating-prep", "title": "01. 约会前的准备工作", "description": "学习约会前的各种准备工作，包括形象打理、心理准备等。", "duration": 930, "categoryId": "free-dating-tips", "playCount": 12000, "watch_count": 3, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video1.mp4"}, {"id": "free-dating-location", "title": "02. 约会地点的选择", "description": "如何选择合适的约会地点，营造浪漫氛围。", "duration": 765, "categoryId": "free-dating-tips", "playCount": 8500, "watch_count": 2, "cache_status": "CACHED", "progress": 0.65, "cloudUrl": "https://example.com/videos/video2.mp4"}, {"id": "free-pickup-mindset", "title": "01. 搭讪基础心态", "description": "建立正确的搭讪心态，克服内心恐惧。", "duration": 1100, "categoryId": "free-pickup-skills", "playCount": 21000, "watch_count": 1, "cache_status": "DOWNLOADING", "progress": 0.0, "cloudUrl": "https://example.com/videos/video3.mp4"}, {"id": "free-pickup-opening", "title": "02. 自然开场技巧", "description": "学习自然而然的开场白技巧。", "duration": 975, "categoryId": "free-pickup-skills", "playCount": 18000, "watch_count": 5, "cache_status": "CACHED", "progress": 1.0, "cloudUrl": "https://example.com/videos/video4.mp4"}, {"id": "free-pickup-anxiety", "title": "03. 克服紧张情绪", "description": "有效方法帮助你克服搭讪时的紧张情绪。", "duration": 870, "categoryId": "free-pickup-skills", "playCount": 15000, "watch_count": 2, "cache_status": "NOT_CACHED", "progress": 0.25, "cloudUrl": "https://example.com/videos/video5.mp4"}, {"id": "love-guide-1-attraction", "title": "01. 初识吸引力法则", "description": "深入了解吸引力的本质和运作原理。", "duration": 1350, "categoryId": "love-guide-1", "playCount": 956, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video6.mp4"}, {"id": "love-guide-1-confidence", "title": "02. 建立自信的方法", "description": "系统性方法帮助你建立内在自信。", "duration": 1185, "categoryId": "love-guide-1", "playCount": 1200, "watch_count": 1, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video7.mp4"}, {"id": "love-guide-1-first-impression", "title": "03. 第一印象的重要性", "description": "如何在初次见面时留下深刻的好印象。", "duration": 1040, "categoryId": "love-guide-1", "playCount": 2300, "watch_count": 2, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video8.mp4"}, {"id": "love-guide-1-body-language", "title": "04. 肢体语言的艺术", "description": "掌握肢体语言在恋爱中的重要作用。", "duration": 1275, "categoryId": "love-guide-1", "playCount": 3100, "watch_count": 3, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video11.mp4"}, {"id": "love-guide-1-emotional-intelligence", "title": "05. 情商提升技巧", "description": "提高情商，更好地理解和处理情感。", "duration": 1110, "categoryId": "love-guide-1", "playCount": 4500, "watch_count": 4, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video12.mp4"}, {"id": "love-guide-1-conversation-skills", "title": "06. 对话技巧进阶", "description": "掌握高级对话技巧，让聊天更有趣。", "duration": 1425, "categoryId": "love-guide-1", "playCount": 5200, "watch_count": 5, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video13.mp4"}, {"id": "love-guide-1-date-planning", "title": "07. 约会策划大师", "description": "学会策划完美的约会，留下美好回忆。", "duration": 1220, "categoryId": "love-guide-1", "playCount": 6800, "watch_count": 6, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video14.mp4"}, {"id": "love-guide-1-relationship-building", "title": "08. 关系建立与维护", "description": "建立稳固的恋爱关系并长期维护。", "duration": 1510, "categoryId": "love-guide-1", "playCount": 7300, "watch_count": 7, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video15.mp4"}, {"id": "love-guide-1-conflict-resolution", "title": "09. 冲突解决智慧", "description": "学会处理恋爱中的矛盾和冲突。", "duration": 1170, "categoryId": "love-guide-1", "playCount": 8900, "watch_count": 8, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video16.mp4"}, {"id": "love-guide-1-commitment", "title": "10. 承诺与责任", "description": "学会在恋爱中承担责任和承诺。", "duration": 1305, "categoryId": "love-guide-1", "playCount": 9400, "watch_count": 9, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video17.mp4"}, {"id": "love-guide-1-future-planning", "title": "11. 未来规划共识", "description": "与伴侣共同规划美好的未来。", "duration": 1470, "categoryId": "love-guide-1", "playCount": 11000, "watch_count": 10, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://example.com/videos/video18.mp4"}, {"id": "love-guide-2-deep-communication", "title": "01. 深度沟通技巧", "description": "进阶的深度沟通技巧和方法。", "duration": 1575, "categoryId": "love-guide-2", "playCount": 18000, "watch_count": 3, "cache_status": "CACHED", "progress": 0.8, "cloudUrl": "https://example.com/videos/video9.mp4"}, {"id": "chat-tech-1-opening", "title": "01. 开场白技巧", "description": "掌握各种场合的开场白技巧。", "duration": 1215, "categoryId": "chat-tech-1", "playCount": 32000, "watch_count": 2, "cache_status": "DOWNLOADING", "progress": 0.3, "cloudUrl": "https://example.com/videos/video10.mp4"}, {"id": "love-guide-1-demo-0", "title": "01. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 1", "duration": 1200, "categoryId": "love-guide-1", "playCount": 0, "watch_count": 0, "cache_status": "NOT_CACHED", "progress": 0.0, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-1", "title": "02. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 2", "duration": 1265, "categoryId": "love-guide-1", "playCount": 89, "watch_count": 1, "cache_status": "NOT_CACHED", "progress": 0.1, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-2", "title": "03. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 3", "duration": 1330, "categoryId": "love-guide-1", "playCount": 156, "watch_count": 2, "cache_status": "NOT_CACHED", "progress": 0.2, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-3", "title": "04. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 4", "duration": 1395, "categoryId": "love-guide-1", "playCount": 234, "watch_count": 3, "cache_status": "NOT_CACHED", "progress": 0.3, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-4", "title": "05. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 5", "duration": 1460, "categoryId": "love-guide-1", "playCount": 312, "watch_count": 4, "cache_status": "NOT_CACHED", "progress": 0.4, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-5", "title": "06. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 6", "duration": 1525, "categoryId": "love-guide-1", "playCount": 445, "watch_count": 5, "cache_status": "NOT_CACHED", "progress": 0.5, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-6", "title": "07. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 7", "duration": 1590, "categoryId": "love-guide-1", "playCount": 523, "watch_count": 6, "cache_status": "NOT_CACHED", "progress": 0.6, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-7", "title": "08. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 8", "duration": 1655, "categoryId": "love-guide-1", "playCount": 678, "watch_count": 7, "cache_status": "NOT_CACHED", "progress": 0.7, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-8", "title": "09. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 9", "duration": 1720, "categoryId": "love-guide-1", "playCount": 789, "watch_count": 8, "cache_status": "NOT_CACHED", "progress": 0.8, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-9", "title": "10. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 10", "duration": 1785, "categoryId": "love-guide-1", "playCount": 834, "watch_count": 9, "cache_status": "NOT_CACHED", "progress": 0.9, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-10", "title": "11. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 11", "duration": 1850, "categoryId": "love-guide-1", "playCount": 945, "watch_count": 10, "cache_status": "NOT_CACHED", "progress": 1.0, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}]