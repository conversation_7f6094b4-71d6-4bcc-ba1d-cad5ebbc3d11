import json
from pathlib import Path
from typing import Dict, Any, List, Optional

# Base directory for JSON data
DATA_DIR = Path(__file__).resolve().parent.parent / "data"

def validate_user_data_format(data: Dict[str, Any]) -> Dict[str, Any]:
    """验证新格式用户数据"""
    
    # 1. 确保基本字段存在
    if "userId" not in data:
        data["userId"] = "anonymous"
    
    if "purchases" not in data:
        data["purchases"] = {"series": [], "categories": []}
    
    # 2. 验证 watchProgress 必须是 Map 格式
    watch_progress = data.get("watchProgress", {})
    if not isinstance(watch_progress, dict):
        raise ValueError(f"watchProgress 必须是 dict 格式，当前是 {type(watch_progress)}")
    
    # 3. 验证 videoCaches 必须是 Map 格式
    video_caches = data.get("videoCaches", {})
    if not isinstance(video_caches, dict):
        raise ValueError(f"videoCaches 必须是 dict 格式，当前是 {type(video_caches)}")
    
    # 4. 确保版本标识
    data["schemaVersion"] = 1
    
    return data

def load_user_data(user_id: str) -> Dict[str, Any]:
    """加载用户个人化数据 - 只支持新格式"""
    user_file = DATA_DIR / "user_data" / f"{user_id}.json"
    
    if not user_file.exists():
        # 如果用户文件不存在，尝试加载匿名访客数据作为默认模板
        anonymous_file = DATA_DIR / "user_data" / "匿名访客.json"
        if anonymous_file.exists():
            with open(anonymous_file, "r", encoding="utf-8") as f:
                anonymous_data = json.load(f)
                anonymous_data["userId"] = user_id
                return validate_user_data_format(anonymous_data)
        
        # 返回默认新格式数据
        return {
            "schemaVersion": 1,
            "userId": user_id,
            "purchases": {"series": [], "categories": []},
            "favorites": {"videos": [], "categories": []},
            "watchProgress": {},
            "videoCaches": {}
        }
    
    with open(user_file, "r", encoding="utf-8") as f:
        data = json.load(f)
        return validate_user_data_format(data)

def save_user_data(user_id: str, data: Dict[str, Any]) -> None:
    """保存用户个人化数据"""
    user_file = DATA_DIR / "user_data" / f"{user_id}.json"
    user_file.parent.mkdir(exist_ok=True)
    
    with open(user_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def get_user_purchases(user_id: str) -> List[str]:
    """获取用户购买记录"""
    user_data = load_user_data(user_id)
    purchases = user_data.get("purchases", {})
    
    # 提取购买记录的ID
    all_purchases = []
    
    # 处理series购买记录（可能是字符串列表或对象列表）
    for item in purchases.get("series", []):
        if isinstance(item, str):
            all_purchases.append(item)
        elif isinstance(item, dict) and "id" in item:
            all_purchases.append(item["id"])
    
    # 处理categories购买记录（可能是字符串列表或对象列表）
    for item in purchases.get("categories", []):
        if isinstance(item, str):
            all_purchases.append(item)
        elif isinstance(item, dict) and "id" in item:
            all_purchases.append(item["id"])
    
    return all_purchases

def get_video_watch_progress(user_id: str, video_id: str) -> Dict[str, Any]:
    """获取视频观看进度"""
    user_data = load_user_data(user_id)
    watch_progress = user_data.get("watchProgress", {})
    
    # 🔥 使用 Map 解析，直接通过 key 读取
    if isinstance(watch_progress, dict) and video_id in watch_progress:
        return watch_progress[video_id]
    
    # 如果没有观看记录，返回默认值
    return {
        "videoId": video_id,
        "position": 0,
        "duration": 0,
        "watchCount": 0
    }

def get_video_cache_status(user_id: str, video_id: str) -> Dict[str, Any]:
    """获取视频缓存状态"""
    user_data = load_user_data(user_id)
    video_caches = user_data.get("videoCaches", {})
    
    # 🔥 使用 Map 解析，直接通过 key 读取
    if isinstance(video_caches, dict) and video_id in video_caches:
        cache_info = video_caches[video_id]
        return {
            "videoId": video_id,
            "isCached": cache_info.get("status") == "CACHED",
            "localPath": cache_info.get("localPath"),
            "size": cache_info.get("size"),
            "updatedAt": cache_info.get("updatedAt")
        }
    
    # 如果没有缓存记录，返回默认值（未缓存）
    return {
        "videoId": video_id,
        "isCached": False,
        "localPath": None,
        "size": 0,
        "updatedAt": None
    }

def update_video_watch_progress(user_id: str, video_id: str, position: int, duration: int, watch_count: int = None) -> None:
    """更新视频观看进度"""
    user_data = load_user_data(user_id)
    watch_progress = user_data.get("watchProgress", {})
    
    # 查找现有记录
    if isinstance(watch_progress, dict) and video_id in watch_progress:
        watch_progress[video_id] = {
            "videoId": video_id,
            "position": position,
            "duration": duration,
            "watchCount": watch_count if watch_count is not None else watch_progress[video_id].get("watchCount", 0)
        }
    else:
        # 没有找到，添加新记录
        watch_progress[video_id] = {
            "videoId": video_id,
            "position": position,
            "duration": duration,
            "watchCount": watch_count if watch_count is not None else 1
        }
    
    user_data["watchProgress"] = watch_progress
    save_user_data(user_id, user_data)

def update_video_cache_status(user_id: str, video_id: str, is_cached: bool, local_path: str = None) -> None:
    """更新视频缓存状态"""
    user_data = load_user_data(user_id)
    video_caches = user_data.get("videoCaches", {})
    
    # 查找现有记录
    if isinstance(video_caches, dict) and video_id in video_caches:
        if is_cached:
            video_caches[video_id] = {
                "videoId": video_id,
                "isCached": True,
                "localPath": local_path,
                "status": "CACHED",
                "size": 0,
                "updatedAt": None
            }
        else:
            # 删除缓存记录
            video_caches.pop(video_id)
    else:
        # 没有找到，如果是添加缓存则新增记录
        if is_cached:
            video_caches[video_id] = {
                "videoId": video_id,
                "isCached": True,
                "localPath": local_path,
                "status": "CACHED",
                "size": 0,
                "updatedAt": None
            }
    
    user_data["videoCaches"] = video_caches
    save_user_data(user_id, user_data)

def calculate_category_progress(user_id: str, video_ids: List[str]) -> float:
    """计算分类的观看进度"""
    if not video_ids:
        return 0.0
    
    total_progress = 0.0
    for video_id in video_ids:
        progress_data = get_video_watch_progress(user_id, video_id)
        if progress_data.get("duration", 0) > 0:
            video_progress = progress_data.get("position", 0) / progress_data.get("duration", 1)
            total_progress += min(video_progress, 1.0)  # 限制在1.0以内
    
    return total_progress / len(video_ids)

def calculate_category_watch_count(user_id: str, video_ids: List[str]) -> int:
    """计算分类的总观看次数"""
    total_watch_count = 0
    for video_id in video_ids:
        progress_data = get_video_watch_progress(user_id, video_id)
        total_watch_count += progress_data.get("watchCount", 0)
    
    return total_watch_count 