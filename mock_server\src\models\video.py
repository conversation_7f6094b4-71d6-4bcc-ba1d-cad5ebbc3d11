from pydantic import BaseModel
from typing import Optional

class Video(BaseModel):
    id: str
    title: str
    description: str
    duration: int  # 视频总时长，单位：秒
    categoryId: Optional[str] = None
    playCount: Optional[int] = None  # 全局播放量（所有用户观看总次数）
    watch_count: int = 0  # 个人观看次数 (0-10次，用于徽章等级计算)
    cache_status: str = 'NOT_CACHED'
    progress: float = 0.0  # 观看进度 (0.0-1.0)
    cloudUrl: str
    localPath: Optional[str] = None
