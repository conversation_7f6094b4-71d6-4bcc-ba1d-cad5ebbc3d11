package com.shuimu.course.presentation.ui.components.user

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

// Note: In a real app, you'd use an image loading library like Coil or Glide.
// For this component, we'll use a placeholder if no painter is provided.

@Composable
fun UserAvatar(
    modifier: Modifier = Modifier,
    // painter: Painter? = null, // In real app
    avatarUrl: String?,
    size: Dp = 48.dp
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape)
            .background(Color.LightGray),
        contentAlignment = Alignment.Center
    ) {
        if (avatarUrl.isNullOrEmpty()) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "Default Avatar",
                tint = Color.White,
                modifier = Modifier.size(size * 0.6f)
            )
        } else {
            // Placeholder for an image loading library
            Image(
                imageVector = Icons.Default.Person, // Replace with loaded image painter
                contentDescription = "User Avatar",
                contentScale = ContentScale.Crop,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@Preview(name = "User Avatar with Image")
@Composable
fun UserAvatarWithImagePreview() {
    ShuimuCourseTheme {
        UserAvatar(avatarUrl = "https://example.com/avatar.jpg")
    }
}

@Preview(name = "User Avatar without Image")
@Composable
fun UserAvatarDefaultPreview() {
    ShuimuCourseTheme {
        UserAvatar(avatarUrl = null)
    }
} 