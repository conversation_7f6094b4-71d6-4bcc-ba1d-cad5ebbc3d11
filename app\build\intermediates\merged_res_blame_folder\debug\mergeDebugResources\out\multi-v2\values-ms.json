{"logs": [{"outputFile": "com.shuimu.course.app-mergeDebugResources-85:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\886ce1740e58ab542309752b74bbc803\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1028,1133,1241,1328,1432,1543,1622,1700,1791,1884,1979,2073,2171,2264,2359,2453,2544,2635,2715,2827,2935,3032,3141,3245,3352,3511,21800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "1023,1128,1236,1323,1427,1538,1617,1695,1786,1879,1974,2068,2166,2259,2354,2448,2539,2630,2710,2822,2930,3027,3136,3240,3347,3506,3607,21876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60456bf525e41666fc62eb9c2882cd30\\transformed\\jetified-media3-session-1.3.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,208,275,341,414,490,589", "endColumns": "73,78,66,65,72,75,98,102", "endOffsets": "124,203,270,336,409,485,584,687"}, "to": {"startLines": "50,69,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,5426,17228,17295,17361,17434,17510,17609", "endColumns": "73,78,66,65,72,75,98,102", "endOffsets": "3681,5500,17290,17356,17429,17505,17604,17707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\98772e44dd82187f64bd43b721368829\\transformed\\jetified-media3-exoplayer-1.3.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7753,7823,7887,7953,8018,8096,8162,8252,8335", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "7818,7882,7948,8013,8091,8157,8247,8330,8407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad952cb150bedcbfcb57f35849ccf9e8\\transformed\\jetified-foundation-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,90", "endOffsets": "137,228"}, "to": {"startLines": "269,270", "startColumns": "4,4", "startOffsets": "22779,22866", "endColumns": "86,90", "endOffsets": "22861,22952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60558d3ccad013c87c458ddc43ef9e3a\\transformed\\jetified-ui-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,997,1083,1155,1232,1305,1378,1454,1520", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,992,1078,1150,1227,1300,1373,1449,1515,1635"}, "to": {"startLines": "66,67,68,70,71,128,129,252,253,255,256,260,262,263,264,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5156,5246,5330,5505,5607,9763,9846,21371,21458,21629,21714,22044,22193,22270,22343,22517,22593,22659", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "5241,5325,5421,5602,5687,9841,9936,21453,21538,21709,21795,22111,22265,22338,22411,22588,22654,22774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ed624b17e998c266cfbf9ad645cdecae\\transformed\\jetified-material3-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,294,408,527,627,732,854,1004,1132,1280,1366,1466,1558,1656,1772,1898,2003,2141,2276,2408,2587,2712,2837,2965,3094,3187,3288,3409,3537,3638,3745,3851,3992,4138,4245,4344,4420,4518,4616,4718,4805,4894,4996,5076,5159,5258,5357,5454,5557,5644,5747,5846,5953,6075,6156,6262", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "170,289,403,522,622,727,849,999,1127,1275,1361,1461,1553,1651,1767,1893,1998,2136,2271,2403,2582,2707,2832,2960,3089,3182,3283,3404,3532,3633,3740,3846,3987,4133,4240,4339,4415,4513,4611,4713,4800,4889,4991,5071,5154,5253,5352,5449,5552,5639,5742,5841,5948,6070,6151,6257,6353"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10000,10120,10239,10353,10472,10572,10677,10799,10949,11077,11225,11311,11411,11503,11601,11717,11843,11948,12086,12221,12353,12532,12657,12782,12910,13039,13132,13233,13354,13482,13583,13690,13796,13937,14083,14190,14289,14365,14463,14561,14663,14750,14839,14941,15021,15104,15203,15302,15399,15502,15589,15692,15791,15898,16020,16101,16207", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "10115,10234,10348,10467,10567,10672,10794,10944,11072,11220,11306,11406,11498,11596,11712,11838,11943,12081,12216,12348,12527,12652,12777,12905,13034,13127,13228,13349,13477,13578,13685,13791,13932,14078,14185,14284,14360,14458,14556,14658,14745,14834,14936,15016,15099,15198,15297,15394,15497,15584,15687,15786,15893,16015,16096,16202,16298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\c12dcf7643131f54deb56aa398bbca93\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "56,57,58,59,60,61,62,265", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4111,4206,4308,4405,4515,4621,4739,22416", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "4201,4303,4400,4510,4616,4734,4849,22512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\50a9f4044b9bccbf0a8c519c38d7ae77\\transformed\\jetified-media3-ui-1.3.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1774,1883,1988,2055,2137,2207,2278,2362,2447,2514,2577,2630,2688,2736,2797,2861,2923,2984,3050,3113,3172,3238,3302,3368,3420,3482,3558,3634,3696", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61,73", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1769,1878,1983,2050,2132,2202,2273,2357,2442,2509,2572,2625,2683,2731,2792,2856,2918,2979,3045,3108,3167,3233,3297,3363,3415,3477,3553,3629,3691,3765"}, "to": {"startLines": "2,11,15,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,566,5819,5902,5986,6063,6154,6247,6320,6389,6485,6579,6643,6706,6771,6844,6950,7059,7164,7231,7313,7383,7454,7538,7623,7690,8412,8465,8523,8571,8632,8696,8758,8819,8885,8948,9007,9073,9137,9203,9255,9317,9393,9469,9531", "endLines": "10,14,18,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61,73", "endOffsets": "374,561,733,5897,5981,6058,6149,6242,6315,6384,6480,6574,6638,6701,6766,6839,6945,7054,7159,7226,7308,7378,7449,7533,7618,7685,7748,8460,8518,8566,8627,8691,8753,8814,8880,8943,9002,9068,9132,9198,9250,9312,9388,9464,9526,9600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad601f1f1ae6fb89202723eea59af52d\\transformed\\material-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1068,1133,1226,1291,1350,1437,1499,1561,1621,1687,1749,1803,1911,1968,2029,2084,2155,2275,2366,2443,2540,2625,2711,2859,2945,3031,3159,3247,3325,3378,3429,3495,3566,3644,3715,3794,3867,3943,4016,4087,4194,4286,4359,4449,4542,4616,4687,4778,4830,4910,4978,5062,5147,5209,5273,5336,5408,5512,5620,5716,5822,5879,5934,6020,6105,6183", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "274,354,433,520,612,699,802,918,1001,1063,1128,1221,1286,1345,1432,1494,1556,1616,1682,1744,1798,1906,1963,2024,2079,2150,2270,2361,2438,2535,2620,2706,2854,2940,3026,3154,3242,3320,3373,3424,3490,3561,3639,3710,3789,3862,3938,4011,4082,4189,4281,4354,4444,4537,4611,4682,4773,4825,4905,4973,5057,5142,5204,5268,5331,5403,5507,5615,5711,5817,5874,5929,6015,6100,6178,6255"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,72,73,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,254,258,259,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "738,3686,3766,3845,3932,4024,4854,4957,5073,5692,5754,9605,9698,9941,16303,16390,16452,16514,16574,16640,16702,16756,16864,16921,16982,17037,17108,17712,17803,17880,17977,18062,18148,18296,18382,18468,18596,18684,18762,18815,18866,18932,19003,19081,19152,19231,19304,19380,19453,19524,19631,19723,19796,19886,19979,20053,20124,20215,20267,20347,20415,20499,20584,20646,20710,20773,20845,20949,21057,21153,21259,21316,21543,21881,21966,22116", "endLines": "22,51,52,53,54,55,63,64,65,72,73,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,254,258,259,261", "endColumns": "12,79,78,86,91,86,102,115,82,61,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,76,96,84,85,147,85,85,127,87,77,52,50,65,70,77,70,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85,84,77,76", "endOffsets": "912,3761,3840,3927,4019,4106,4952,5068,5151,5749,5814,9693,9758,9995,16385,16447,16509,16569,16635,16697,16751,16859,16916,16977,17032,17103,17223,17798,17875,17972,18057,18143,18291,18377,18463,18591,18679,18757,18810,18861,18927,18998,19076,19147,19226,19299,19375,19448,19519,19626,19718,19791,19881,19974,20048,20119,20210,20262,20342,20410,20494,20579,20641,20705,20768,20840,20944,21052,21148,21254,21311,21366,21624,21961,22039,22188"}}]}]}