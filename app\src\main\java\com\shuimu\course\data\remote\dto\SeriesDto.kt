package com.shuimu.course.data.remote.dto

import com.google.gson.annotations.SerializedName

data class SeriesDto(
    @SerializedName("id") val id: String,
    @SerializedName("title") val title: String,
    @SerializedName("icon") val icon: String?,
    @SerializedName("price") val price: Int,
    @SerializedName("isFree") val isFree: Boolean,
    @SerializedName("isPurchased") val isPurchased: <PERSON>olean,
    @SerializedName("isPackage") val isPackage: Boolean = false,
    @SerializedName("defaultExpanded") val defaultExpanded: Boolean = false,
    @SerializedName("categories") val categories: List<CategoryDto>
) 