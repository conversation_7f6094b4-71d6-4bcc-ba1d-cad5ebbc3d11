package com.shuimu.course.data.workers

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.util.Log
import androidx.work.*
import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.CacheRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.PriorityQueue
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 智能下载调度器
 * 负责管理下载队列、优先级调度、网络条件感知等
 */
@Singleton
class SmartDownloadScheduler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val cacheRepository: CacheRepository,
    private val workManager: WorkManager,
    private val enhancedDownloadEngine: EnhancedDownloadEngine
) {
    companion object {
        private const val TAG = "SmartDownloadScheduler"
        private const val SCHEDULER_CHECK_INTERVAL = 10000L // 10秒检查一次
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val PRIORITY_HIGH = 100
        private const val PRIORITY_NORMAL = 50
        private const val PRIORITY_LOW = 10
    }

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val schedulerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 优先级队列，按优先级和添加时间排序
    private val downloadQueue = PriorityQueue<DownloadTask> { task1, task2 ->
        when {
            task1.priority != task2.priority -> task2.priority.compareTo(task1.priority) // 高优先级在前
            else -> task1.addedTime.compareTo(task2.addedTime) // 相同优先级按时间排序
        }
    }
    
    private val _queueState = MutableStateFlow(QueueState())
    val queueState: StateFlow<QueueState> = _queueState.asStateFlow()
    
    private var isSchedulerRunning = false
    
    /**
     * 下载任务
     */
    data class DownloadTask(
        val videoId: String,
        val cloudUrl: String,
        val videoTitle: String,
        val priority: Int,
        val addedTime: Long = System.currentTimeMillis(),
        val retryCount: Int = 0,
        val requireWifi: Boolean = false
    )
    
    /**
     * 队列状态
     */
    data class QueueState(
        val pendingTasks: List<DownloadTask> = emptyList(),
        val activeTasks: List<String> = emptyList(), // 正在下载的videoId列表
        val networkState: NetworkState = NetworkState.UNKNOWN,
        val maxConcurrentDownloads: Int = 3
    )
    
    /**
     * 网络状态
     */
    enum class NetworkState {
        WIFI, CELLULAR, NO_NETWORK, UNKNOWN
    }
    
    init {
        startScheduler()
        monitorNetworkChanges()
    }
    
    /**
     * 添加下载任务到队列
     */
    suspend fun addDownloadTask(
        videoId: String,
        cloudUrl: String,
        videoTitle: String,
        priority: Int = PRIORITY_NORMAL,
        requireWifi: Boolean = false
    ): Result<String> {
        return try {
            Log.d(TAG, "添加下载任务: $videoId, 优先级: $priority")
            
            // 检查是否已存在
            val existingCache = cacheRepository.getCacheInfoSync(videoId)
            if (existingCache?.status == CacheStatus.DOWNLOADED) {
                return Result.failure(Exception("视频已缓存"))
            }
            
            if (existingCache?.status == CacheStatus.DOWNLOADING) {
                return Result.failure(Exception("视频正在下载中"))
            }
            
            // 创建下载任务
            val task = DownloadTask(
                videoId = videoId,
                cloudUrl = cloudUrl,
                videoTitle = videoTitle,
                priority = priority,
                requireWifi = requireWifi
            )
            
            // 添加到队列
            synchronized(downloadQueue) {
                downloadQueue.offer(task)
            }
            
            // 更新缓存状态为等待
            cacheRepository.updateDownloadStatus(videoId, CacheStatus.PENDING)
            
            // 创建缓存记录
            val pendingCache = CachedVideo(
                videoId = videoId,
                title = videoTitle,
                localPath = null,
                downloadedBytes = 0L,
                totalBytes = 0L,
                status = CacheStatus.PENDING,
                progress = 0,
                cloudUrl = cloudUrl,
                updatedAt = System.currentTimeMillis(),
                priority = priority,
                retryCount = 0,
                lastRetryTime = 0L
            )
            cacheRepository.upsertCacheInfo(pendingCache)
            
            updateQueueState()
            triggerScheduling()
            
            Result.success("已加入下载队列")
        } catch (e: Exception) {
            Log.e(TAG, "添加下载任务失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 设置任务优先级
     */
    suspend fun setTaskPriority(videoId: String, priority: Int): Result<Unit> {
        return try {
            synchronized(downloadQueue) {
                val tasks = downloadQueue.toList()
                downloadQueue.clear()
                
                tasks.forEach { task ->
                    if (task.videoId == videoId) {
                        downloadQueue.offer(task.copy(priority = priority))
                    } else {
                        downloadQueue.offer(task)
                    }
                }
            }
            
            // 更新数据库中的优先级
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            cacheInfo?.let {
                val updatedCache = it.copy(priority = priority)
                cacheRepository.upsertCacheInfo(updatedCache)
            }
            
            updateQueueState()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 取消下载任务
     */
    suspend fun cancelDownloadTask(videoId: String): Result<Unit> {
        return try {
            Log.d(TAG, "取消下载任务: $videoId")
            
            // 从队列中移除
            synchronized(downloadQueue) {
                downloadQueue.removeAll { it.videoId == videoId }
            }
            
            // 取消WorkManager任务
            workManager.cancelAllWorkByTag("video_$videoId")
            
            // 更新状态
            cacheRepository.updateDownloadStatus(videoId, CacheStatus.NOT_CACHED)
            cacheRepository.deleteCacheInfo(videoId)
            
            updateQueueState()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 暂停下载任务
     */
    suspend fun pauseDownloadTask(videoId: String): Result<Unit> {
        return try {
            Log.d(TAG, "暂停下载任务: $videoId")
            
            // 取消WorkManager任务
            workManager.cancelAllWorkByTag("video_$videoId")
            
            // 更新状态为暂停
            cacheRepository.updateDownloadStatus(videoId, CacheStatus.PAUSED)
            
            updateQueueState()
            triggerScheduling() // 触发调度处理下一个任务
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 恢复下载任务
     */
    suspend fun resumeDownloadTask(videoId: String): Result<Unit> {
        return try {
            Log.d(TAG, "恢复下载任务: $videoId")
            
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo == null) {
                return Result.failure(Exception("缓存信息不存在"))
            }
            
            // 重新添加到队列
            addDownloadTask(
                videoId = videoId,
                cloudUrl = cacheInfo.cloudUrl ?: "",
                videoTitle = cacheInfo.title,
                priority = cacheInfo.priority
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 启动调度器
     */
    private fun startScheduler() {
        if (isSchedulerRunning) return
        
        isSchedulerRunning = true
        schedulerScope.launch {
            while (isSchedulerRunning) {
                try {
                    processDownloadQueue()
                    delay(SCHEDULER_CHECK_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "调度器运行异常", e)
                    delay(SCHEDULER_CHECK_INTERVAL)
                }
            }
        }
        
        Log.d(TAG, "智能调度器已启动")
    }
    
    /**
     * 处理下载队列
     */
    private suspend fun processDownloadQueue() {
        val networkState = getCurrentNetworkState()
        val config = cacheRepository.getCacheConfig().getOrNull()
        val maxConcurrent = config?.maxConcurrentDownloads ?: 3
        
        // 获取当前活跃下载数量
        val activeDownloads = cacheRepository.getDownloadingCount()
        
        Log.d(TAG, "处理下载队列: 网络状态=$networkState, 活跃下载=$activeDownloads, 最大并发=$maxConcurrent")
        
        if (activeDownloads >= maxConcurrent) {
            Log.d(TAG, "已达到最大并发下载数")
            return
        }
        
        // 从队列中获取合适的任务
        val availableSlots = maxConcurrent - activeDownloads
        val tasksToStart = mutableListOf<DownloadTask>()
        
        synchronized(downloadQueue) {
            repeat(availableSlots) {
                val task = findNextSuitableTask(networkState)
                if (task != null) {
                    downloadQueue.remove(task)
                    tasksToStart.add(task)
                }
            }
        }
        
        // 启动选中的任务
        tasksToStart.forEach { task ->
            startDownloadTask(task)
        }
        
        updateQueueState()
    }
    
    /**
     * 查找下一个合适的任务
     */
    private fun findNextSuitableTask(networkState: NetworkState): DownloadTask? {
        return downloadQueue.firstOrNull { task ->
            when {
                task.requireWifi && networkState != NetworkState.WIFI -> false
                networkState == NetworkState.NO_NETWORK -> false
                else -> true
            }
        }
    }
    
    /**
     * 启动下载任务
     */
    private suspend fun startDownloadTask(task: DownloadTask) {
        try {
            Log.d(TAG, "启动下载任务: ${task.videoId}")
            
            // 更新状态为下载中
            cacheRepository.updateDownloadStatus(task.videoId, CacheStatus.DOWNLOADING)
            
            // 创建WorkManager请求
            val downloadRequest = VideoDownloadWorker.createDownloadRequest(
                videoId = task.videoId,
                cloudUrl = task.cloudUrl,
                videoTitle = task.videoTitle
            )
            
            // 提交任务
            workManager.enqueue(downloadRequest)
            
        } catch (e: Exception) {
            Log.e(TAG, "启动下载任务失败: ${task.videoId}", e)
            
            // 失败时重新加入队列或标记失败
            if (task.retryCount < MAX_RETRY_ATTEMPTS) {
                val retryTask = task.copy(retryCount = task.retryCount + 1)
                synchronized(downloadQueue) {
                    downloadQueue.offer(retryTask)
                }
            } else {
                cacheRepository.updateDownloadStatus(task.videoId, CacheStatus.FAILED, "启动下载失败")
            }
        }
    }
    
    /**
     * 获取当前网络状态
     */
    private fun getCurrentNetworkState(): NetworkState {
        val activeNetwork = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        return when {
            capabilities == null -> NetworkState.NO_NETWORK
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkState.WIFI
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkState.CELLULAR
            else -> NetworkState.UNKNOWN
        }
    }
    
    /**
     * 监听网络变化
     */
    private fun monitorNetworkChanges() {
        enhancedDownloadEngine.networkState
            .onEach { networkState ->
                val state = when (networkState) {
                    EnhancedDownloadEngine.NetworkState.WIFI -> NetworkState.WIFI
                    EnhancedDownloadEngine.NetworkState.CELLULAR -> NetworkState.CELLULAR
                    EnhancedDownloadEngine.NetworkState.NO_NETWORK -> NetworkState.NO_NETWORK
                    EnhancedDownloadEngine.NetworkState.UNKNOWN -> NetworkState.UNKNOWN
                }
                
                _queueState.value = _queueState.value.copy(networkState = state)
                
                // 网络状态变化时触发调度
                if (state != NetworkState.NO_NETWORK) {
                    triggerScheduling()
                }
            }
            .launchIn(schedulerScope)
    }
    
    /**
     * 触发立即调度
     */
    private fun triggerScheduling() {
        schedulerScope.launch {
            processDownloadQueue()
        }
    }
    
    /**
     * 更新队列状态
     */
    private suspend fun updateQueueState() {
        val pendingTasks = synchronized(downloadQueue) {
            downloadQueue.toList()
        }
        
        val activeTasks = cacheRepository.getDownloadingCaches().first().map { it.videoId }
        
        _queueState.value = _queueState.value.copy(
            pendingTasks = pendingTasks,
            activeTasks = activeTasks
        )
    }
    
    /**
     * 停止调度器
     */
    fun stopScheduler() {
        isSchedulerRunning = false
        schedulerScope.cancel()
        enhancedDownloadEngine.cleanup()
        Log.d(TAG, "智能调度器已停止")
    }
}
