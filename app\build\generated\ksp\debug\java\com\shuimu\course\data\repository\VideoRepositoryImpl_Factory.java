package com.shuimu.course.data.repository;

import com.shuimu.course.data.local.dao.VideoDao;
import com.shuimu.course.data.remote.api.VideoApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class VideoRepositoryImpl_Factory implements Factory<VideoRepositoryImpl> {
  private final Provider<VideoApi> videoApiProvider;

  private final Provider<VideoDao> videoDaoProvider;

  public VideoRepositoryImpl_Factory(Provider<VideoApi> videoApiProvider,
      Provider<VideoDao> videoDaoProvider) {
    this.videoApiProvider = videoApiProvider;
    this.videoDaoProvider = videoDaoProvider;
  }

  @Override
  public VideoRepositoryImpl get() {
    return newInstance(videoApiProvider.get(), videoDaoProvider.get());
  }

  public static VideoRepositoryImpl_Factory create(Provider<VideoApi> videoApiProvider,
      Provider<VideoDao> videoDaoProvider) {
    return new VideoRepositoryImpl_Factory(videoApiProvider, videoDaoProvider);
  }

  public static VideoRepositoryImpl newInstance(VideoApi videoApi, VideoDao videoDao) {
    return new VideoRepositoryImpl(videoApi, videoDao);
  }
}
