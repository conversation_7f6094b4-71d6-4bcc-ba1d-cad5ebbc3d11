package com.shuimu.course.data.local.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "videos")
data class VideoEntity(
    @PrimaryKey val id: String,
    @ColumnInfo(name = "title") val title: String,
    @ColumnInfo(name = "duration") val duration: Int, // 视频总时长，单位：秒
    @ColumnInfo(name = "description") val description: String,
    @ColumnInfo(name = "category_id") val categoryId: String,
    @ColumnInfo(name = "watch_count") val watchCount: Int?,
    @ColumnInfo(name = "cloud_url") val cloudUrl: String,
    @ColumnInfo(name = "local_path") val localPath: String?,
    @ColumnInfo(name = "cache_status") val cacheStatus: String,
    
    // 🔥 统一进度参数：观看完成度 (0.0-1.0)
    @ColumnInfo(name = "progress") val progress: Float
) 