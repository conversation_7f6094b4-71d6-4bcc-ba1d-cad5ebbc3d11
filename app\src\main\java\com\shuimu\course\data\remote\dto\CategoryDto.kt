package com.shuimu.course.data.remote.dto

import com.google.gson.annotations.SerializedName

data class CategoryDto(
    @SerializedName("id") val id: String,
    @SerializedName("title") val title: String,
    @SerializedName("seriesId") val seriesId: String, // 所属系列ID
    @SerializedName("price") val price: Int? = null, // 价格，单位：分
    @SerializedName("isFree") val isFree: Boolean,
    @SerializedName("isPurchased") val isPurchased: Boolean? = null,
    @SerializedName("defaultExpanded") val defaultExpanded: Boolean = false,
    @SerializedName("videoIds") val videoIds: List<String> = emptyList(),
    
    // 🔥 API现在直接返回完整的videos对象
    @SerializedName("videos") val videos: List<VideoDto> = emptyList(),
    
    // 🔥 服务器计算好的显示数据
    @SerializedName("watchCount") val watchCount: Int? = null, // 分类总观看次数 (个人观看次数累计)
    
    // 🔥 统一进度参数：分类完成度 (0.0-1.0，用于UI显示)
    @SerializedName("progress") val progress: Float? = null
) 