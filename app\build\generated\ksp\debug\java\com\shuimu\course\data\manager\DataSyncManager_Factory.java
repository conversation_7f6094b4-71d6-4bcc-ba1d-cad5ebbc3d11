package com.shuimu.course.data.manager;

import android.content.Context;
import com.shuimu.course.data.remote.api.CacheApi;
import com.shuimu.course.data.remote.api.PaymentApi;
import com.shuimu.course.domain.repository.CacheRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DataSyncManager_Factory implements Factory<DataSyncManager> {
  private final Provider<Context> contextProvider;

  private final Provider<PaymentApi> paymentApiProvider;

  private final Provider<CacheApi> cacheApiProvider;

  private final Provider<CacheRepository> cacheRepositoryProvider;

  public DataSyncManager_Factory(Provider<Context> contextProvider,
      Provider<PaymentApi> paymentApiProvider, Provider<CacheApi> cacheApiProvider,
      Provider<CacheRepository> cacheRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.paymentApiProvider = paymentApiProvider;
    this.cacheApiProvider = cacheApiProvider;
    this.cacheRepositoryProvider = cacheRepositoryProvider;
  }

  @Override
  public DataSyncManager get() {
    return newInstance(contextProvider.get(), paymentApiProvider.get(), cacheApiProvider.get(), cacheRepositoryProvider.get());
  }

  public static DataSyncManager_Factory create(Provider<Context> contextProvider,
      Provider<PaymentApi> paymentApiProvider, Provider<CacheApi> cacheApiProvider,
      Provider<CacheRepository> cacheRepositoryProvider) {
    return new DataSyncManager_Factory(contextProvider, paymentApiProvider, cacheApiProvider, cacheRepositoryProvider);
  }

  public static DataSyncManager newInstance(Context context, PaymentApi paymentApi,
      CacheApi cacheApi, CacheRepository cacheRepository) {
    return new DataSyncManager(context, paymentApi, cacheApi, cacheRepository);
  }
}
