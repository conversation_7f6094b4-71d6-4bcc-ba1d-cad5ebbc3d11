<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="228478ff-15cd-4d9c-ac6c-51e5f799bbf2" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.gradle/9.0-milestone-1/executionHistory/executionHistory.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/9.0-milestone-1/executionHistory/executionHistory.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/9.0-milestone-1/executionHistory/executionHistory.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/9.0-milestone-1/executionHistory/executionHistory.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/9.0-milestone-1/fileHashes/fileHashes.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/9.0-milestone-1/fileHashes/fileHashes.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/9.0-milestone-1/fileHashes/fileHashes.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/9.0-milestone-1/fileHashes/fileHashes.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/9.0-milestone-1/fileHashes/resourceHashesCache.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/9.0-milestone-1/fileHashes/resourceHashesCache.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/buildOutputCleanup/buildOutputCleanup.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/buildOutputCleanup/buildOutputCleanup.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/file-system.probe" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/file-system.probe" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/hilt/component_sources/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/hilt/component_sources/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/ksp/debug/java/com/shuimu/course/CourseApplication_MembersInjector.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/ksp/debug/java/com/shuimu/course/CourseApplication_MembersInjector.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/ksp/debug/java/com/shuimu/course/data/workers/VideoDownloadWorker_Factory.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/ksp/debug/java/com/shuimu/course/data/workers/VideoDownloadWorker_Factory.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/generated/ksp/debug/java/com/shuimu/course/presentation/viewmodel/PaymentViewModel_Factory.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/generated/ksp/debug/java/com/shuimu/course/presentation/viewmodel/PaymentViewModel_Factory.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/apk/debug/app-debug.apk" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/apk/debug/app-debug.apk" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/CourseApplication$onCreate$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/CourseApplication$onCreate$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/CourseApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/CourseApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/CourseApplication_MembersInjector.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/CourseApplication_MembersInjector.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl$LazyClassKeyProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl$LazyClassKeyProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl$SwitchingProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl$SwitchingProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$Builder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$Builder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$LazyClassKeyProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$LazyClassKeyProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$SwitchingProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$SwitchingProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$Companion.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$Companion.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$downloadVideo$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$downloadVideo$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2$emit$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2$emit$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$realDownload$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker$realDownload$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker_Factory.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/data/workers/VideoDownloadWorker_Factory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/domain/usecase/cache/StartDownloadUseCase.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/domain/usecase/cache/StartDownloadUseCase.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme2$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme2$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$7$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$7$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/CategoryCardKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/display/VideoItemKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$lambda$5$lambda$4$$inlined$items$default$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$lambda$5$lambda$4$$inlined$items$default$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$3$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$4$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/HomeScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$lambda$10$lambda$9$$inlined$onDispose$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$lambda$10$lambda$9$$inlined$onDispose$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1$emit$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1$emit$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$getSeries$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$getSeries$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeCacheStatus$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeCacheStatus$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeExpandedStates$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeExpandedStates$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$setExpandedState$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$setExpandedState$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/HomeViewModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/PaymentViewModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/PaymentViewModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/PaymentViewModel_Factory.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/classes/debug/transformDebugClassesWithAsm/dirs/com/shuimu/course/presentation/viewmodel/PaymentViewModel_Factory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/desugar_graph/debug/dexBuilderDebug/out/currentProject/dirs_bucket_2/graph.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/desugar_graph/debug/dexBuilderDebug/out/currentProject/dirs_bucket_2/graph.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/1/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/1/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/12/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/12/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/14/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/14/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/15/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/15/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/3/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/3/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/5/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/5/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/6/classes.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/dex/debug/mergeProjectDexDebug/6/classes.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl$LazyClassKeyProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl$LazyClassKeyProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl$SwitchingProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl$SwitchingProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$Builder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$Builder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$LazyClassKeyProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$LazyClassKeyProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$SwitchingProvider.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$SwitchingProvider.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCBuilder.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCBuilder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/hilt/component_classes/debug/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug-mergeJavaRes/merge-state" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug-mergeJavaRes/merge-state" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/mergeDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/incremental/packageDebug/tmp/debug/dex-renamer-state.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/incremental/packageDebug/tmp/debug/dex-renamer-state.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/shuimu/course/CourseApplication_MembersInjector.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/shuimu/course/CourseApplication_MembersInjector.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/shuimu/course/data/workers/VideoDownloadWorker_Factory.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/shuimu/course/data/workers/VideoDownloadWorker_Factory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/shuimu/course/presentation/viewmodel/PaymentViewModel_Factory.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/shuimu/course/presentation/viewmodel/PaymentViewModel_Factory.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/CourseApplication$onCreate$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/CourseApplication$onCreate$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/CourseApplication.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/CourseApplication.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/CourseApplication_MembersInjector.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/CourseApplication_MembersInjector.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCBuilder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCBuilder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl$LazyClassKeyProvider.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl$LazyClassKeyProvider.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCBuilder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCBuilder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl$SwitchingProvider.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl$SwitchingProvider.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$Builder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$Builder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCBuilder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCBuilder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCImpl.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCImpl.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCBuilder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCBuilder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCImpl.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCImpl.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCBuilder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCBuilder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCImpl.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewCImpl.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCBuilder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCBuilder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$LazyClassKeyProvider.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$LazyClassKeyProvider.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$SwitchingProvider.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$SwitchingProvider.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCBuilder.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCBuilder.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCImpl.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCImpl.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/DaggerCourseApplication_HiltComponents_SingletonC.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$Companion.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$Companion.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$downloadVideo$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$downloadVideo$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2$emit$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2$emit$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$realDownload$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker$realDownload$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker_Factory.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/data/workers/VideoDownloadWorker_Factory.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/domain/usecase/cache/StartDownloadUseCase.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/domain/usecase/cache/StartDownloadUseCase.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2$2$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2$2$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme2$1$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme2$1$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$6$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$6$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$7$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$7$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/CategoryCardKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt$lambda-1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt$lambda-1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$5$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$5$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$7$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$7$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$2.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$2.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$3.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$3.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$5.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$5.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/display/VideoItemKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt$lambda-1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt$lambda-1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$lambda$5$lambda$4$$inlined$items$default$4.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$lambda$5$lambda$4$$inlined$items$default$4.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-2$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-2$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$2.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$2$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$1$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$2$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$3$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$4$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$2.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$3.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$4.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/HomeScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$1$1$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$1$1$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$2$1.dex" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$3$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$3$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$4$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$4$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$lambda$10$lambda$9$$inlined$onDispose$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$lambda$10$lambda$9$$inlined$onDispose$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1$emit$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1$emit$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$getSeries$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$getSeries$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeCacheStatus$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeCacheStatus$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeExpandedStates$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeExpandedStates$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$setExpandedState$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$setExpandedState$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/HomeViewModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/PaymentViewModel.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/PaymentViewModel.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/PaymentViewModel_Factory.dex" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/intermediates/project_dex_archive/debug/dexBuilderDebug/out/com/shuimu/course/presentation/viewmodel/PaymentViewModel_Factory.dex" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/last-build.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/cacheable/last-build.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/local-state/build-history.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/kotlin/compileDebugKotlin/local-state/build-history.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/outputs/apk/debug/app-debug.apk" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/outputs/apk/debug/app-debug.apk" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BR.class.uniqueId39" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BR.class.uniqueId5" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BR.java.uniqueId10" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BR.java.uniqueId35" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/CacheRepositoryImpl_Factory.class.uniqueId11" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/CourseApplication_GeneratedInjector.class.uniqueId2" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/CourseApplication_MembersInjector.class.uniqueId13" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/DataBinderMapperImpl$InnerBrLookup.class.uniqueId30" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/DataBinderMapperImpl$InnerLayoutIdLookup.class.uniqueId15" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/DataBinderMapperImpl.class.uniqueId21" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/DataBinderMapperImpl.java.uniqueId33" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/DataBinderMapperImpl.java.uniqueId37" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/DataBindingComponent.class.uniqueId14" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/DataBindingComponent.java.uniqueId23" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/DownloadManager_Factory.class.uniqueId40" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/HomeViewModel_Factory.class.uniqueId12" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/HomeViewModel_HiltModules$BindsModule.class.uniqueId4" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/HomeViewModel_HiltModules$KeyModule.class.uniqueId24" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/HomeViewModel_HiltModules.class.uniqueId41" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/HomeViewModel_HiltModules_KeyModule_ProvideFactory$InstanceHolder.class.uniqueId7" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/HomeViewModel_HiltModules_KeyModule_ProvideFactory.class.uniqueId17" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideAuthApiFactory.class.uniqueId31" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideCacheApiFactory.class.uniqueId8" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideCustomDnsFactory$InstanceHolder.class.uniqueId18" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideCustomDnsFactory.class.uniqueId27" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideHostInterceptorFactory$InstanceHolder.class.uniqueId22" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideHostInterceptorFactory.class.uniqueId20" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideHostnameVerifierFactory$InstanceHolder.class.uniqueId36" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideHostnameVerifierFactory.class.uniqueId29" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideLoggingInterceptorFactory$InstanceHolder.class.uniqueId32" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideLoggingInterceptorFactory.class.uniqueId28" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideOkHttpClientFactory.class.uniqueId16" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvidePaymentApiFactory.class.uniqueId34" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvidePlaylistApiFactory.class.uniqueId26" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideRetrofitFactory.class.uniqueId1" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideSearchApiFactory.class.uniqueId3" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideSeriesApiFactory.class.uniqueId25" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideShareApiFactory.class.uniqueId6" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideUserApiFactory.class.uniqueId19" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/NetworkModule_ProvideVideoApiFactory.class.uniqueId9" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/StartDownloadUseCase_Factory.class.uniqueId38" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCBuilder.class.uniqueId31" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCBuilder.class.uniqueId31" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl$LazyClassKeyProvider.class.uniqueId16" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl$LazyClassKeyProvider.class.uniqueId16" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl.class.uniqueId0" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityCImpl.class.uniqueId0" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCBuilder.class.uniqueId48" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCBuilder.class.uniqueId48" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl$SwitchingProvider.class.uniqueId40" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl$SwitchingProvider.class.uniqueId40" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl.class.uniqueId5" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ActivityRetainedCImpl.class.uniqueId5" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$Builder.class.uniqueId36" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$Builder.class.uniqueId36" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCBuilder.class.uniqueId7" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCBuilder.class.uniqueId7" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCImpl.class.uniqueId29" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$FragmentCImpl.class.uniqueId29" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCBuilder.class.uniqueId26" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCBuilder.class.uniqueId26" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCImpl.class.uniqueId14" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ServiceCImpl.class.uniqueId14" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider$1.class.uniqueId49" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider$1.class.uniqueId49" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.class.uniqueId6" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.class.uniqueId6" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl.class.uniqueId23" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$SingletonCImpl.class.uniqueId23" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewCBuilder.class.uniqueId42" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewCBuilder.class.uniqueId42" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewCImpl.class.uniqueId10" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewCImpl.class.uniqueId10" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCBuilder.class.uniqueId32" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCBuilder.class.uniqueId32" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$LazyClassKeyProvider.class.uniqueId47" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$LazyClassKeyProvider.class.uniqueId47" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$SwitchingProvider.class.uniqueId20" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl$SwitchingProvider.class.uniqueId20" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl.class.uniqueId22" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewModelCImpl.class.uniqueId22" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCBuilder.class.uniqueId24" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCBuilder.class.uniqueId24" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCImpl.class.uniqueId33" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC$ViewWithFragmentCImpl.class.uniqueId33" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC.class.uniqueId18" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC.class.uniqueId18" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC.java.uniqueId30" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/compileTransaction/stash-dir/DaggerCourseApplication_HiltComponents_SingletonC.java.uniqueId30" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/previous-compilation-data.bin" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/hiltJavaCompileDebug/previous-compilation-data.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/CourseApplication$onCreate$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/CourseApplication$onCreate$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/CourseApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/CourseApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$Companion.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$Companion.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$doWork$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$downloadVideo$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$downloadVideo$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2$emit$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2$emit$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$processNextPendingDownload$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$realDownload$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker$realDownload$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/data/workers/VideoDownloadWorker.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/domain/usecase/cache/StartDownloadUseCase.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/domain/usecase/cache/StartDownloadUseCase.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCard$5$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme2$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme2$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$7$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$1$7$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2$invoke$$inlined$ConstraintLayout$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryCardScheme3$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$$inlined$ConstraintLayout$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt$CategoryHeaderLayout$2$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/CategoryCardKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$CategoryCardKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt$lambda-5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/ComposableSingletons$VideoItemKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$5$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$5$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$7$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$3$2$7$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt$VideoItem$lambda$27$$inlined$ConstraintLayout$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/display/VideoItemKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/ComposableSingletons$PlaylistPanelKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$1$1$1$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$lambda$5$lambda$4$$inlined$items$default$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt$PlaylistPanel$lambda$5$lambda$4$$inlined$items$default$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/components/video/PlaylistPanelKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt$lambda-2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/ComposableSingletons$HomeScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$1$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$3$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3$1$1$4$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$2$1$1$1$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2$invoke$lambda$7$lambda$6$lambda$5$$inlined$items$default$4.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3$1$2.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt$HomeScreen$3.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/HomeScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$1$1$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$1$1$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$2$1.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$3$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$3$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$4$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$4$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$lambda$10$lambda$9$$inlined$onDispose$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt$VideoPlayerScreen$lambda$10$lambda$9$$inlined$onDispose$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/ui/screens/VideoPlayerScreenKt.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1$emit$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1$emit$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$downloadVideo$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$getSeries$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$getSeries$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$monitorDownloadStatus$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeCacheStatus$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeCacheStatus$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeExpandedStates$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$observeExpandedStates$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$setExpandedState$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$setExpandedState$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel$startVideoDownload$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/HomeViewModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/PaymentViewModel$processPayment$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/PaymentViewModel.class" beforeDir="false" afterPath="$PROJECT_DIR$/app/build/tmp/kotlin-classes/debug/com/shuimu/course/presentation/viewmodel/PaymentViewModel.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/CourseApplication.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/CourseApplication.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/data/workers/VideoDownloadWorker.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/data/workers/VideoDownloadWorker.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/components/display/CategoryCard.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/components/display/CategoryCard.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/components/display/VideoItem.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/components/display/VideoItem.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/components/video/PlaylistPanel.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/components/video/PlaylistPanel.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/screens/HomeScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/screens/HomeScreen.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/screens/VideoPlayerScreen.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/ui/screens/VideoPlayerScreen.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/viewmodel/HomeViewModel.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/viewmodel/HomeViewModel.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/viewmodel/PaymentViewModel.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/shuimu/course/presentation/viewmodel/PaymentViewModel.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/mock_server/src/data/videos.json" beforeDir="false" afterPath="$PROJECT_DIR$/mock_server/src/data/videos.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/setup_claude_path.ps1" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=LocalEmulator, isTemplate=false, identifier=path=C:\Users\<USER>\.android\avd\Medium_Phone_API_35.avd)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ShuimuVideoCourse" type="f1a62948:ProjectNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2ykjT3d6bQnqxlDGvseYDddXDO2" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Android App.MainActivity (1).executor&quot;: &quot;Run&quot;,
    &quot;Android App.MainActivity.executor&quot;: &quot;Run&quot;,
    &quot;Android App.ShuimuVideoCourse.app.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;deletionFromPopupRequiresConfirmation&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;fenzhi03&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/MCP_Server&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;Errors&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="MainActivity" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false" temporary="true">
      <module name="ShuimuVideoCourse.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="specific_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="com.shuimu.course.MainActivity" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Android App.MainActivity" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-975db3bf15a3-31b6be0877a2-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="228478ff-15cd-4d9c-ac6c-51e5f799bbf2" name="Changes" comment="" />
      <created>1750385469490</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750385469490</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$/.." />
    </ignored-roots>
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.shuimu.course">
          <value>
            <CheckInfo lastCheckTimestamp="1750386182982" />
          </value>
        </entry>
        <entry key="com.shuimu.course.test">
          <value>
            <CheckInfo lastCheckTimestamp="1750386182982" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>