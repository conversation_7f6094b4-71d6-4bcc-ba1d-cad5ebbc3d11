package com.shuimu.course.data.repository

import com.shuimu.course.data.local.dao.*
import com.shuimu.course.data.local.relations.toDomain
import com.shuimu.course.data.remote.api.SeriesApi
import com.shuimu.course.data.toEntity
import com.shuimu.course.data.toDomainModel
import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import retrofit2.HttpException
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SeriesRepositoryImpl @Inject constructor(
    private val seriesApi: SeriesApi,
    private val seriesDao: SeriesDao,
    private val categoryDao: CategoryDao,
    private val videoDao: VideoDao,
    private val playProgressDao: PlayProgressDao,
    private val cacheInfoDao: CacheInfoDao
) : SeriesRepository {

    override fun getSeries(): Flow<Resource<List<Series>>> = flow {
        emit(Resource.Loading())

        // First, emit the currently cached data so the UI can display something immediately.
        val initialData = getCombinedLocalData()
        emit(Resource.Loading(data = initialData))

        try {
            // Then, try to fetch fresh data from the network.
            val remoteSeries = seriesApi.getAllSeries().body()
            if (remoteSeries != null) {
                val domainSeries = remoteSeries.map { it.toDomainModel() }

                // --- DATABASE CACHING ---
                seriesDao.clearAll()
                
                // 强制清空UI缓存，确保重新渲染
                emit(Resource.Loading(data = emptyList()))
                
                // Emit fresh data immediately so the user sees content without waiting for DB.
                emit(Resource.Success(domainSeries))
                
                // 🔥 使用事务确保数据一致性和外键约束
                val allSeriesEntities = domainSeries.map { it.toEntity() }
                val allCategoryEntities = domainSeries.flatMap { series ->
                    series.categories.map { it.toEntity() }
                }
                val allVideoEntities = domainSeries.flatMap { series ->
                    series.categories.flatMap { category ->
                        category.videos.map { it.toEntity() }
                    }
                }
                
                // 在单个事务中按顺序写入所有数据，添加外键约束异常处理
                try {
                    seriesDao.insertAllData(
                        allSeriesEntities,
                        allCategoryEntities, 
                        allVideoEntities,
                        categoryDao,
                        videoDao
                    )
                } catch (e: android.database.sqlite.SQLiteConstraintException) {
                    // 外键约束失败时，清理所有表并重试
                    android.util.Log.w("SeriesRepository", "Foreign key constraint failed, retrying with clean slate", e)
                    seriesDao.clearAll()
                    categoryDao.clearAll()
                    videoDao.clearAll()
                    
                    // 重试插入
                    seriesDao.insertAllData(
                        allSeriesEntities,
                        allCategoryEntities, 
                        allVideoEntities,
                        categoryDao,
                        videoDao
                    )
                }
                
                // 🔥 写库完成后，重新emit带完整视频数据的版本
                val finalData = getCombinedLocalData()
                emit(Resource.Success(finalData))
            } else {
                emit(Resource.Error("Failed to fetch data.", data = initialData))
            }
        } catch (e: IOException) {
            emit(Resource.Error("Couldn't reach server. Check your internet connection.", data = initialData))
        } catch (e: HttpException) {
            emit(Resource.Error("An unexpected error occurred.", data = initialData))
        }
    }
    
    private suspend fun getCombinedLocalData(): List<Series> {
        val seriesWithCategories = seriesDao.getSeriesWithCategories().first()
        val progresses = playProgressDao.getAllProgress().first()
        val caches = cacheInfoDao.getAllCompletedCaches().first()
        
        return seriesWithCategories.map { it.toDomain(progresses, caches) }
    }
} 