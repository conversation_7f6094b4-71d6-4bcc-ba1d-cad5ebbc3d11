package com.shuimu.course.data.repository

import com.shuimu.course.data.local.dao.*
import com.shuimu.course.data.local.relations.toDomain
import com.shuimu.course.data.remote.api.SeriesApi
import com.shuimu.course.data.toEntity
import com.shuimu.course.data.toDomainModel
import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import retrofit2.HttpException
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SeriesRepositoryImpl @Inject constructor(
    private val seriesApi: SeriesApi,
    private val seriesDao: SeriesDao,
    private val categoryDao: CategoryDao,
    private val videoDao: VideoDao,
    private val playProgressDao: PlayProgressDao,
    private val cacheInfoDao: CacheInfoDao,
    private val dataLayerManager: com.shuimu.course.data.manager.DataLayerManager,
    private val presetDataProvider: com.shuimu.course.data.provider.PresetDataProvider
) : SeriesRepository {

    override fun getSeries(): Flow<Resource<List<Series>>> = flow {
        android.util.Log.d("SeriesRepository", "开始获取系列数据")

        // 🔥 修复：优化首次加载逻辑，避免重复emit
        val initialData = try {
            getCombinedLocalData()
        } catch (e: Exception) {
            android.util.Log.w("SeriesRepository", "获取本地数据失败", e)
            emptyList()
        }

        // 🔥 新增：支持预置数据兜底
        val dataToShow = if (initialData.isNotEmpty()) {
            android.util.Log.d("SeriesRepository", "发送本地数据: ${initialData.size}个系列")
            initialData
        } else {
            android.util.Log.w("SeriesRepository", "无本地数据，使用预置数据兜底")
            presetDataProvider.getPresetSeries()
        }

        // 立即显示数据（永远不显示空白页面）
        emit(Resource.Success(dataToShow))

        try {
            // Then, try to fetch fresh data from the network.
            val remoteSeries = seriesApi.getAllSeries().body()
            if (remoteSeries != null) {
                val domainSeries = remoteSeries.map { it.toDomainModel() }

                // --- 智能数据更新策略 ---
                // 🔥 修复：实现数据差异检测，避免不必要的UI刷新
                val existingCaches = cacheInfoDao.getAllCacheInfo().first()
                android.util.Log.d("SeriesRepository", "保存现有缓存状态: ${existingCaches.size}个")

                // 获取当前本地数据进行对比
                val currentLocalData = try {
                    getCombinedLocalData()
                } catch (e: Exception) {
                    android.util.Log.w("SeriesRepository", "获取本地数据失败，使用空列表", e)
                    emptyList()
                }

                // 数据差异检测（与当前显示的数据对比）
                val hasSignificantChanges = detectDataChanges(dataToShow, domainSeries)

                if (!hasSignificantChanges) {
                    android.util.Log.d("SeriesRepository", "✅ 数据无显著变化，跳过UI更新，避免重复刷新")
                    return@flow // 直接返回，不触发UI更新
                }

                android.util.Log.d("SeriesRepository", "🔄 检测到数据变化，开始无缝更新")
                seriesDao.clearAll()

                // 🔥 移除强制清空UI的逻辑，直接发送新数据
                // emit(Resource.Loading(data = emptyList())) // ❌ 删除这行，避免闪烁

                // 直接发送新数据，实现无缝更新
                android.util.Log.d("SeriesRepository", "📤 发送更新后的数据，无UI闪烁")
                emit(Resource.Success(domainSeries))
                
                // 🔥 使用事务确保数据一致性和外键约束
                val allSeriesEntities = domainSeries.map { it.toEntity() }
                val allCategoryEntities = domainSeries.flatMap { series ->
                    series.categories.map { it.toEntity() }
                }
                val allVideoEntities = domainSeries.flatMap { series ->
                    series.categories.flatMap { category ->
                        category.videos.map { it.toEntity() }
                    }
                }
                
                // 在单个事务中按顺序写入所有数据，添加外键约束异常处理
                try {
                    seriesDao.insertAllData(
                        allSeriesEntities,
                        allCategoryEntities, 
                        allVideoEntities,
                        categoryDao,
                        videoDao
                    )
                } catch (e: android.database.sqlite.SQLiteConstraintException) {
                    // 外键约束失败时，清理所有表并重试
                    android.util.Log.w("SeriesRepository", "Foreign key constraint failed, retrying with clean slate", e)
                    seriesDao.clearAll()
                    categoryDao.clearAll()
                    videoDao.clearAll()
                    
                    // 重试插入
                    seriesDao.insertAllData(
                        allSeriesEntities,
                        allCategoryEntities, 
                        allVideoEntities,
                        categoryDao,
                        videoDao
                    )
                }

                // 🔥 恢复缓存状态（重要：确保本地缓存状态不丢失）
                existingCaches.forEach { cache ->
                    try {
                        cacheInfoDao.upsertCacheInfo(cache)
                        android.util.Log.d("SeriesRepository", "恢复缓存状态: ${cache.videoId} - ${cache.status}")
                    } catch (e: Exception) {
                        android.util.Log.e("SeriesRepository", "恢复缓存状态失败: ${cache.videoId}", e)
                    }
                }

                // 🔥 写库完成后，重新emit带完整视频数据的版本
                val finalData = getCombinedLocalData()
                emit(Resource.Success(finalData))
            } else {
                android.util.Log.w("SeriesRepository", "服务器返回空数据")
                // 🔥 修复：服务器返回空数据时，如果已有显示数据就不emit错误
                if (dataToShow.isEmpty()) {
                    emit(Resource.Error("Failed to fetch data.", data = dataToShow))
                }
            }
        } catch (e: IOException) {
            android.util.Log.e("SeriesRepository", "网络连接错误", e)
            // 🔥 修复：网络错误时，如果已显示数据就不emit错误，避免UI闪烁
            if (dataToShow.isEmpty()) {
                emit(Resource.Error("Couldn't reach server. Check your internet connection.", data = dataToShow))
            }
        } catch (e: HttpException) {
            android.util.Log.e("SeriesRepository", "HTTP错误", e)
            if (dataToShow.isEmpty()) {
                emit(Resource.Error("An unexpected error occurred.", data = dataToShow))
            }
        }
    }
    
    private suspend fun getCombinedLocalData(): List<Series> {
        val seriesWithCategories = seriesDao.getSeriesWithCategories().first()
        val progresses = playProgressDao.getAllProgress().first()
        // 🔥 修复：获取所有缓存状态，不只是已完成的
        val caches = cacheInfoDao.getAllCacheInfo().first()

        android.util.Log.d("SeriesRepository", "本地数据合并: ${caches.size}个缓存项")
        caches.forEach { cache ->
            android.util.Log.d("SeriesRepository", "缓存项: ${cache.videoId} - ${cache.status}")
        }

        return seriesWithCategories.map { seriesWithCategory ->
            // 使用数据分层管理器进行智能合并
            val series = seriesWithCategory.toDomain(progresses, caches)

            // 对每个视频应用智能合并策略
            val enhancedCategories = series.categories.map { category ->
                val enhancedVideos = category.videos.map { video ->
                    // 获取本地状态（简化版本）
                    val localState = try {
                        dataLayerManager.getLocalVideoState(video.id)
                    } catch (e: Exception) {
                        android.util.Log.e("SeriesRepository", "获取本地状态失败: ${video.id}", e)
                        null
                    }

                    // 智能合并数据
                    dataLayerManager.mergeVideoData(video, localState)
                }
                category.copy(videos = enhancedVideos)
            }

            series.copy(categories = enhancedCategories)
        }
    }

    /**
     * 检测数据是否有显著变化
     * 只有在内容、购买状态、价格等关键信息变化时才返回true
     */
    private fun detectDataChanges(localData: List<Series>, serverData: List<Series>): Boolean {
        try {
            // 1. 数量变化
            if (localData.size != serverData.size) {
                android.util.Log.d("SeriesRepository", "系列数量变化: ${localData.size} -> ${serverData.size}")
                return true
            }

            // 2. 逐个对比系列
            for (i in localData.indices) {
                val local = localData[i]
                val server = serverData[i]

                // 2.1 系列基本信息变化
                if (local.id != server.id ||
                    local.title != server.title ||
                    local.price != server.price ||
                    local.isFree != server.isFree ||
                    local.isPurchased != server.isPurchased) {
                    android.util.Log.d("SeriesRepository", "系列信息变化: ${local.id}")
                    return true
                }

                // 2.2 分类数量变化
                if (local.categories.size != server.categories.size) {
                    android.util.Log.d("SeriesRepository", "分类数量变化: ${local.categories.size} -> ${server.categories.size}")
                    return true
                }

                // 2.3 逐个对比分类
                for (j in local.categories.indices) {
                    val localCategory = local.categories[j]
                    val serverCategory = server.categories[j]

                    // 分类基本信息变化
                    if (localCategory.id != serverCategory.id ||
                        localCategory.title != serverCategory.title ||
                        localCategory.price != serverCategory.price ||
                        localCategory.isFree != serverCategory.isFree ||
                        localCategory.isPurchased != serverCategory.isPurchased) {
                        android.util.Log.d("SeriesRepository", "分类信息变化: ${localCategory.id}")
                        return true
                    }

                    // 视频数量变化
                    if (localCategory.videos.size != serverCategory.videos.size) {
                        android.util.Log.d("SeriesRepository", "视频数量变化: ${localCategory.videos.size} -> ${serverCategory.videos.size}")
                        return true
                    }

                    // 2.4 逐个对比视频（只检查关键信息，不检查本地状态如缓存、进度）
                    for (k in localCategory.videos.indices) {
                        val localVideo = localCategory.videos[k]
                        val serverVideo = serverCategory.videos[k]

                        // 只检查服务器控制的字段
                        if (localVideo.id != serverVideo.id ||
                            localVideo.title != serverVideo.title ||
                            localVideo.description != serverVideo.description ||
                            localVideo.duration != serverVideo.duration ||
                            localVideo.cloudUrl != serverVideo.cloudUrl ||
                            localVideo.isPurchasable != serverVideo.isPurchasable) {
                            android.util.Log.d("SeriesRepository", "视频信息变化: ${localVideo.id}")
                            return true
                        }
                    }
                }
            }

            android.util.Log.d("SeriesRepository", "数据对比完成，无显著变化")
            return false

        } catch (e: Exception) {
            android.util.Log.e("SeriesRepository", "数据对比失败，默认认为有变化", e)
            return true // 出错时默认认为有变化，确保数据更新
        }
    }
}