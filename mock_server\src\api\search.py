from fastapi import APIRouter, Query
from typing import List, Optional
from ..models.search import SearchHistory, SearchResult

router = APIRouter()

# Mock data for search functionality
SEARCH_HISTORY_DATA = [
    "恋爱技巧",
    "聊天方法", 
    "约会攻略",
    "表白技巧",
    "情感心理",
    "社交技能"
]

SEARCH_RESULTS_DATA = [
    SearchResult(
        id="1",
        title="恋爱技巧完整指南",
        description="系统性学习恋爱中的各种技巧",
        type="series",
        matchScore=0.95
    ),
    SearchResult(
        id="2", 
        title="高效聊天方法",
        description="掌握聊天的核心技巧",
        type="video",
        matchScore=0.87
    ),
    SearchResult(
        id="3",
        title="约会攻略全集",
        description="从第一次约会到长期关系",
        type="category",
        matchScore=0.82
    ),
    SearchResult(
        id="4",
        title="表白技巧与时机",
        description="如何选择最佳表白时机",
        type="video", 
        matchScore=0.78
    )
]

@router.get("/search/history", response_model=List[str])
def get_search_history():
    """获取用户搜索历史"""
    return SEARCH_HISTORY_DATA

@router.get("/search", response_model=List[SearchResult])
def search_content(q: str = Query(..., description="搜索关键词")):
    """搜索内容"""
    if not q:
        return []
    
    # 简单的模拟搜索逻辑
    results = []
    query_lower = q.lower()
    
    for result in SEARCH_RESULTS_DATA:
        if (query_lower in result.title.lower() or 
            query_lower in result.description.lower()):
            results.append(result)
    
    # 按匹配度排序
    results.sort(key=lambda x: x.matchScore, reverse=True)
    return results

@router.post("/search/history")
def add_search_history(query: str):
    """添加搜索历史"""
    if query and query not in SEARCH_HISTORY_DATA:
        SEARCH_HISTORY_DATA.insert(0, query)
        # 保持历史记录不超过10条
        if len(SEARCH_HISTORY_DATA) > 10:
            SEARCH_HISTORY_DATA.pop()
    return {"success": True}

@router.delete("/search/history")
def clear_search_history():
    """清空搜索历史"""
    SEARCH_HISTORY_DATA.clear()
    return {"success": True} 