package com.shuimu.course.data.repository

import com.shuimu.course.data.remote.api.SearchApi
import com.shuimu.course.data.remote.dto.toDomain
import com.shuimu.course.domain.repository.SearchRepository
import com.shuimu.course.domain.repository.SearchResult
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SearchRepositoryImpl @Inject constructor(
    private val searchApi: SearchApi
) : SearchRepository {

    override suspend fun getSearchHistory(): List<String> {
        return try {
            val response = searchApi.getSearchHistory()
            if (response.isSuccessful) {
                response.body() ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun addSearchHistory(query: String) {
        try {
            searchApi.addSearchHistory(query)
        } catch (e: Exception) {
            // 忽略错误，搜索历史不是关键功能
        }
    }

    override suspend fun clearSearchHistory() {
        try {
            searchApi.clearSearchHistory()
        } catch (e: Exception) {
            // 忽略错误
        }
    }

    override suspend fun searchContent(query: String): List<SearchResult> {
        return try {
            val response = searchApi.searchContent(query)
            if (response.isSuccessful) {
                response.body()?.map { it.toDomain() } ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
} 