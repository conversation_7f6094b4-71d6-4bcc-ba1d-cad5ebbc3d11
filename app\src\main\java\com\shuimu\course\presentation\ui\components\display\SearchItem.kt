package com.shuimu.course.presentation.ui.components.display

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun SearchItem(
    modifier: Modifier = Modifier,
    title: String,
    category: String,
    isPurchased: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp, horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(id = android.R.drawable.ic_menu_search),
            contentDescription = "Search Result",
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )

        Spacer(Modifier.width(16.dp))

        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = category,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        if (!isPurchased) {
            Spacer(Modifier.width(16.dp))
            Icon(
                painter = painterResource(id = android.R.drawable.ic_lock_lock),
                contentDescription = "Locked",
                tint = Color.Gray,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

@Preview(name = "SearchItem - Purchased")
@Composable
fun SearchItemPurchasedPreview() {
    ShuimuCourseTheme {
        SearchItem(
            title = "深度沟通的艺术",
            category = "道：恋爱宝典系列·恋爱宝典1",
            isPurchased = true,
            onClick = {}
        )
    }
}

@Preview(name = "SearchItem - Not Purchased")
@Composable
fun SearchItemNotPurchasedPreview() {
    ShuimuCourseTheme {
        SearchItem(
            title = "开场白技巧",
            category = "术：聊天技术系列·聊天技术1",
            isPurchased = false,
            onClick = {}
        )
    }
} 