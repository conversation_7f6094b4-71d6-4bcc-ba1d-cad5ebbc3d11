package com.shuimu.course.data.local.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.PrimaryKey

@Entity(
    tableName = "play_progress",
    foreignKeys = [ForeignKey(
        entity = VideoEntity::class,
        parentColumns = ["id"],
        childColumns = ["video_id"],
        onDelete = ForeignKey.CASCADE
    )]
)
data class PlayProgressEntity(
    @PrimaryKey @ColumnInfo(name = "video_id") val videoId: String,
    @ColumnInfo(name = "watch_count") val watchCount: Int,
    @ColumnInfo(name = "last_position_seconds") val lastPositionSeconds: Long,
    @ColumnInfo(name = "total_duration_seconds") val totalDurationSeconds: Long,
    @ColumnInfo(name = "updated_at") val updatedAt: Long
) 