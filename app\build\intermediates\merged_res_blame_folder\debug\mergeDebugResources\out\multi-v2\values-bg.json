{"logs": [{"outputFile": "com.shuimu.course.app-mergeDebugResources-85:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60558d3ccad013c87c458ddc43ef9e3a\\transformed\\jetified-ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1017,1105,1177,1254,1332,1408,1491,1560", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,1012,1100,1172,1249,1327,1403,1486,1555,1676"}, "to": {"startLines": "66,67,68,70,71,128,129,252,253,255,256,260,262,263,264,266,267,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5324,5427,5520,5717,5820,10238,10314,22088,22179,22349,22433,22772,22923,23000,23078,23255,23338,23407", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "5422,5515,5618,5815,5899,10309,10400,22174,22258,22428,22516,22839,22995,23073,23149,23333,23402,23523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\886ce1740e58ab542309752b74bbc803\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "966,1086,1192,1297,1383,1493,1614,1694,1771,1862,1955,2050,2144,2244,2337,2432,2540,2631,2722,2805,2919,3027,3127,3241,3348,3456,3616,22521", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "1081,1187,1292,1378,1488,1609,1689,1766,1857,1950,2045,2139,2239,2332,2427,2535,2626,2717,2800,2914,3022,3122,3236,3343,3451,3611,3710,22600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\98772e44dd82187f64bd43b721368829\\transformed\\jetified-media3-exoplayer-1.3.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,189,253,327,405,478,575,666", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "121,184,248,322,400,473,570,661,737"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8143,8214,8277,8341,8415,8493,8566,8663,8754", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "8209,8272,8336,8410,8488,8561,8658,8749,8825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ed624b17e998c266cfbf9ad645cdecae\\transformed\\jetified-material3-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,432,551,648,744,857,987,1108,1255,1339,1438,1534,1630,1743,1872,1976,2119,2262,2407,2595,2735,2862,2992,3126,3223,3320,3457,3592,3695,3800,3905,4050,4200,4308,4411,4498,4590,4685,4798,4895,4985,5094,5174,5257,5357,5459,5555,5653,5741,5848,5948,6052,6171,6251,6361", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,112,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "169,290,427,546,643,739,852,982,1103,1250,1334,1433,1529,1625,1738,1867,1971,2114,2257,2402,2590,2730,2857,2987,3121,3218,3315,3452,3587,3690,3795,3900,4045,4195,4303,4406,4493,4585,4680,4793,4890,4980,5089,5169,5252,5352,5454,5550,5648,5736,5843,5943,6047,6166,6246,6356,6453"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10467,10586,10707,10844,10963,11060,11156,11269,11399,11520,11667,11751,11850,11946,12042,12155,12284,12388,12531,12674,12819,13007,13147,13274,13404,13538,13635,13732,13869,14004,14107,14212,14317,14462,14612,14720,14823,14910,15002,15097,15210,15307,15397,15506,15586,15669,15769,15871,15967,16065,16153,16260,16360,16464,16583,16663,16773", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,112,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "10581,10702,10839,10958,11055,11151,11264,11394,11515,11662,11746,11845,11941,12037,12150,12279,12383,12526,12669,12814,13002,13142,13269,13399,13533,13630,13727,13864,13999,14102,14207,14312,14457,14607,14715,14818,14905,14997,15092,15205,15302,15392,15501,15581,15664,15764,15866,15962,16060,16148,16255,16355,16459,16578,16658,16768,16865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad952cb150bedcbfcb57f35849ccf9e8\\transformed\\jetified-foundation-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "269,270", "startColumns": "4,4", "startOffsets": "23528,23616", "endColumns": "87,94", "endOffsets": "23611,23706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\c12dcf7643131f54deb56aa398bbca93\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "56,57,58,59,60,61,62,265", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4266,4363,4473,4575,4676,4783,4888,23154", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "4358,4468,4570,4671,4778,4883,5002,23250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\50a9f4044b9bccbf0a8c519c38d7ae77\\transformed\\jetified-media3-ui-1.3.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,500,706,796,887,970,1057,1145,1225,1290,1394,1499,1577,1651,1715,1783,1908,2029,2157,2234,2326,2398,2479,2580,2682,2748,2816,2870,2930,2978,3039,3111,3179,3242,3318,3383,3441,3512,3577,3648,3700,3759,3840,3921,3978", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,124,120,127,76,91,71,80,100,101,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56,68", "endOffsets": "282,495,701,791,882,965,1052,1140,1220,1285,1389,1494,1572,1646,1710,1778,1903,2024,2152,2229,2321,2393,2474,2575,2677,2743,2811,2865,2925,2973,3034,3106,3174,3237,3313,3378,3436,3507,3572,3643,3695,3754,3835,3916,3973,4042"}, "to": {"startLines": "2,11,15,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,595,6033,6123,6214,6297,6384,6472,6552,6617,6721,6826,6904,6978,7042,7110,7235,7356,7484,7561,7653,7725,7806,7907,8009,8075,8830,8884,8944,8992,9053,9125,9193,9256,9332,9397,9455,9526,9591,9662,9714,9773,9854,9935,9992", "endLines": "10,14,18,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,124,120,127,76,91,71,80,100,101,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56,68", "endOffsets": "377,590,796,6118,6209,6292,6379,6467,6547,6612,6716,6821,6899,6973,7037,7105,7230,7351,7479,7556,7648,7720,7801,7902,8004,8070,8138,8879,8939,8987,9048,9120,9188,9251,9327,9392,9450,9521,9586,9657,9709,9768,9849,9930,9987,10056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\60456bf525e41666fc62eb9c2882cd30\\transformed\\jetified-media3-session-1.3.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,227,295,364,448,536,639", "endColumns": "77,93,67,68,83,87,102,106", "endOffsets": "128,222,290,359,443,531,634,741"}, "to": {"startLines": "50,69,201,202,203,204,205,206", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3715,5623,17853,17921,17990,18074,18162,18265", "endColumns": "77,93,67,68,83,87,102,106", "endOffsets": "3788,5712,17916,17985,18069,18157,18260,18367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad601f1f1ae6fb89202723eea59af52d\\transformed\\material-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1118,1184,1281,1361,1423,1512,1575,1640,1699,1772,1835,1889,2017,2074,2136,2190,2263,2406,2490,2568,2661,2743,2831,2967,3055,3143,3279,3364,3441,3494,3545,3611,3686,3762,3833,3912,3989,4065,4142,4216,4328,4419,4494,4585,4677,4751,4838,4929,4984,5066,5132,5215,5301,5363,5427,5490,5560,5677,5789,5900,6010,6067,6122,6208,6299,6375", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "260,339,418,501,623,733,828,961,1050,1113,1179,1276,1356,1418,1507,1570,1635,1694,1767,1830,1884,2012,2069,2131,2185,2258,2401,2485,2563,2656,2738,2826,2962,3050,3138,3274,3359,3436,3489,3540,3606,3681,3757,3828,3907,3984,4060,4137,4211,4323,4414,4489,4580,4672,4746,4833,4924,4979,5061,5127,5210,5296,5358,5422,5485,5555,5672,5784,5895,6005,6062,6117,6203,6294,6370,6449"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,72,73,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,254,258,259,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "801,3793,3872,3951,4034,4156,5007,5102,5235,5904,5967,10061,10158,10405,16870,16959,17022,17087,17146,17219,17282,17336,17464,17521,17583,17637,17710,18372,18456,18534,18627,18709,18797,18933,19021,19109,19245,19330,19407,19460,19511,19577,19652,19728,19799,19878,19955,20031,20108,20182,20294,20385,20460,20551,20643,20717,20804,20895,20950,21032,21098,21181,21267,21329,21393,21456,21526,21643,21755,21866,21976,22033,22263,22605,22696,22844", "endLines": "22,51,52,53,54,55,63,64,65,72,73,126,127,130,188,189,190,191,192,193,194,195,196,197,198,199,200,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,254,258,259,261", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "961,3867,3946,4029,4151,4261,5097,5230,5319,5962,6028,10153,10233,10462,16954,17017,17082,17141,17214,17277,17331,17459,17516,17578,17632,17705,17848,18451,18529,18622,18704,18792,18928,19016,19104,19240,19325,19402,19455,19506,19572,19647,19723,19794,19873,19950,20026,20103,20177,20289,20380,20455,20546,20638,20712,20799,20890,20945,21027,21093,21176,21262,21324,21388,21451,21521,21638,21750,21861,21971,22028,22083,22344,22691,22767,22918"}}]}]}