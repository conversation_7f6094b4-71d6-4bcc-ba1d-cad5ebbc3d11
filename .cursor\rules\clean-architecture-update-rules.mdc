---
description: USE WHEN 修改 Domain / DTO / Entity 时，必须按「Domain→Data→Presentation」六层顺序同步更新
globs: 
alwaysApply: false
---
# Clean Architecture 更新规则

- 以‘Hi，我将按照Clean Architecture 更新规则执行’开头

## 核心原则：从内向外更新 (Inside-Out)

遵循 Clean Architecture 分层原则，任何数据模型变更必须按照以下顺序进行：

```
Domain Layer → Data Layer → Presentation Layer
```

## 更新顺序检查清单

### 第1层：Domain 核心层 (最高优先级)
- [ ] [domain/model/*.kt](mdc:app/src/main/java/com/shuimu/course/domain/model) - 业务实体模型
- [ ] [mock_server/src/data/*.json](mdc:mock_server/src/data) - 测试数据
- [ ] [mock_server/src/models/*.py](mdc:mock_server/src/models) - 服务器Pydantic模型

### 第2层：Data 数据传输层
- [ ] [data/remote/dto/*.kt](mdc:app/src/main/java/com/shuimu/course/data/remote/dto) - 网络传输对象
- [ ] [data/local/entities/*.kt](mdc:app/src/main/java/com/shuimu/course/data/local/entities) - 数据库实体

### 第3层：Data 持久化层
- [ ] [data/local/database/AppDatabase.kt](mdc:app/src/main/java/com/shuimu/course/data/local/database/AppDatabase.kt) - 数据库版本更新
- [ ] [di/DatabaseModule.kt](mdc:app/src/main/java/com/shuimu/course/di/DatabaseModule.kt) - 数据库迁移
- [ ] [data/Mappers.kt](mdc:app/src/main/java/com/shuimu/course/data/Mappers.kt) - 数据转换映射

### 第4层：Repository 仓库层
- [ ] [domain/repository/*.kt](mdc:app/src/main/java/com/shuimu/course/domain/repository) - 仓库接口
- [ ] [data/repository/*Impl.kt](mdc:app/src/main/java/com/shuimu/course/data/repository) - 仓库实现
- [ ] [di/RepositoryModule.kt](mdc:app/src/main/java/com/shuimu/course/di/RepositoryModule.kt) - 依赖注入绑定

### 第5层：Presentation 业务逻辑层
- [ ] [presentation/viewmodel/*.kt](mdc:app/src/main/java/com/shuimu/course/presentation/viewmodel) - ViewModel状态管理

### 第6层：Presentation UI层 (最低优先级)
- [ ] [presentation/ui/screens/*.kt](mdc:app/src/main/java/com/shuimu/course/presentation/ui/screens) - UI界面组件

## 关联性检查矩阵

### 修改 Domain Model 时必须同步：
- ✅ 对应的 DTO 类（网络层）
- ✅ 对应的 Entity 类（数据库层）
- ✅ 所有相关的 Mapper 函数
- ✅ 数据库版本号 + Migration
- ✅ Mock 数据和服务器模型

### 修改 Entity 时必须同步：
- ✅ 数据库版本号递增
- ✅ 添加对应的 Migration
- ✅ 更新 Entity→Domain 的 Mapper
- ✅ 检查 Room DAO 方法兼容性

### 修改 Repository 时必须同步：
- ✅ 对应的接口定义
- ✅ DI 模块中的 @Binds 注解
- ✅ 所有使用该 Repository 的 ViewModel

## 数据库迁移规范

### 版本管理：
```kotlin
@Database(
    entities = [...],
    version = X,  // 每次Entity变更必须递增
    exportSchema = false  // 生产环境建议false
)
```

### Migration 模式：
```kotlin
private val MIGRATION_X_Y = object : Migration(X, Y) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 使用 ALTER TABLE 保证数据安全
        database.execSQL("ALTER TABLE table_name ADD COLUMN new_field TYPE DEFAULT value")
    }
}
```

## 验证步骤

### 编译验证：
```bash
./gradlew assembleDebug
```

### 数据流验证：
确保以下链路完整：
```
Server JSON → DTO → Domain → Entity → UI
```

### 常见遗漏检查：
- 🔍 Mapper 函数中是否包含新字段
- 🔍 DI 模块是否注册新的 Repository
- 🔍 数据库迁移是否安全（使用ALTER不是DROP）
- 🔍 Mock 数据是否包含新字段

## 禁止操作

❌ **绝不允许**：
- 跳过任何层级直接修改UI
- 忘记更新数据库版本号
- 使用 `.fallbackToDestructiveMigration()`（会删除用户数据）
- 修改Entity后不添加Migration

✅ **必须遵循**：
- 按层级顺序更新
- 每次变更都要编译验证
- 保证数据向后兼容
- 维护Clean Architecture边界



