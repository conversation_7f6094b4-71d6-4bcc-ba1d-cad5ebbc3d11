package com.shuimu.course.domain.usecase.orders

import com.shuimu.course.domain.model.orders.Order
import com.shuimu.course.domain.repository.UserRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

class GetOrdersUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    operator fun invoke(): Flow<Resource<List<Order>>> = flow {
        emit(Resource.Loading())
        try {
            val result = userRepository.getOrders()
            emit(result)
        } catch (e: Exception) {
            emit(Resource.Error(e.localizedMessage ?: "An unexpected error occurred"))
        }
    }
} 