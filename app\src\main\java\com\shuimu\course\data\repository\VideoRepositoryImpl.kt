package com.shuimu.course.data.repository

import com.shuimu.course.data.local.dao.VideoDao
import com.shuimu.course.data.remote.api.VideoApi
import com.shuimu.course.data.toEntity
import com.shuimu.course.data.toModel
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.VideoRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VideoRepositoryImpl @Inject constructor(
    private val videoApi: VideoApi,
    private val videoDao: VideoDao
) : VideoRepository {

    override suspend fun getVideoById(videoId: String): Video? {
        return try {
            // 首先尝试从服务器获取
            val response = videoApi.getVideoDetails(videoId)
            if (response.isSuccessful && response.body() != null) {
                val videoDto = response.body()!!
                val video = videoDto.toModel()
                
                // 缓存到本地数据库
                videoDao.insertVideos(listOf(video.toEntity()))
                
                video
            } else {
                // 如果服务器返回错误，从本地数据库获取
                videoDao.getVideoById(videoId)?.toModel()
            }
        } catch (e: Exception) {
            // 如果网络失败，从本地数据库获取
            videoDao.getVideoById(videoId)?.toModel()
        }
    }

    override suspend fun updateVideoProgress(videoId: String, progress: Float) {
        try {
            // 更新服务器
            val response = videoApi.updateVideoProgress(videoId, (progress * 100).toInt())
            
            // 更新本地缓存
            val entity = videoDao.getVideoById(videoId)
            entity?.let {
                val updatedEntity = it.copy(
                    progress = progress
                )
                videoDao.insertVideos(listOf(updatedEntity))
            }
        } catch (e: Exception) {
            // 网络失败时只更新本地
            val entity = videoDao.getVideoById(videoId)
            entity?.let {
                val updatedEntity = it.copy(
                    progress = progress
                )
                videoDao.insertVideos(listOf(updatedEntity))
            }
        }
    }

    override suspend fun incrementWatchCount(videoId: String) {
        try {
            videoApi.updateWatchCount(videoId)
        } catch (e: Exception) {
            // 忽略网络错误，观看次数不是关键数据
        }
    }

    override fun getVideosForCategory(categoryId: String): Flow<List<Video>> {
        return videoDao.getVideosForCategory(categoryId).map { entities ->
            entities.map { it.toModel() }
        }
    }
    
    override suspend fun deleteCachedVideo(videoId: String): Result<Unit> {
        return try {
            // 1. 立即更新本地数据库（本地优先原则）
            videoDao.updateVideoCacheStatus(
                videoId = videoId,
                cacheStatus = CacheStatus.NOT_CACHED.value,
                localPath = null
            )
            
            // 2. Fire & Forget: 异步更新服务器状态
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    videoApi.updateVideoCacheStatus(videoId, false)
                } catch (e: Exception) {
                    // 服务器更新失败不影响本地结果
                    // 可以在这里添加日志或重试机制
                }
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}