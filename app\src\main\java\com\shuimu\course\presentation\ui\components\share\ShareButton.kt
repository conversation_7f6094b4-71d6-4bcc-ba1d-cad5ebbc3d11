package com.shuimu.course.presentation.ui.components.share

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun ShareButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    IconButton(
        modifier = modifier,
        onClick = onClick
    ) {
        Icon(
            imageVector = Icons.Default.Share,
            contentDescription = "Share"
        )
    }
}

@Preview
@Composable
fun ShareButtonPreview() {
    ShuimuCourseTheme {
        ShareButton(onClick = {})
    }
} 