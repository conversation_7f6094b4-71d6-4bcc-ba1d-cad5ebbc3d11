package com.shuimu.course.data.workers;

import androidx.hilt.work.WorkerAssistedFactory;
import androidx.work.ListenableWorker;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.multibindings.IntoMap;
import dagger.multibindings.StringKey;
import javax.annotation.processing.Generated;

@Generated("androidx.hilt.AndroidXHiltProcessor")
@Module
@InstallIn(SingletonComponent.class)
@OriginatingElement(
    topLevelClass = VideoDownloadWorker.class
)
public interface VideoDownloadWorker_HiltModule {
  @Binds
  @IntoMap
  @StringKey("com.shuimu.course.data.workers.VideoDownloadWorker")
  WorkerAssistedFactory<? extends ListenableWorker> bind(
      VideoDownloadWorker_AssistedFactory factory);
}
