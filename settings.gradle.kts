pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        // JitPack repository for some libraries if needed
        maven { url = uri("https://jitpack.io") }
    }
}
rootProject.name = "ShuimuVideoCourse"
include(":app") 