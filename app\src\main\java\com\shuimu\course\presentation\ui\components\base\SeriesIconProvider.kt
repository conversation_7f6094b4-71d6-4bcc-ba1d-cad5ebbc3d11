package com.shuimu.course.presentation.ui.components.base

import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Comment
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.PlayCircle
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.course.R

object SeriesIconProvider {
    
    @Composable
    fun SeriesIcon(
        iconKey: String?,
        modifier: Modifier = Modifier,
        size: Dp = 28.dp
    ) {
        // 图标别名映射，增强兼容性
        val iconAlias = mapOf(
            "play" to "play", "fa-play-circle" to "play",
            "heart" to "heart", "fa-heart" to "heart", 
            "comment" to "comment", "fa-comments" to "comment",
            "crown" to "crown", "package" to "crown", "fa-crown" to "crown"
        )
        val normalizedKey = iconAlias[iconKey] ?: iconKey
        
        // 特殊处理：crown图标使用emoji
        if (normalizedKey == "crown") {
            Text(
                text = "👑",
                fontSize = size.value.sp,
                modifier = modifier
            )
            return
        }
        
        val (painter, color) = getIconResource(normalizedKey)
        
        if (painter != null) {
            Icon(
                painter = painter,
                contentDescription = "Series Icon",
                tint = color,
                modifier = modifier.size(size)
            )
        } else {
            // 使用Material Icons作为fallback
            val (vector, vectorColor) = getMaterialIconFallback(normalizedKey)
            Icon(
                imageVector = vector,
                contentDescription = "Series Icon",
                tint = vectorColor,
                modifier = modifier.size(size)
            )
        }
    }
    
    @Composable
    private fun getIconResource(iconKey: String?): Pair<Painter?, Color> {
        return when (iconKey) {
            "play" -> painterResource(id = R.drawable.ic_play_circle) to Color(0xFF10B981) // Green
            "heart" -> painterResource(id = R.drawable.ic_heart) to Color(0xFFEF4444) // Red
            "comment" -> painterResource(id = R.drawable.ic_comments_fa) to Color(0xFFEC4899) // Pink - Font Awesome
            // crown不在这里处理，已经在上面用emoji处理了
            else -> null to Color.Gray
        }
    }
    
    @Composable
    private fun getMaterialIconFallback(iconKey: String?): Pair<ImageVector, Color> {
        return when (iconKey) {
            "play" -> Icons.Default.PlayCircle to Color(0xFF10B981)
            "heart" -> Icons.Default.Favorite to Color(0xFFEF4444)
            "comment" -> Icons.AutoMirrored.Filled.Comment to Color(0xFFEC4899)
            // crown不在这里处理，已经在上面用emoji处理了
            else -> Icons.Default.Favorite to Color.Gray
        }
    }
}

// 为了兼容现有代码，提供简单的函数版本
@Composable
fun SeriesIcon(
    iconKey: String?,
    modifier: Modifier = Modifier,
    size: Dp = 28.dp
) {
    SeriesIconProvider.SeriesIcon(
        iconKey = iconKey,
        modifier = modifier,
        size = size
    )
} 