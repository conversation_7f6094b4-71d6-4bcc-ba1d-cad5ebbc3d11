package com.shuimu.course.data.workers

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.io.*
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 增强的下载引擎
 * 支持断点续传、网络状态监听、下载速度控制、多线程分片下载
 */
@Singleton
class EnhancedDownloadEngine @Inject constructor(
    private val context: Context
) {
    companion object {
        private const val TAG = "EnhancedDownloadEngine"
        private const val CHUNK_SIZE = 8192 // 8KB
        private const val MAX_THREADS = 4 // 最大下载线程数
        private const val SPEED_LIMIT_BYTES_PER_SECOND = 1024 * 1024 // 1MB/s 默认限速
        private const val NETWORK_CHECK_INTERVAL = 5000L // 5秒检查一次网络
    }

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val _networkState = MutableStateFlow(NetworkState.UNKNOWN)
    val networkState: StateFlow<NetworkState> = _networkState
    
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            updateNetworkState()
        }
        
        override fun onLost(network: Network) {
            updateNetworkState()
        }
        
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            updateNetworkState()
        }
    }
    
    init {
        registerNetworkCallback()
        updateNetworkState()
    }
    
    /**
     * 网络状态枚举
     */
    enum class NetworkState {
        UNKNOWN,
        WIFI,
        CELLULAR,
        NO_NETWORK
    }
    
    /**
     * 下载进度回调
     */
    data class DownloadProgress(
        val downloadedBytes: Long,
        val totalBytes: Long,
        val progress: Int,
        val speed: Long // bytes per second
    )
    
    /**
     * 下载配置
     */
    data class DownloadConfig(
        val enableResume: Boolean = true,
        val maxThreads: Int = MAX_THREADS,
        val speedLimitBytesPerSecond: Long = SPEED_LIMIT_BYTES_PER_SECOND,
        val requireWifi: Boolean = false,
        val retryCount: Int = 3,
        val retryDelay: Long = 1000L
    )
    
    /**
     * 启动增强下载
     */
    suspend fun downloadFile(
        url: String,
        outputFile: File,
        config: DownloadConfig = DownloadConfig(),
        onProgress: (DownloadProgress) -> Unit = {},
        onNetworkWaiting: () -> Unit = {}
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始下载: $url -> ${outputFile.absolutePath}")
            
            // 检查网络状态
            if (!isNetworkSuitable(config.requireWifi)) {
                Log.w(TAG, "网络条件不满足，等待合适网络")
                onNetworkWaiting()
                waitForSuitableNetwork(config.requireWifi)
            }
            
            // 获取文件信息
            val fileInfo = getFileInfo(url)
            val totalBytes = fileInfo.contentLength
            val supportsResume = fileInfo.supportsResume
            
            Log.d(TAG, "文件大小: $totalBytes bytes, 支持断点续传: $supportsResume")
            
            // 检查已下载的部分
            val existingBytes = if (outputFile.exists() && config.enableResume && supportsResume) {
                outputFile.length()
            } else {
                0L
            }
            
            if (existingBytes >= totalBytes) {
                Log.d(TAG, "文件已完整下载")
                return@withContext Result.success(Unit)
            }
            
            // 执行下载
            if (supportsResume && totalBytes > CHUNK_SIZE * 10 && config.maxThreads > 1) {
                // 多线程分片下载
                downloadWithMultipleThreads(url, outputFile, existingBytes, totalBytes, config, onProgress)
            } else {
                // 单线程下载
                downloadWithSingleThread(url, outputFile, existingBytes, totalBytes, config, onProgress)
            }
            
            Log.d(TAG, "下载完成: ${outputFile.absolutePath}")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "下载失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取文件信息
     */
    private suspend fun getFileInfo(url: String): FileInfo = withContext(Dispatchers.IO) {
        val connection = URL(url).openConnection() as HttpURLConnection
        try {
            connection.requestMethod = "HEAD"
            connection.connectTimeout = 10000
            connection.readTimeout = 10000
            connection.connect()
            
            val contentLength = connection.getHeaderField("Content-Length")?.toLongOrNull() ?: -1L
            val acceptRanges = connection.getHeaderField("Accept-Ranges")
            val supportsResume = acceptRanges?.equals("bytes", ignoreCase = true) == true
            
            FileInfo(contentLength, supportsResume)
        } finally {
            connection.disconnect()
        }
    }
    
    /**
     * 单线程下载
     */
    private suspend fun downloadWithSingleThread(
        url: String,
        outputFile: File,
        startBytes: Long,
        totalBytes: Long,
        config: DownloadConfig,
        onProgress: (DownloadProgress) -> Unit
    ) = withContext(Dispatchers.IO) {
        val connection = URL(url).openConnection() as HttpURLConnection
        try {
            connection.requestMethod = "GET"
            connection.connectTimeout = 10000
            connection.readTimeout = 30000
            
            if (startBytes > 0) {
                connection.setRequestProperty("Range", "bytes=$startBytes-")
            }
            
            connection.connect()
            
            val inputStream = connection.inputStream
            val outputStream = FileOutputStream(outputFile, startBytes > 0)
            
            downloadWithSpeedControl(
                inputStream, outputStream, startBytes, totalBytes, 
                config.speedLimitBytesPerSecond, onProgress
            )
            
        } finally {
            connection.disconnect()
        }
    }
    
    /**
     * 多线程分片下载
     */
    private suspend fun downloadWithMultipleThreads(
        url: String,
        outputFile: File,
        startBytes: Long,
        totalBytes: Long,
        config: DownloadConfig,
        onProgress: (DownloadProgress) -> Unit
    ) = withContext(Dispatchers.IO) {
        val remainingBytes = totalBytes - startBytes
        val chunkSize = remainingBytes / config.maxThreads
        val downloadedBytes = AtomicLong(startBytes)
        val lastProgressTime = AtomicLong(System.currentTimeMillis())
        
        // 创建临时文件用于分片下载
        val tempFiles = mutableListOf<File>()
        val jobs = mutableListOf<Job>()
        
        try {
            for (i in 0 until config.maxThreads) {
                val chunkStart = startBytes + i * chunkSize
                val chunkEnd = if (i == config.maxThreads - 1) {
                    totalBytes - 1
                } else {
                    startBytes + (i + 1) * chunkSize - 1
                }
                
                val tempFile = File(outputFile.parent, "${outputFile.name}.part$i")
                tempFiles.add(tempFile)
                
                val job = launch {
                    downloadChunk(url, tempFile, chunkStart, chunkEnd, downloadedBytes, totalBytes, onProgress)
                }
                jobs.add(job)
            }
            
            // 等待所有分片下载完成
            jobs.joinAll()
            
            // 合并分片文件
            mergeChunks(tempFiles, outputFile, startBytes > 0)
            
        } finally {
            // 清理临时文件
            tempFiles.forEach { it.delete() }
        }
    }
    
    /**
     * 下载单个分片
     */
    private suspend fun downloadChunk(
        url: String,
        tempFile: File,
        startByte: Long,
        endByte: Long,
        totalDownloaded: AtomicLong,
        totalBytes: Long,
        onProgress: (DownloadProgress) -> Unit
    ) = withContext(Dispatchers.IO) {
        val connection = URL(url).openConnection() as HttpURLConnection
        try {
            connection.requestMethod = "GET"
            connection.setRequestProperty("Range", "bytes=$startByte-$endByte")
            connection.connectTimeout = 10000
            connection.readTimeout = 30000
            connection.connect()
            
            val inputStream = connection.inputStream
            val outputStream = FileOutputStream(tempFile)
            
            val buffer = ByteArray(CHUNK_SIZE)
            var bytesRead: Int
            
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
                
                val currentTotal = totalDownloaded.addAndGet(bytesRead.toLong())
                val progress = ((currentTotal * 100) / totalBytes).toInt()
                
                onProgress(DownloadProgress(currentTotal, totalBytes, progress, 0))
            }
            
            outputStream.close()
            inputStream.close()
            
        } finally {
            connection.disconnect()
        }
    }
    
    /**
     * 合并分片文件
     */
    private suspend fun mergeChunks(tempFiles: List<File>, outputFile: File, append: Boolean) = withContext(Dispatchers.IO) {
        val outputStream = FileOutputStream(outputFile, append)
        try {
            tempFiles.forEach { tempFile ->
                if (tempFile.exists()) {
                    val inputStream = FileInputStream(tempFile)
                    inputStream.copyTo(outputStream)
                    inputStream.close()
                }
            }
        } finally {
            outputStream.close()
        }
    }
    
    /**
     * 带速度控制的下载
     */
    private suspend fun downloadWithSpeedControl(
        inputStream: InputStream,
        outputStream: OutputStream,
        startBytes: Long,
        totalBytes: Long,
        speedLimitBytesPerSecond: Long,
        onProgress: (DownloadProgress) -> Unit
    ) = withContext(Dispatchers.IO) {
        val buffer = ByteArray(CHUNK_SIZE)
        var bytesRead: Int
        var totalRead = startBytes
        var lastTime = System.currentTimeMillis()
        var bytesInSecond = 0L
        
        try {
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
                totalRead += bytesRead
                bytesInSecond += bytesRead
                
                val currentTime = System.currentTimeMillis()
                val timeDiff = currentTime - lastTime
                
                // 每秒更新一次进度和速度控制
                if (timeDiff >= 1000) {
                    val speed = bytesInSecond * 1000 / timeDiff
                    val progress = ((totalRead * 100) / totalBytes).toInt()
                    
                    onProgress(DownloadProgress(totalRead, totalBytes, progress, speed))
                    
                    // 速度控制
                    if (speed > speedLimitBytesPerSecond) {
                        val delayMs = ((bytesInSecond - speedLimitBytesPerSecond) * 1000 / speedLimitBytesPerSecond).coerceAtLeast(0)
                        if (delayMs > 0) {
                            delay(delayMs)
                        }
                    }
                    
                    lastTime = currentTime
                    bytesInSecond = 0L
                }
            }
        } finally {
            outputStream.close()
            inputStream.close()
        }
    }
    
    /**
     * 注册网络状态监听
     */
    private fun registerNetworkCallback() {
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        connectivityManager.registerNetworkCallback(request, networkCallback)
    }
    
    /**
     * 更新网络状态
     */
    private fun updateNetworkState() {
        val activeNetwork = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        _networkState.value = when {
            capabilities == null -> NetworkState.NO_NETWORK
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkState.WIFI
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkState.CELLULAR
            else -> NetworkState.UNKNOWN
        }
    }
    
    /**
     * 检查网络是否合适
     */
    private fun isNetworkSuitable(requireWifi: Boolean): Boolean {
        return when (_networkState.value) {
            NetworkState.WIFI -> true
            NetworkState.CELLULAR -> !requireWifi
            else -> false
        }
    }
    
    /**
     * 等待合适的网络
     */
    private suspend fun waitForSuitableNetwork(requireWifi: Boolean) {
        while (!isNetworkSuitable(requireWifi)) {
            delay(NETWORK_CHECK_INTERVAL)
        }
    }
    
    /**
     * 文件信息
     */
    private data class FileInfo(
        val contentLength: Long,
        val supportsResume: Boolean
    )
    
    fun cleanup() {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }
}
