package com.shuimu.course.data.repository

import com.shuimu.course.BuildConfig
import com.shuimu.course.data.local.dao.UserDao
import com.shuimu.course.data.remote.api.AuthApi
import com.shuimu.course.data.remote.api.LoginRequest
import com.shuimu.course.data.remote.api.UserApi
import com.shuimu.course.data.remote.dto.orders.OrderDto
import com.shuimu.course.data.remote.dto.orders.OrderItemDto
import com.shuimu.course.data.remote.dto.profile.ProfileDataDto
import com.shuimu.course.data.toEntity
import com.shuimu.course.data.toDomain
import com.shuimu.course.domain.model.User
import com.shuimu.course.domain.model.orders.Order
import com.shuimu.course.domain.model.orders.OrderItem
import com.shuimu.course.domain.model.profile.UserProfile
import com.shuimu.course.domain.model.profile.UserStats
import com.shuimu.course.domain.repository.UserRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import retrofit2.HttpException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import com.google.gson.JsonSyntaxException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepositoryImpl @Inject constructor(
    private val authApi: AuthApi,
    private val userApi: UserApi,
    private val userDao: UserDao
) : UserRepository {

    override fun getUserState(): Flow<User?> {
        // This flow will automatically update when the user table changes.
        return userDao.getCurrentUser().map { it?.toDomain() }
    }

    override suspend fun login(username: String, password: String): Flow<Resource<Unit>> = flow {
        emit(Resource.Loading())
        try {
            val response = authApi.login(LoginRequest(username, password))
            if (response.isSuccessful && response.body() != null) {
                // Here you would typically get user profile and save it
                // For mock, we can assume a successful login saves a mock user
                val mockUser = User(id = BuildConfig.MOCK_USER_ID, username = username, nickname = "张三", avatarUrl = null)
                userDao.insertUser(mockUser.toEntity())
                emit(Resource.Success(Unit))
            } else {
                emit(Resource.Error("登录失败，请检查用户名和密码"))
            }
        } catch (e: JsonSyntaxException) {
            emit(Resource.Error("数据格式异常，可能是服务器数据结构变更"))
        } catch (e: UnknownHostException) {
            emit(Resource.Error("DNS解析失败，请检查网络设置"))
        } catch (e: SocketTimeoutException) {
            emit(Resource.Error("连接超时，请检查网络连接"))
        } catch (e: IOException) {
            emit(Resource.Error("网络连接失败，请检查网络状态"))
        } catch (e: HttpException) {
            when (e.code()) {
                401 -> emit(Resource.Error("认证失败，请重新登录"))
                404 -> emit(Resource.Error("服务不存在，请联系技术支持"))
                500 -> emit(Resource.Error("服务器内部错误，请稍后重试"))
                else -> emit(Resource.Error("服务器响应异常：${e.code()}"))
            }
        } catch (e: Exception) {
            emit(Resource.Error("未知错误：${e.message}"))
        }
    }

    override suspend fun logout() {
        // For mock, we can just clear the user table or a specific user
        // In a real app, you'd clear tokens and user data
    }

    override suspend fun getProfileData(): Resource<UserProfile> {
        return try {
            val response = userApi.getProfileData()
            Resource.Success(response.toDomain())
        } catch (e: JsonSyntaxException) {
            Resource.Error("用户数据格式异常，可能是服务器数据结构变更")
        } catch (e: UnknownHostException) {
            Resource.Error("DNS解析失败，请检查网络设置")
        } catch (e: SocketTimeoutException) {
            Resource.Error("连接超时，请检查网络连接")
        } catch (e: IOException) {
            Resource.Error("网络连接失败，请检查网络状态")
        } catch (e: HttpException) {
            when (e.code()) {
                401 -> Resource.Error("认证失败，请重新登录")
                404 -> Resource.Error("用户信息不存在")
                500 -> Resource.Error("服务器内部错误，请稍后重试")
                else -> Resource.Error("服务器响应异常：${e.code()}")
            }
        } catch (e: Exception) {
            Resource.Error("获取用户信息失败：${e.message}")
        }
    }

    override suspend fun getOrders(): Resource<List<Order>> {
        return try {
            val response = userApi.getOrders()
            Resource.Success(response.map { it.toDomain() })
        } catch (e: JsonSyntaxException) {
            Resource.Error("订单数据格式异常，可能是服务器数据结构变更")
        } catch (e: UnknownHostException) {
            Resource.Error("DNS解析失败，请检查网络设置")
        } catch (e: SocketTimeoutException) {
            Resource.Error("连接超时，请检查网络连接")
        } catch (e: IOException) {
            Resource.Error("网络连接失败，请检查网络状态")
        } catch (e: HttpException) {
            when (e.code()) {
                401 -> Resource.Error("认证失败，请重新登录")
                404 -> Resource.Error("订单信息不存在")
                500 -> Resource.Error("服务器内部错误，请稍后重试")
                else -> Resource.Error("服务器响应异常：${e.code()}")
            }
        } catch (e: Exception) {
            Resource.Error("获取订单信息失败：${e.message}")
        }
    }
}

private fun ProfileDataDto.toDomain(): UserProfile {
    return UserProfile(
        id = this.user.id,
        nickname = this.user.nickname,
        avatarUrl = this.user.avatarUrl,
        learningProgress = this.user.learningProgress,
        shareEarnings = this.user.shareEarnings,
        stats = this.stats.toDomain()
    )
}

private fun com.shuimu.course.data.remote.dto.profile.UserStatsDto.toDomain(): UserStats {
    return UserStats(
        myCoursesCount = this.myCoursesCount,
        favoritesCount = this.favoritesCount,
        viewingHistoryCount = this.viewingHistoryCount
    )
}

private fun OrderDto.toDomain(): Order {
    return Order(
        id = this.id,
        title = this.title,
        status = this.status,
        price = this.price,
        date = this.date,
        items = this.items.map { it.toDomain() },
        paymentMethod = this.paymentMethod,
        shareEarningsApplied = this.shareEarningsApplied
    )
}

private fun OrderItemDto.toDomain(): OrderItem {
    return OrderItem(
        name = this.name,
        price = this.price
    )
} 