package com.shuimu.course.data.repository;

import com.shuimu.course.data.local.dao.CacheInfoDao;
import com.shuimu.course.data.local.dao.CategoryDao;
import com.shuimu.course.data.local.dao.PlayProgressDao;
import com.shuimu.course.data.local.dao.SeriesDao;
import com.shuimu.course.data.local.dao.VideoDao;
import com.shuimu.course.data.manager.DataLayerManager;
import com.shuimu.course.data.remote.api.SeriesApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SeriesRepositoryImpl_Factory implements Factory<SeriesRepositoryImpl> {
  private final Provider<SeriesApi> seriesApiProvider;

  private final Provider<SeriesDao> seriesDaoProvider;

  private final Provider<CategoryDao> categoryDaoProvider;

  private final Provider<VideoDao> videoDaoProvider;

  private final Provider<PlayProgressDao> playProgressDaoProvider;

  private final Provider<CacheInfoDao> cacheInfoDaoProvider;

  private final Provider<DataLayerManager> dataLayerManagerProvider;

  public SeriesRepositoryImpl_Factory(Provider<SeriesApi> seriesApiProvider,
      Provider<SeriesDao> seriesDaoProvider, Provider<CategoryDao> categoryDaoProvider,
      Provider<VideoDao> videoDaoProvider, Provider<PlayProgressDao> playProgressDaoProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider,
      Provider<DataLayerManager> dataLayerManagerProvider) {
    this.seriesApiProvider = seriesApiProvider;
    this.seriesDaoProvider = seriesDaoProvider;
    this.categoryDaoProvider = categoryDaoProvider;
    this.videoDaoProvider = videoDaoProvider;
    this.playProgressDaoProvider = playProgressDaoProvider;
    this.cacheInfoDaoProvider = cacheInfoDaoProvider;
    this.dataLayerManagerProvider = dataLayerManagerProvider;
  }

  @Override
  public SeriesRepositoryImpl get() {
    return newInstance(seriesApiProvider.get(), seriesDaoProvider.get(), categoryDaoProvider.get(), videoDaoProvider.get(), playProgressDaoProvider.get(), cacheInfoDaoProvider.get(), dataLayerManagerProvider.get());
  }

  public static SeriesRepositoryImpl_Factory create(Provider<SeriesApi> seriesApiProvider,
      Provider<SeriesDao> seriesDaoProvider, Provider<CategoryDao> categoryDaoProvider,
      Provider<VideoDao> videoDaoProvider, Provider<PlayProgressDao> playProgressDaoProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider,
      Provider<DataLayerManager> dataLayerManagerProvider) {
    return new SeriesRepositoryImpl_Factory(seriesApiProvider, seriesDaoProvider, categoryDaoProvider, videoDaoProvider, playProgressDaoProvider, cacheInfoDaoProvider, dataLayerManagerProvider);
  }

  public static SeriesRepositoryImpl newInstance(SeriesApi seriesApi, SeriesDao seriesDao,
      CategoryDao categoryDao, VideoDao videoDao, PlayProgressDao playProgressDao,
      CacheInfoDao cacheInfoDao, DataLayerManager dataLayerManager) {
    return new SeriesRepositoryImpl(seriesApi, seriesDao, categoryDao, videoDao, playProgressDao, cacheInfoDao, dataLayerManager);
  }
}
