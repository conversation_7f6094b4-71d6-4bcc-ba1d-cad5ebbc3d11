package com.shuimu.course.data.repository;

import com.shuimu.course.data.local.dao.CacheInfoDao;
import com.shuimu.course.data.local.dao.CategoryDao;
import com.shuimu.course.data.local.dao.PlayProgressDao;
import com.shuimu.course.data.local.dao.SeriesDao;
import com.shuimu.course.data.local.dao.VideoDao;
import com.shuimu.course.data.remote.api.SeriesApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SeriesRepositoryImpl_Factory implements Factory<SeriesRepositoryImpl> {
  private final Provider<SeriesApi> seriesApiProvider;

  private final Provider<SeriesDao> seriesDaoProvider;

  private final Provider<CategoryDao> categoryDaoProvider;

  private final Provider<VideoDao> videoDaoProvider;

  private final Provider<PlayProgressDao> playProgressDaoProvider;

  private final Provider<CacheInfoDao> cacheInfoDaoProvider;

  public SeriesRepositoryImpl_Factory(Provider<SeriesApi> seriesApiProvider,
      Provider<SeriesDao> seriesDaoProvider, Provider<CategoryDao> categoryDaoProvider,
      Provider<VideoDao> videoDaoProvider, Provider<PlayProgressDao> playProgressDaoProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider) {
    this.seriesApiProvider = seriesApiProvider;
    this.seriesDaoProvider = seriesDaoProvider;
    this.categoryDaoProvider = categoryDaoProvider;
    this.videoDaoProvider = videoDaoProvider;
    this.playProgressDaoProvider = playProgressDaoProvider;
    this.cacheInfoDaoProvider = cacheInfoDaoProvider;
  }

  @Override
  public SeriesRepositoryImpl get() {
    return newInstance(seriesApiProvider.get(), seriesDaoProvider.get(), categoryDaoProvider.get(), videoDaoProvider.get(), playProgressDaoProvider.get(), cacheInfoDaoProvider.get());
  }

  public static SeriesRepositoryImpl_Factory create(Provider<SeriesApi> seriesApiProvider,
      Provider<SeriesDao> seriesDaoProvider, Provider<CategoryDao> categoryDaoProvider,
      Provider<VideoDao> videoDaoProvider, Provider<PlayProgressDao> playProgressDaoProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider) {
    return new SeriesRepositoryImpl_Factory(seriesApiProvider, seriesDaoProvider, categoryDaoProvider, videoDaoProvider, playProgressDaoProvider, cacheInfoDaoProvider);
  }

  public static SeriesRepositoryImpl newInstance(SeriesApi seriesApi, SeriesDao seriesDao,
      CategoryDao categoryDao, VideoDao videoDao, PlayProgressDao playProgressDao,
      CacheInfoDao cacheInfoDao) {
    return new SeriesRepositoryImpl(seriesApi, seriesDao, categoryDao, videoDao, playProgressDao, cacheInfoDao);
  }
}
