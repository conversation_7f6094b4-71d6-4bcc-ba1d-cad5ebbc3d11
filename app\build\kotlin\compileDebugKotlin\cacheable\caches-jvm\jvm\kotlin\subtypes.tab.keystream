android.app.Application$androidx.work.Configuration.Provider#androidx.activity.ComponentActivityandroidx.room.RoomDatabasekotlin.Enumokhttp3.Interceptor3com.shuimu.course.domain.repository.CacheRepository5com.shuimu.course.domain.repository.PaymentRepository:com.shuimu.course.domain.repository.PlayProgressRepository6com.shuimu.course.domain.repository.PlaylistRepository4com.shuimu.course.domain.repository.SearchRepository4com.shuimu.course.domain.repository.SeriesRepository3com.shuimu.course.domain.repository.ShareRepository<com.shuimu.course.domain.repository.UserPreferenceRepository2com.shuimu.course.domain.repository.UserRepository3com.shuimu.course.domain.repository.VideoRepositoryandroidx.work.CoroutineWorker7com.shuimu.course.domain.usecase.cache.VideoClickAction&com.shuimu.course.domain.util.Resource+androidx.media3.session.MediaSessionServiceandroidx.lifecycle.ViewModel?com.shuimu.course.presentation.viewmodel.profile.ProfileUiState                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                