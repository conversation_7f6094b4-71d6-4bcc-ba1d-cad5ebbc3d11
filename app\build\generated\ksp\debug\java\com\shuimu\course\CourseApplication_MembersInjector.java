package com.shuimu.course;

import androidx.hilt.work.HiltWorkerFactory;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class CourseApplication_MembersInjector implements MembersInjector<CourseApplication> {
  private final Provider<HiltWorkerFactory> workerFactoryProvider;

  public CourseApplication_MembersInjector(Provider<HiltWorkerFactory> workerFactoryProvider) {
    this.workerFactoryProvider = workerFactoryProvider;
  }

  public static MembersInjector<CourseApplication> create(
      Provider<HiltWorkerFactory> workerFactoryProvider) {
    return new CourseApplication_MembersInjector(workerFactoryProvider);
  }

  @Override
  public void injectMembers(CourseApplication instance) {
    injectWorkerFactory(instance, workerFactoryProvider.get());
  }

  @InjectedFieldSignature("com.shuimu.course.CourseApplication.workerFactory")
  public static void injectWorkerFactory(CourseApplication instance,
      HiltWorkerFactory workerFactory) {
    instance.workerFactory = workerFactory;
  }
}
