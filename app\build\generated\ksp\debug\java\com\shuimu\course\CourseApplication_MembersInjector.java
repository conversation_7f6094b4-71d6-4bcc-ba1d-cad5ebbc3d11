package com.shuimu.course;

import androidx.hilt.work.HiltWorkerFactory;
import com.shuimu.course.data.manager.AppConfigManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class CourseApplication_MembersInjector implements MembersInjector<CourseApplication> {
  private final Provider<HiltWorkerFactory> workerFactoryProvider;

  private final Provider<AppConfigManager> appConfigManagerProvider;

  public CourseApplication_MembersInjector(Provider<HiltWorkerFactory> workerFactoryProvider,
      Provider<AppConfigManager> appConfigManagerProvider) {
    this.workerFactoryProvider = workerFactoryProvider;
    this.appConfigManagerProvider = appConfigManagerProvider;
  }

  public static MembersInjector<CourseApplication> create(
      Provider<HiltWorkerFactory> workerFactoryProvider,
      Provider<AppConfigManager> appConfigManagerProvider) {
    return new CourseApplication_MembersInjector(workerFactoryProvider, appConfigManagerProvider);
  }

  @Override
  public void injectMembers(CourseApplication instance) {
    injectWorkerFactory(instance, workerFactoryProvider.get());
    injectAppConfigManager(instance, appConfigManagerProvider.get());
  }

  @InjectedFieldSignature("com.shuimu.course.CourseApplication.workerFactory")
  public static void injectWorkerFactory(CourseApplication instance,
      HiltWorkerFactory workerFactory) {
    instance.workerFactory = workerFactory;
  }

  @InjectedFieldSignature("com.shuimu.course.CourseApplication.appConfigManager")
  public static void injectAppConfigManager(CourseApplication instance,
      AppConfigManager appConfigManager) {
    instance.appConfigManager = appConfigManager;
  }
}
