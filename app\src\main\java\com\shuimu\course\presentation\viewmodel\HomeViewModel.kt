package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.repository.UserPreferenceRepository
import com.shuimu.course.domain.util.Resource
import com.shuimu.course.domain.usecase.cache.StartDownloadUseCase
import com.shuimu.course.data.workers.DownloadManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject
import com.shuimu.course.domain.model.Category
import com.shuimu.course.domain.model.Video

data class HomeState(
    val isLoading: Boolean = false,
    val series: List<Series> = emptyList(),
    val cacheStatus: Map<String, CachedVideo> = emptyMap(),
    val expandedStates: Map<String, Boolean> = emptyMap(),
    val error: String? = null,
    val downloadMessage: String? = null
)

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val seriesRepository: SeriesRepository,
    private val cacheRepository: CacheRepository,
    private val userPreferenceRepository: UserPreferenceRepository,
    private val startDownloadUseCase: StartDownloadUseCase,
    private val downloadManager: DownloadManager
) : ViewModel() {

    private val _state = MutableStateFlow(HomeState())
    val state = _state.asStateFlow()

    init {
        getSeries()
        observeCacheStatus()
        observeExpandedStates()
        
        // 清理启动时的孤儿下载状态
        viewModelScope.launch {
            try {
                val result = cacheRepository.cleanupOrphanDownloads()
                if (result.isSuccess) {
                    val resetCount = result.getOrNull() ?: 0
                    if (resetCount > 0) {
                        android.util.Log.d("HomeViewModel", "已清理${resetCount}个孤儿下载状态")
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("HomeViewModel", "清理孤儿下载状态失败", e)
            }
        }
    }

    fun getSeries() {
        seriesRepository.getSeries().onEach { result ->
            when (result) {
                is Resource.Loading -> {
                    // 如果已有缓存就保留旧 error；否则清空，避免首轮黑屏
                    val keepError = _state.value.series.isNotEmpty()
                    _state.value = _state.value.copy(
                        isLoading = true,
                        error = if (keepError) _state.value.error else null,
                        series = result.data?.let { sortSeries(it) } ?: _state.value.series
                    )
                }
                is Resource.Success -> {
                    _state.value = _state.value.copy(
                        isLoading = false,
                        error = null,
                        series = result.data?.let { sortSeries(it) } ?: emptyList()
                    )
                }
                is Resource.Error -> {
                    _state.value = _state.value.copy(
                        isLoading = false,
                        error = result.message,
                        series = result.data?.let { sortSeries(it) } ?: emptyList()
                    )
                }
            }
        }.launchIn(viewModelScope)
    }

    private fun observeCacheStatus() {
        cacheRepository.getAllCacheInfo().onEach { cacheList ->
            android.util.Log.d("HomeViewModel", "缓存状态更新: ${cacheList.size}个缓存项")
            cacheList.forEach { cache ->
                android.util.Log.d("HomeViewModel", "缓存项: ${cache.videoId} - ${cache.status} - ${cache.localPath}")
            }

            val cacheMap = cacheList.associateBy { it.videoId }
            _state.value = _state.value.copy(cacheStatus = cacheMap)
        }.launchIn(viewModelScope)
    }

    private fun observeExpandedStates() {
        userPreferenceRepository.getAllExpandedStatesFlow().onEach { expandedStates ->
            _state.value = _state.value.copy(expandedStates = expandedStates)
        }.launchIn(viewModelScope)
    }

    private fun sortSeries(series: List<Series>): List<Series> {
        return series.sortedWith { a, b ->
            when {
                // Free series first
                a.isFree && !b.isFree -> -1
                !a.isFree && b.isFree -> 1
                // Complete package last
                a.isPackage && !b.isPackage -> 1
                !a.isPackage && b.isPackage -> -1
                // Otherwise maintain original order
                else -> 0
            }
        }
    }

    fun getExpandedState(id: String, defaultExpanded: Boolean): Boolean {
        return _state.value.expandedStates[id] ?: defaultExpanded
    }

    fun setExpandedState(id: String, isExpanded: Boolean) {
        viewModelScope.launch {
            userPreferenceRepository.setExpandedState(id, isExpanded)
        }
    }

    /**
     * 开始下载视频，返回详细的状态反馈
     */
    fun downloadVideo(videoId: String, cloudUrl: String, videoTitle: String = ""): Flow<String> {
        return flow {
            try {
                android.util.Log.d("HomeViewModel", "开始下载视频: $videoId")
                
                startDownloadUseCase(videoId).collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            _state.value = _state.value.copy(downloadMessage = "正在处理...")
                            emit("正在处理...")
                        }
                        is Resource.Success -> {
                            val message = resource.data ?: "下载已启动"
                            _state.value = _state.value.copy(downloadMessage = message)
                            emit(message)
                            android.util.Log.d("HomeViewModel", "下载启动成功: $message")
                        }
                        is Resource.Error -> {
                            val errorMessage = resource.message ?: "下载失败"
                            _state.value = _state.value.copy(downloadMessage = errorMessage)
                            emit(errorMessage)
                            android.util.Log.e("HomeViewModel", "下载启动失败: $errorMessage")
                        }
                    }
                }
            } catch (e: Exception) {
                val errorMessage = "启动下载失败：${e.message}"
                _state.value = _state.value.copy(downloadMessage = errorMessage)
                emit(errorMessage)
                android.util.Log.e("HomeViewModel", "下载异常", e)
            }
        }
    }
    
    /**
     * 启动视频下载（改进版，使用回调避免协程作用域问题）
     */
    fun startVideoDownload(
        videoId: String, 
        cloudUrl: String, 
        videoTitle: String = "",
        onResult: (String) -> Unit
    ) {
        android.util.Log.d("HomeViewModel", "=== startVideoDownload调用开始 ===")
        android.util.Log.d("HomeViewModel", "参数: videoId=$videoId, cloudUrl=$cloudUrl, videoTitle=$videoTitle")
        
        viewModelScope.launch {
            try {
                android.util.Log.d("HomeViewModel", "开始执行startDownloadUseCase...")
                
                startDownloadUseCase(videoId).collect { resource ->
                    android.util.Log.d("HomeViewModel", "收到UseCase结果: ${resource.javaClass.simpleName}")
                    when (resource) {
                        is Resource.Loading -> {
                            android.util.Log.d("HomeViewModel", "Resource.Loading收到")
                        }
                        is Resource.Success -> {
                            val message = resource.data ?: "下载已启动"
                            android.util.Log.d("HomeViewModel", "Resource.Success收到: $message")
                            onResult(message)

                            // 启动成功后，监控WorkManager状态以检测可能的失败
                            // 延迟一下再开始监控，避免重复提示
                            monitorDownloadStatus(videoId) { statusMessage ->
                                // 只有在状态发生重要变化时才显示消息
                                if (statusMessage.contains("失败") || statusMessage.contains("完成") || statusMessage.contains("错误")) {
                                    onResult(statusMessage)
                                }
                            }
                        }
                        is Resource.Error -> {
                            val errorMessage = resource.message ?: "下载失败"
                            android.util.Log.e("HomeViewModel", "Resource.Error收到: $errorMessage")
                            onResult("下载启动失败: $errorMessage")
                        }
                    }
                }
                
                android.util.Log.d("HomeViewModel", "startDownloadUseCase.collect执行完成")
            } catch (e: Exception) {
                val errorMessage = "启动下载失败：${e.message}"
                android.util.Log.e("HomeViewModel", "协程异常捕获", e)
                onResult(errorMessage)
            }
        }
        
        android.util.Log.d("HomeViewModel", "=== startVideoDownload调用结束 ===")
    }
    
    /**
     * 监控下载状态，检测WorkManager任务是否立即失败
     */
    private fun monitorDownloadStatus(videoId: String, onStatusUpdate: (String) -> Unit) {
        viewModelScope.launch {
            try {
                // 延迟一下再检查，给WorkManager时间启动任务
                delay(2000)
                
                var hasStartedRunning = false
                var enqueuedTime = System.currentTimeMillis()
                
                downloadManager.getDownloadProgress(videoId).collect { workInfo ->
                    android.util.Log.d("HomeViewModel", "WorkInfo状态监控: ${workInfo?.state}")
                    
                    when (workInfo?.state) {
                        androidx.work.WorkInfo.State.FAILED -> {
                            android.util.Log.e("HomeViewModel", "WorkManager任务失败")
                            onStatusUpdate("下载失败：网络连接异常或服务器错误")

                            // 更新缓存状态为失败
                            cacheRepository.updateDownloadStatus(
                                videoId,
                                com.shuimu.course.domain.model.CacheStatus.FAILED,
                                "WorkManager任务失败"
                            )
                            return@collect // 停止监控
                        }
                        androidx.work.WorkInfo.State.CANCELLED -> {
                            android.util.Log.w("HomeViewModel", "WorkManager任务被取消")
                            onStatusUpdate("下载任务被取消")
                        }
                        androidx.work.WorkInfo.State.RUNNING -> {
                            android.util.Log.d("HomeViewModel", "WorkManager任务正在运行")
                            hasStartedRunning = true
                            
                            // 检查Worker返回的进度数据
                            val progress = workInfo.progress.getInt("progress", 0)
                            val progressVideoId = workInfo.progress.getString("videoId")
                            
                            if (progressVideoId == videoId && progress > 0) {
                                android.util.Log.d("HomeViewModel", "下载进度更新: $progress%")
                                onStatusUpdate("正在下载: $progress%")
                            } else {
                                onStatusUpdate("正在下载...")
                            }
                            
                            // 更新状态为正在下载
                            cacheRepository.updateDownloadStatus(
                                videoId, 
                                com.shuimu.course.domain.model.CacheStatus.DOWNLOADING
                            )
                        }
                        androidx.work.WorkInfo.State.SUCCEEDED -> {
                            android.util.Log.d("HomeViewModel", "WorkManager任务成功完成")
                            onStatusUpdate("下载完成！")
                            // 任务成功，停止监控
                            return@collect
                        }
                        androidx.work.WorkInfo.State.ENQUEUED -> {
                            android.util.Log.d("HomeViewModel", "WorkManager任务已入队")
                            val currentTime = System.currentTimeMillis()
                            
                            // 如果任务排队超过5秒且从未运行过，可能是约束不满足
                            if (currentTime - enqueuedTime > 5000 && !hasStartedRunning) {
                                android.util.Log.w("HomeViewModel", "任务长时间排队，可能网络约束不满足")
                                onStatusUpdate("等待合适网络环境开始下载...")
                                
                                // 更新状态为等待网络
                                cacheRepository.updateDownloadStatus(
                                    videoId, 
                                    com.shuimu.course.domain.model.CacheStatus.WAITING_NETWORK,
                                    "等待网络条件满足"
                                )
                            }
                        }
                        else -> {
                            android.util.Log.d("HomeViewModel", "WorkManager任务状态: ${workInfo?.state}")
                        }
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("HomeViewModel", "监控下载状态异常", e)
            }
        }
    }
    
    /**
     * 清除下载消息
     */
    fun clearDownloadMessage() {
        _state.value = _state.value.copy(downloadMessage = null)
    }
} 