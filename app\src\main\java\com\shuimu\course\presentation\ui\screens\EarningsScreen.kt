package com.shuimu.course.presentation.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.components.base.ShuimuButton
import com.shuimu.course.presentation.ui.components.display.EarningsCard
import com.shuimu.course.presentation.ui.components.state.ErrorMessage
import com.shuimu.course.presentation.ui.components.state.LoadingIndicator
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.EarningsViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EarningsScreen(
    navController: androidx.navigation.NavController? = null,
    viewModel: EarningsViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()
    
    Scaffold(
        topBar = { TopAppBar(title = { Text("分享收益") }) }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            state.earnings?.let { earnings ->
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    item {
                        EarningsCard(
                            earnings = earnings,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                        Spacer(Modifier.height(8.dp))
                        ShuimuButton(onClick = { /*TODO*/ }, text = "立即提现")
                    }
                    
                    item {
                        // Placeholder for earnings chart
                        Spacer(Modifier.height(16.dp))
                        Text("收益明细", style = MaterialTheme.typography.titleLarge)
                    }

                    // Placeholder for earnings history list
                    items(5) {
                        Text("收益记录 +¥89.70", Modifier.padding(8.dp))
                    }
                }
            }

            if (state.isLoading) {
                LoadingIndicator()
            }

            state.error?.let { error ->
                ErrorMessage(message = error, onRetry = { viewModel.loadEarnings() })
            }
        }
    }
}

@Preview
@Composable
fun EarningsScreenPreview() {
    ShuimuCourseTheme {
        EarningsScreen()
    }
} 