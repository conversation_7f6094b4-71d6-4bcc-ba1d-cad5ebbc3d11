import uuid
import random
from fastapi import APIRouter
from ..models.payment import PaymentCreate, PaymentResponse

router = APIRouter()

@router.post("/payments/create", response_model=PaymentResponse)
def create_payment(payment_details: PaymentCreate):
    """
    Simulates creating a payment order.
    Randomly returns a success or failure message.
    """
    print(f"Received payment request: {payment_details.dict()}")
    
    # Simulate a 90% success rate
    if random.random() < 0.9:
        return PaymentResponse(
            orderId=str(uuid.uuid4()),
            status="completed",
            message="Payment successful!"
        )
    else:
        return PaymentResponse(
            orderId=str(uuid.uuid4()),
            status="failed",
            message="Payment failed. Please try again."
        )
