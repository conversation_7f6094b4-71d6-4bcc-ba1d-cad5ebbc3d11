# AI环境诊断脚本
# 自动检测WSL、Claude配置状态并提供修复建议

Write-Host "🔍 AI环境全面诊断开始..." -ForegroundColor Magenta

$issues = @()
$warnings = @()
$successes = @()

# 1. 检测PowerShell版本
Write-Host "`n📋 检测PowerShell环境..." -ForegroundColor Cyan
$psVersion = $PSVersionTable.PSVersion
Write-Host "PowerShell版本: $psVersion" -ForegroundColor Yellow

if ($psVersion.Major -ge 5) {
    $successes += "PowerShell版本符合要求 ($psVersion)"
} else {
    $issues += "PowerShell版本过低，建议升级到5.0以上"
}

# 2. 检测WSL
Write-Host "`n🐧 检测WSL环境..." -ForegroundColor Cyan
try {
    $wslVersion = & wsl --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        $successes += "WSL已正确安装"
        Write-Host "✅ WSL已安装" -ForegroundColor Green
        
        # 检测WSL发行版
        $distributions = & wsl -l -v 2>$null
        Write-Host "已安装的WSL发行版:" -ForegroundColor Yellow
        Write-Host $distributions -ForegroundColor White
    } else {
        $issues += "WSL未安装或配置错误"
    }
} catch {
    $issues += "WSL检测失败: $_"
}

# 3. 检测Claude CLI
Write-Host "`n🤖 检测Claude CLI..." -ForegroundColor Cyan
try {
    $claudeVersion = & wsl claude --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        $successes += "Claude CLI在WSL中正常工作"
        Write-Host "✅ Claude CLI可用" -ForegroundColor Green
        Write-Host "版本信息: $claudeVersion" -ForegroundColor White
    } else {
        $issues += "Claude CLI在WSL中不可用，请检查安装"
    }
} catch {
    $issues += "Claude CLI检测失败: $_"
}

# 4. 检测PATH配置
Write-Host "`n🛤️ 检测PATH配置..." -ForegroundColor Cyan
$userPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$systemPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")

if ($userPath -like "*D:\Tools*") {
    $successes += "D:\Tools已在用户PATH中"
    Write-Host "✅ D:\Tools在用户PATH中" -ForegroundColor Green
} elseif ($systemPath -like "*D:\Tools*") {
    $warnings += "D:\Tools在系统PATH中（建议移到用户PATH）"
    Write-Host "⚠️ D:\Tools在系统PATH中" -ForegroundColor Yellow
} else {
    $issues += "D:\Tools不在PATH中"
}

# 5. 检测文件结构
Write-Host "`n📁 检测工具文件..." -ForegroundColor Cyan
$requiredFiles = @("claude.cmd", "clean_path.ps1", "cloudflared.exe", "AI-Tools.psm1")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    if (Test-Path "D:\Tools\$file") {
        Write-Host "✅ $file 存在" -ForegroundColor Green
    } else {
        $missingFiles += $file
        Write-Host "❌ $file 缺失" -ForegroundColor Red
    }
}

if ($missingFiles.Count -eq 0) {
    $successes += "所有必需文件都存在"
} else {
    $issues += "缺失文件: $($missingFiles -join ', ')"
}

# 6. 检测PowerShell模块
Write-Host "`n📦 检测PowerShell模块..." -ForegroundColor Cyan
if (Test-Path "D:\Tools\AI-Tools.psm1") {
    try {
        Import-Module "D:\Tools\AI-Tools.psm1" -Force
        $successes += "AI-Tools模块可以正常加载"
        Write-Host "✅ AI-Tools模块加载成功" -ForegroundColor Green
        
        # 测试关键函数
        if (Get-Command Invoke-Claude -ErrorAction SilentlyContinue) {
            Write-Host "✅ claude别名可用" -ForegroundColor Green
        } else {
            $warnings += "claude别名未正确设置"
        }
    } catch {
        $issues += "AI-Tools模块加载失败: $_"
    }
} else {
    $issues += "AI-Tools.psm1文件不存在"
}

# 7. 检测网络连接（Cloudflare）
Write-Host "`n🌐 检测网络连接..." -ForegroundColor Cyan
try {
    $cloudflareTest = Test-NetConnection -ComputerName "*******" -Port 443 -InformationLevel Quiet
    if ($cloudflareTest) {
        $successes += "网络连接正常"
        Write-Host "✅ 网络连接正常" -ForegroundColor Green
    } else {
        $warnings += "网络连接可能存在问题"
    }
} catch {
    $warnings += "网络连接检测失败"
}

# 生成诊断报告
Write-Host "`n" + "="*60 -ForegroundColor Magenta
Write-Host "📊 诊断报告" -ForegroundColor Magenta
Write-Host "="*60 -ForegroundColor Magenta

Write-Host "`n✅ 成功项目 ($($successes.Count)):" -ForegroundColor Green
foreach ($success in $successes) {
    Write-Host "  ✓ $success" -ForegroundColor Green
}

if ($warnings.Count -gt 0) {
    Write-Host "`n⚠️ 警告项目 ($($warnings.Count)):" -ForegroundColor Yellow
    foreach ($warning in $warnings) {
        Write-Host "  ⚠ $warning" -ForegroundColor Yellow
    }
}

if ($issues.Count -gt 0) {
    Write-Host "`n❌ 问题项目 ($($issues.Count)):" -ForegroundColor Red
    foreach ($issue in $issues) {
        Write-Host "  ✗ $issue" -ForegroundColor Red
    }
    
    Write-Host "`n🔧 修复建议:" -ForegroundColor Cyan
    if ($issues -like "*WSL*") {
        Write-Host "  • 安装WSL: wsl --install" -ForegroundColor White
    }
    if ($issues -like "*Claude CLI*") {
        Write-Host "  • 在WSL中安装Claude CLI" -ForegroundColor White
    }
    if ($issues -like "*PATH*") {
        Write-Host "  • 运行: D:\Tools\clean_path.ps1" -ForegroundColor White
    }
    if ($missingFiles.Count -gt 0) {
        Write-Host "  • 重新创建缺失的文件" -ForegroundColor White
    }
} else {
    Write-Host "`n🎉 恭喜！所有检测都通过了！" -ForegroundColor Green
    Write-Host "你可以开始使用AI工具了：" -ForegroundColor Cyan
    Write-Host "  • claude -p '你的问题'      # 直接提问" -ForegroundColor White
    Write-Host "  • ai-env                   # 环境检测" -ForegroundColor White
    Write-Host "  • ai-pipe 'cmd1' 'cmd2'   # 管道处理" -ForegroundColor White
}

Write-Host "`n" + "="*60 -ForegroundColor Magenta 