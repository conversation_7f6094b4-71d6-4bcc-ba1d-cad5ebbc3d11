package com.shuimu.course.di;

import com.shuimu.course.data.remote.api.PlaylistApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvidePlaylistApiFactory implements Factory<PlaylistApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvidePlaylistApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public PlaylistApi get() {
    return providePlaylistApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvidePlaylistApiFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvidePlaylistApiFactory(retrofitProvider);
  }

  public static PlaylistApi providePlaylistApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.providePlaylistApi(retrofit));
  }
}
