package com.shuimu.course.presentation.viewmodel.profile;

import com.shuimu.course.domain.usecase.profile.GetProfileDataUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class ProfileViewModel_Factory implements Factory<ProfileViewModel> {
  private final Provider<GetProfileDataUseCase> getProfileDataUseCaseProvider;

  public ProfileViewModel_Factory(Provider<GetProfileDataUseCase> getProfileDataUseCaseProvider) {
    this.getProfileDataUseCaseProvider = getProfileDataUseCaseProvider;
  }

  @Override
  public ProfileViewModel get() {
    return newInstance(getProfileDataUseCaseProvider.get());
  }

  public static ProfileViewModel_Factory create(
      Provider<GetProfileDataUseCase> getProfileDataUseCaseProvider) {
    return new ProfileViewModel_Factory(getProfileDataUseCaseProvider);
  }

  public static ProfileViewModel newInstance(GetProfileDataUseCase getProfileDataUseCase) {
    return new ProfileViewModel(getProfileDataUseCase);
  }
}
