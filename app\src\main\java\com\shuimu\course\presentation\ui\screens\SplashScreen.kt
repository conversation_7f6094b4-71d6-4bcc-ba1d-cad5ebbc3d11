package com.shuimu.course.presentation.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.SplashViewModel
import kotlinx.coroutines.delay

/**
 * 启动页面 - 仿美团模式
 * 显示品牌信息的同时后台预加载数据
 */
@Composable
fun SplashScreen(
    onNavigateToHome: () -> Unit,
    viewModel: SplashViewModel = hiltViewModel()
) {
    val splashState by viewModel.state.collectAsState()
    
    // 监听数据加载状态
    LaunchedEffect(splashState) {
        when {
            splashState.isDataReady -> {
                // 数据准备完成，导航到首页
                android.util.Log.d("SplashScreen", "数据准备完成，导航到首页")
                delay(500) // 稍微延迟，让用户看到完整的启动页
                onNavigateToHome()
            }
            splashState.shouldUseLocalData -> {
                // 使用本地数据，导航到首页
                android.util.Log.d("SplashScreen", "使用本地数据，导航到首页")
                delay(500)
                onNavigateToHome()
            }
            splashState.shouldUsePresetData -> {
                // 使用预置数据，导航到首页
                android.util.Log.d("SplashScreen", "使用预置数据，导航到首页")
                delay(500)
                onNavigateToHome()
            }
        }
    }
    
    // 启动数据预加载
    LaunchedEffect(Unit) {
        android.util.Log.d("SplashScreen", "开始数据预加载")
        viewModel.preloadData()
    }
    
    SplashContent()
}

@Composable
private fun SplashContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // 中部偏上：Logo + Slogan
        Column(
            modifier = Modifier
                .align(Alignment.Center)
                .offset(y = (-80).dp), // 向上偏移
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 水幕Logo（临时使用文字替代）
            Text(
                text = "水幕",
                fontSize = 48.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2196F3),
                modifier = Modifier.padding(16.dp)
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Slogan
            Text(
                text = "从此让情感自由不再难",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF333333),
                textAlign = TextAlign.Center
            )
        }
        
        // 底部：服务提供商信息
        Text(
            text = "水幕情感提供服务",
            fontSize = 14.sp,
            color = Color(0xFF999999),
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 48.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun SplashScreenPreview() {
    ShuimuCourseTheme {
        SplashContent()
    }
}
