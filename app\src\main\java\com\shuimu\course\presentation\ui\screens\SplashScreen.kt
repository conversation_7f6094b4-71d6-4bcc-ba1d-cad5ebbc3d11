package com.shuimu.course.presentation.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.SplashViewModel
import com.shuimu.course.presentation.ui.components.base.ShuimuLogo
import kotlinx.coroutines.delay

/**
 * 启动页面 - 仿美团模式
 * 显示品牌信息的同时后台预加载数据
 */
@Composable
fun SplashScreen(
    onNavigateToHome: () -> Unit,
    viewModel: SplashViewModel = hiltViewModel()
) {
    val splashState by viewModel.state.collectAsState()
    
    // 🔥 修复：数据准备完成立即导航，无延迟
    LaunchedEffect(splashState) {
        when {
            splashState.isDataReady -> {
                // 数据准备完成，立即导航
                android.util.Log.d("SplashScreen", "✅ 服务器数据准备完成，立即导航")
                onNavigateToHome() // 🔥 移除200ms延迟，立即导航
            }
            splashState.shouldUseLocalData -> {
                // 使用本地数据，立即导航
                android.util.Log.d("SplashScreen", "✅ 本地数据准备完成，立即导航")
                onNavigateToHome() // 🔥 移除延迟
            }
            splashState.shouldUsePresetData -> {
                // 使用预置数据，立即导航
                android.util.Log.d("SplashScreen", "✅ 预置数据准备完成，立即导航")
                onNavigateToHome() // 🔥 移除延迟
            }
        }
    }
    
    // 启动数据预加载
    LaunchedEffect(Unit) {
        android.util.Log.d("SplashScreen", "开始数据预加载")
        viewModel.preloadData()
    }
    
    SplashContent()
}

@Composable
private fun SplashContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // 中部偏上：Logo + Slogan
        Column(
            modifier = Modifier
                .align(Alignment.Center)
                .offset(y = (-80).dp), // 向上偏移
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 🔥 启动页专用Logo：蓝色圆底 + 白色"水幕"文字（静态专业）
            ShuimuLogo(
                size = 120.dp, // 启动页使用更大的Logo
                animated = false, // 🔥 静态Logo，专业品牌形象
                useTextMode = true // 🔥 使用文字模式：蓝色圆底 + 白色"水幕"文字
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Slogan
            Text(
                text = "从此让情感自由不再难",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF333333),
                textAlign = TextAlign.Center
            )
        }
        
        // 底部：服务提供商信息
        Text(
            text = "水幕情感提供服务",
            fontSize = 14.sp,
            color = Color(0xFF999999),
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 48.dp)
        )
    }
}

@Preview(
    name = "启动页预览",
    showBackground = true,
    widthDp = 412,
    heightDp = 892,
    device = "spec:width=412dp,height=892dp"
)
@Composable
fun SplashScreenPreview() {
    ShuimuCourseTheme {
        SplashContent()
    }
}
