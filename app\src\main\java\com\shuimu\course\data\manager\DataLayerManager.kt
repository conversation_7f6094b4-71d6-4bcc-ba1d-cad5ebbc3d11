package com.shuimu.course.data.manager

import android.content.Context
import com.shuimu.course.data.local.dao.CacheInfoDao
import com.shuimu.course.data.local.dao.PlayProgressDao
import com.shuimu.course.data.remote.api.VideoApi
import com.shuimu.course.data.remote.api.PaymentApi
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.repository.CacheConfig
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据分层管理器 - 实现智能数据合并策略
 * 
 * 数据分类：
 * 1. 本地优先：缓存状态、播放进度、用户偏好
 * 2. 服务器优先：购买状态、内容信息、权限控制
 * 3. 智能合并：观看次数、最后观看时间等
 */
@Singleton
class DataLayerManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val cacheInfoDao: CacheInfoDao,
    private val playProgressDao: PlayProgressDao,
    private val videoApi: VideoApi,
    private val paymentApi: PaymentApi,
    private val dataSyncManager: DataSyncManager
) {
    
    /**
     * 本地优先的数据状态
     */
    data class LocalVideoState(
        val cacheStatus: com.shuimu.course.domain.model.CacheStatus,
        val localPath: String?,
        val progress: Float,
        val watchCount: Int,
        val lastWatchTime: Long
    )
    
    /**
     * 服务器优先的数据信息
     */
    data class ServerVideoInfo(
        val isPurchasable: Boolean,
        val price: Float?,
        val title: String,
        val description: String,
        val cloudUrl: String,
        val duration: Int,
        val totalPlayCount: Long?
    )
    
    /**
     * 获取本地优先的视频状态
     */
    fun getLocalVideoState(videoId: String): Flow<LocalVideoState?> {
        return combine(
            cacheInfoDao.getCacheInfoByVideoId(videoId),
            playProgressDao.getProgressByVideoId(videoId)
        ) { cacheInfo, progress ->
            if (cacheInfo != null || progress != null) {
                LocalVideoState(
                    cacheStatus = cacheInfo?.status?.let { status ->
                        when (status) {
                            com.shuimu.course.data.local.entities.CacheStatus.NOT_CACHED -> 
                                com.shuimu.course.domain.model.CacheStatus.NOT_CACHED
                            com.shuimu.course.data.local.entities.CacheStatus.WAITING_NETWORK -> 
                                com.shuimu.course.domain.model.CacheStatus.WAITING_NETWORK
                            com.shuimu.course.data.local.entities.CacheStatus.PENDING -> 
                                com.shuimu.course.domain.model.CacheStatus.PENDING
                            com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADING -> 
                                com.shuimu.course.domain.model.CacheStatus.DOWNLOADING
                            com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADED -> 
                                com.shuimu.course.domain.model.CacheStatus.DOWNLOADED
                            com.shuimu.course.data.local.entities.CacheStatus.FAILED -> 
                                com.shuimu.course.domain.model.CacheStatus.FAILED
                            com.shuimu.course.data.local.entities.CacheStatus.PAUSED -> 
                                com.shuimu.course.domain.model.CacheStatus.PAUSED
                        }
                    } ?: com.shuimu.course.domain.model.CacheStatus.NOT_CACHED,
                    localPath = cacheInfo?.localPath,
                    progress = progress?.let { p ->
                        if (p.totalDurationSeconds > 0) {
                            p.lastPositionSeconds.toFloat() / p.totalDurationSeconds.toFloat()
                        } else 0f
                    } ?: 0f,
                    watchCount = progress?.watchCount ?: 0,
                    lastWatchTime = progress?.lastWatchTime ?: 0L
                )
            } else {
                null
            }
        }
    }
    
    /**
     * 智能合并视频数据
     * 本地优先：缓存状态、播放进度、用户偏好
     * 服务器优先：购买状态、内容信息、权限控制
     */
    fun mergeVideoData(
        serverVideo: Video,
        localState: LocalVideoState?
    ): Video {
        return serverVideo.copy(
            // 本地优先的字段
            cacheStatus = localState?.cacheStatus ?: serverVideo.cacheStatus,
            localPath = localState?.localPath ?: serverVideo.localPath,
            progress = localState?.progress ?: serverVideo.progress,
            
            // 智能合并的字段
            watchCount = if (localState != null) {
                maxOf(localState.watchCount, serverVideo.watchCount ?: 0)
            } else {
                serverVideo.watchCount
            },
            
            // 服务器优先的字段保持不变
            // isPurchasable, title, description, cloudUrl, duration 等
        )
    }
    
    /**
     * 立即同步关键数据（播放完成、购买状态等）
     */
    suspend fun syncCriticalData(
        videoId: String,
        progress: Float,
        completed: Boolean
    ): Result<Unit> {
        return try {
            android.util.Log.d("DataLayerManager", "立即同步关键数据: $videoId, progress: $progress, completed: $completed")
            
            // 1. 立即更新本地状态
            updateLocalProgress(videoId, progress, completed)
            
            // 2. 立即同步到服务器
            // val response = videoApi.updateProgress(videoId, progress, completed)
            // 暂时模拟成功
            
            android.util.Log.d("DataLayerManager", "关键数据同步成功")
            Result.success(Unit)
            
        } catch (e: Exception) {
            android.util.Log.e("DataLayerManager", "关键数据同步失败，加入重试队列", e)
            
            // 同步失败，加入队列稍后重试
            dataSyncManager.queueWatchHistory(videoId, 0L, progress, completed)
            
            Result.failure(e)
        }
    }
    
    /**
     * 队列同步非关键数据（缓存状态等）
     */
    suspend fun queueNonCriticalData(
        videoId: String,
        operation: String,
        localPath: String? = null
    ) {
        android.util.Log.d("DataLayerManager", "非关键数据加入队列: $videoId - $operation")
        
        // 1. 立即更新本地状态
        updateLocalCacheStatus(videoId, operation, localPath)
        
        // 2. 加入同步队列（不立即同步）
        dataSyncManager.queueCacheOperation(videoId, operation, localPath)
    }
    
    /**
     * 网络状态感知的智能同步
     */
    suspend fun smartSync(
        data: Any,
        priority: SyncPriority
    ) {
        val networkState = getNetworkState()
        
        when (priority) {
            SyncPriority.CRITICAL -> {
                // 关键数据立即同步，不管网络状态
                syncImmediately(data)
            }
            SyncPriority.HIGH -> {
                when (networkState) {
                    NetworkState.WIFI -> syncImmediately(data)
                    NetworkState.MOBILE -> queueForWifiSync(data)
                    NetworkState.OFFLINE -> saveForLaterSync(data)
                }
            }
            SyncPriority.LOW -> {
                // 低优先级数据只在WiFi下同步
                when (networkState) {
                    NetworkState.WIFI -> queueForSync(data)
                    else -> saveForLaterSync(data)
                }
            }
        }
    }
    
    /**
     * 冲突解决机制
     */
    fun resolveVideoConflict(local: Video, server: Video): Video {
        return when {
            // 购买状态以服务器为准
            local.isPurchasable != server.isPurchasable -> {
                android.util.Log.d("DataLayerManager", "解决购买状态冲突: ${local.isPurchasable} -> ${server.isPurchasable}")
                local.copy(isPurchasable = server.isPurchasable)
            }
            
            // 播放进度以本地为准（除非本地为0且服务器有进度）
            local.progress == 0f && server.progress > 0f -> {
                android.util.Log.d("DataLayerManager", "解决播放进度冲突: ${local.progress} -> ${server.progress}")
                local.copy(progress = server.progress)
            }
            
            // 内容信息以服务器为准
            local.title != server.title || local.description != server.description -> {
                android.util.Log.d("DataLayerManager", "解决内容信息冲突")
                local.copy(
                    title = server.title,
                    description = server.description,
                    cloudUrl = server.cloudUrl,
                    duration = server.duration
                )
            }
            
            // 其他情况保持本地状态
            else -> local
        }
    }
    
    // 私有辅助方法
    private suspend fun updateLocalProgress(videoId: String, progress: Float, completed: Boolean) {
        // 实现本地进度更新逻辑
        android.util.Log.d("DataLayerManager", "更新本地进度: $videoId, $progress, $completed")
    }
    
    private suspend fun updateLocalCacheStatus(videoId: String, operation: String, localPath: String?) {
        // 实现本地缓存状态更新逻辑
        android.util.Log.d("DataLayerManager", "更新本地缓存状态: $videoId, $operation")
    }
    
    private suspend fun syncImmediately(data: Any) {
        android.util.Log.d("DataLayerManager", "立即同步数据")
    }
    
    private suspend fun queueForWifiSync(data: Any) {
        android.util.Log.d("DataLayerManager", "加入WiFi同步队列")
    }
    
    private suspend fun queueForSync(data: Any) {
        android.util.Log.d("DataLayerManager", "加入同步队列")
    }
    
    private suspend fun saveForLaterSync(data: Any) {
        android.util.Log.d("DataLayerManager", "保存待稍后同步")
    }
    
    private fun getNetworkState(): NetworkState {
        // 实现网络状态检测
        return NetworkState.WIFI // 暂时返回WiFi状态
    }
}

// 枚举定义
enum class SyncPriority {
    CRITICAL,  // 关键数据：购买、播放完成
    HIGH,      // 高优先级：播放进度
    LOW        // 低优先级：缓存状态、偏好设置
}

enum class NetworkState {
    WIFI,      // WiFi网络
    MOBILE,    // 移动网络
    OFFLINE    // 离线状态
}
