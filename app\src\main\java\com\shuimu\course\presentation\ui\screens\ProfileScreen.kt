package com.shuimu.course.presentation.ui.screens

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.SaveAlt
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.components.navigation.ShuimuBottomNavigationBar
import com.shuimu.course.presentation.ui.components.user.MenuItem
import com.shuimu.course.presentation.ui.components.user.UserProfileHeader
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.ProfileViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    navController: androidx.navigation.NavController? = null,
    viewModel: ProfileViewModel = hiltViewModel()
) {
    val user by viewModel.userState.collectAsState()
    var showLogoutDialog by remember { mutableStateOf(false) }

    Scaffold(
        topBar = { TopAppBar(title = { Text("个人中心") }) },
        bottomBar = {
            ShuimuBottomNavigationBar(currentRoute = "profile", onNavigate = { /*TODO*/ })
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            user?.let {
                UserProfileHeader(
                    username = it.nickname,
                    nickname = it.username,
                    avatarUrl = it.avatarUrl
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            MenuItem(title = "购买记录", subtitle = "查看购买历史", icon = Icons.Default.History, iconBackgroundColor = Color.Gray, onClick = { /*TODO*/ })
            MenuItem(title = "缓存管理", subtitle = "管理本地视频", icon = Icons.Default.SaveAlt, iconBackgroundColor = Color.Gray, onClick = { /*TODO*/ })
            MenuItem(title = "设置", subtitle = "个性化配置", icon = Icons.Default.Settings, iconBackgroundColor = Color.Gray, onClick = { /*TODO*/ })
            MenuItem(title = "退出登录", subtitle = "退出当前账号", icon = Icons.Default.ExitToApp, iconBackgroundColor = Color.Red, onClick = { showLogoutDialog = true })
        }

        if (showLogoutDialog) {
            AlertDialog(
                onDismissRequest = { showLogoutDialog = false },
                title = { Text("退出登录") },
                text = { Text("您确定要退出当前账号吗？") },
                confirmButton = {
                    Button(onClick = {
                        viewModel.logout()
                        showLogoutDialog = false
                        // TODO: Navigate to login screen
                    }) {
                        Text("确定")
                    }
                },
                dismissButton = {
                    TextButton(onClick = { showLogoutDialog = false }) {
                        Text("取消")
                    }
                }
            )
        }
    }
}

@Preview
@Composable
fun ProfileScreenPreview() {
    ShuimuCourseTheme {
        ProfileScreen()
    }
} 