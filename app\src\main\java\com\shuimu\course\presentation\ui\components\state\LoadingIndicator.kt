package com.shuimu.course.presentation.ui.components.state

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun LoadingIndicator(
    modifier: Modifier = Modifier,
    isFullScreen: Boolean = true
) {
    val boxModifier = if (isFullScreen) {
        modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
    } else {
        modifier
    }

    Box(
        modifier = boxModifier,
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            modifier = Modifier.padding(16.dp),
            color = MaterialTheme.colorScheme.primary
        )
    }
}

@Preview(name = "Full Screen Loading")
@Composable
fun LoadingIndicatorFullScreenPreview() {
    ShuimuCourseTheme {
        LoadingIndicator(isFullScreen = true)
    }
}

@Preview(name = "Inline Loading")
@Composable
fun LoadingIndicatorInlinePreview() {
    ShuimuCourseTheme {
        LoadingIndicator(isFullScreen = false)
    }
} 