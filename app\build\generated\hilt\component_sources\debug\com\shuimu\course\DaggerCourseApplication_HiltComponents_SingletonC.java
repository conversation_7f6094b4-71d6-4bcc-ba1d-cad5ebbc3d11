package com.shuimu.course;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.hilt.work.HiltWorkerFactory;
import androidx.hilt.work.WorkerAssistedFactory;
import androidx.hilt.work.WorkerFactoryModule_ProvideFactoryFactory;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import androidx.work.ListenableWorker;
import androidx.work.WorkerParameters;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.shuimu.course.data.local.dao.CacheInfoDao;
import com.shuimu.course.data.local.dao.CategoryDao;
import com.shuimu.course.data.local.dao.PlayProgressDao;
import com.shuimu.course.data.local.dao.SeriesDao;
import com.shuimu.course.data.local.dao.UserDao;
import com.shuimu.course.data.local.dao.VideoDao;
import com.shuimu.course.data.local.database.AppDatabase;
import com.shuimu.course.data.manager.AppConfigManager;
import com.shuimu.course.data.manager.DataSyncManager;
import com.shuimu.course.data.remote.api.AuthApi;
import com.shuimu.course.data.remote.api.CacheApi;
import com.shuimu.course.data.remote.api.PaymentApi;
import com.shuimu.course.data.remote.api.PlaylistApi;
import com.shuimu.course.data.remote.api.SearchApi;
import com.shuimu.course.data.remote.api.SeriesApi;
import com.shuimu.course.data.remote.api.UserApi;
import com.shuimu.course.data.remote.api.VideoApi;
import com.shuimu.course.data.remote.interceptors.UserIdInterceptor;
import com.shuimu.course.data.repository.CacheRepositoryImpl;
import com.shuimu.course.data.repository.PaymentRepositoryImpl;
import com.shuimu.course.data.repository.PlayProgressRepositoryImpl;
import com.shuimu.course.data.repository.PlaylistRepositoryImpl;
import com.shuimu.course.data.repository.SearchRepositoryImpl;
import com.shuimu.course.data.repository.SeriesRepositoryImpl;
import com.shuimu.course.data.repository.ShareRepositoryImpl;
import com.shuimu.course.data.repository.UserPreferenceRepositoryImpl;
import com.shuimu.course.data.repository.UserRepositoryImpl;
import com.shuimu.course.data.repository.VideoRepositoryImpl;
import com.shuimu.course.data.workers.DownloadManager;
import com.shuimu.course.data.workers.VideoDownloadWorker;
import com.shuimu.course.data.workers.VideoDownloadWorker_AssistedFactory;
import com.shuimu.course.di.DatabaseModule_ProvideAppDatabaseFactory;
import com.shuimu.course.di.DatabaseModule_ProvideCacheInfoDaoFactory;
import com.shuimu.course.di.DatabaseModule_ProvideCategoryDaoFactory;
import com.shuimu.course.di.DatabaseModule_ProvidePlayProgressDaoFactory;
import com.shuimu.course.di.DatabaseModule_ProvideSeriesDaoFactory;
import com.shuimu.course.di.DatabaseModule_ProvideUserDaoFactory;
import com.shuimu.course.di.DatabaseModule_ProvideVideoDaoFactory;
import com.shuimu.course.di.NetworkModule_ProvideAuthApiFactory;
import com.shuimu.course.di.NetworkModule_ProvideCacheApiFactory;
import com.shuimu.course.di.NetworkModule_ProvideCustomDnsFactory;
import com.shuimu.course.di.NetworkModule_ProvideHostInterceptorFactory;
import com.shuimu.course.di.NetworkModule_ProvideHostnameVerifierFactory;
import com.shuimu.course.di.NetworkModule_ProvideLoggingInterceptorFactory;
import com.shuimu.course.di.NetworkModule_ProvideOkHttpClientFactory;
import com.shuimu.course.di.NetworkModule_ProvidePaymentApiFactory;
import com.shuimu.course.di.NetworkModule_ProvidePlaylistApiFactory;
import com.shuimu.course.di.NetworkModule_ProvideRetrofitFactory;
import com.shuimu.course.di.NetworkModule_ProvideSearchApiFactory;
import com.shuimu.course.di.NetworkModule_ProvideSeriesApiFactory;
import com.shuimu.course.di.NetworkModule_ProvideUserApiFactory;
import com.shuimu.course.di.NetworkModule_ProvideVideoApiFactory;
import com.shuimu.course.domain.usecase.cache.StartDownloadUseCase;
import com.shuimu.course.domain.usecase.cache.SyncCacheStateUseCase;
import com.shuimu.course.domain.usecase.orders.GetOrdersUseCase;
import com.shuimu.course.domain.usecase.profile.GetProfileDataUseCase;
import com.shuimu.course.presentation.viewmodel.CacheManagerViewModel;
import com.shuimu.course.presentation.viewmodel.CacheManagerViewModel_HiltModules;
import com.shuimu.course.presentation.viewmodel.EarningsViewModel;
import com.shuimu.course.presentation.viewmodel.EarningsViewModel_HiltModules;
import com.shuimu.course.presentation.viewmodel.HomeViewModel;
import com.shuimu.course.presentation.viewmodel.HomeViewModel_HiltModules;
import com.shuimu.course.presentation.viewmodel.LoginViewModel;
import com.shuimu.course.presentation.viewmodel.LoginViewModel_HiltModules;
import com.shuimu.course.presentation.viewmodel.PaymentViewModel;
import com.shuimu.course.presentation.viewmodel.PaymentViewModel_HiltModules;
import com.shuimu.course.presentation.viewmodel.ProfileViewModel;
import com.shuimu.course.presentation.viewmodel.ProfileViewModel_HiltModules;
import com.shuimu.course.presentation.viewmodel.SearchViewModel;
import com.shuimu.course.presentation.viewmodel.SearchViewModel_HiltModules;
import com.shuimu.course.presentation.viewmodel.VideoPlayerViewModel;
import com.shuimu.course.presentation.viewmodel.VideoPlayerViewModel_HiltModules;
import com.shuimu.course.presentation.viewmodel.orders.OrdersViewModel;
import com.shuimu.course.presentation.viewmodel.orders.OrdersViewModel_HiltModules;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.IdentifierNameString;
import dagger.internal.KeepFieldType;
import dagger.internal.LazyClassKeyMap;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.SingleCheck;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;
import javax.net.ssl.HostnameVerifier;
import okhttp3.Dns;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DaggerCourseApplication_HiltComponents_SingletonC {
  private DaggerCourseApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public CourseApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements CourseApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public CourseApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements CourseApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public CourseApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements CourseApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public CourseApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements CourseApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public CourseApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements CourseApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public CourseApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements CourseApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public CourseApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements CourseApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public CourseApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends CourseApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends CourseApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends CourseApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends CourseApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Map<Class<?>, Boolean> getViewModelKeys() {
      return LazyClassKeyMap.<Boolean>of(ImmutableMap.<String, Boolean>builderWithExpectedSize(10).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_CacheManagerViewModel, CacheManagerViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_EarningsViewModel, EarningsViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_HomeViewModel, HomeViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_LoginViewModel, LoginViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_orders_OrdersViewModel, OrdersViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_PaymentViewModel, PaymentViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_ProfileViewModel, ProfileViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_profile_ProfileViewModel, com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_SearchViewModel, SearchViewModel_HiltModules.KeyModule.provide()).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_VideoPlayerViewModel, VideoPlayerViewModel_HiltModules.KeyModule.provide()).build());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @IdentifierNameString
    private static final class LazyClassKeyProvider {
      static String com_shuimu_course_presentation_viewmodel_SearchViewModel = "com.shuimu.course.presentation.viewmodel.SearchViewModel";

      static String com_shuimu_course_presentation_viewmodel_ProfileViewModel = "com.shuimu.course.presentation.viewmodel.ProfileViewModel";

      static String com_shuimu_course_presentation_viewmodel_VideoPlayerViewModel = "com.shuimu.course.presentation.viewmodel.VideoPlayerViewModel";

      static String com_shuimu_course_presentation_viewmodel_CacheManagerViewModel = "com.shuimu.course.presentation.viewmodel.CacheManagerViewModel";

      static String com_shuimu_course_presentation_viewmodel_EarningsViewModel = "com.shuimu.course.presentation.viewmodel.EarningsViewModel";

      static String com_shuimu_course_presentation_viewmodel_PaymentViewModel = "com.shuimu.course.presentation.viewmodel.PaymentViewModel";

      static String com_shuimu_course_presentation_viewmodel_HomeViewModel = "com.shuimu.course.presentation.viewmodel.HomeViewModel";

      static String com_shuimu_course_presentation_viewmodel_LoginViewModel = "com.shuimu.course.presentation.viewmodel.LoginViewModel";

      static String com_shuimu_course_presentation_viewmodel_orders_OrdersViewModel = "com.shuimu.course.presentation.viewmodel.orders.OrdersViewModel";

      static String com_shuimu_course_presentation_viewmodel_profile_ProfileViewModel = "com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel";

      @KeepFieldType
      SearchViewModel com_shuimu_course_presentation_viewmodel_SearchViewModel2;

      @KeepFieldType
      ProfileViewModel com_shuimu_course_presentation_viewmodel_ProfileViewModel2;

      @KeepFieldType
      VideoPlayerViewModel com_shuimu_course_presentation_viewmodel_VideoPlayerViewModel2;

      @KeepFieldType
      CacheManagerViewModel com_shuimu_course_presentation_viewmodel_CacheManagerViewModel2;

      @KeepFieldType
      EarningsViewModel com_shuimu_course_presentation_viewmodel_EarningsViewModel2;

      @KeepFieldType
      PaymentViewModel com_shuimu_course_presentation_viewmodel_PaymentViewModel2;

      @KeepFieldType
      HomeViewModel com_shuimu_course_presentation_viewmodel_HomeViewModel2;

      @KeepFieldType
      LoginViewModel com_shuimu_course_presentation_viewmodel_LoginViewModel2;

      @KeepFieldType
      OrdersViewModel com_shuimu_course_presentation_viewmodel_orders_OrdersViewModel2;

      @KeepFieldType
      com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel com_shuimu_course_presentation_viewmodel_profile_ProfileViewModel2;
    }
  }

  private static final class ViewModelCImpl extends CourseApplication_HiltComponents.ViewModelC {
    private final SavedStateHandle savedStateHandle;

    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<CacheManagerViewModel> cacheManagerViewModelProvider;

    private Provider<EarningsViewModel> earningsViewModelProvider;

    private Provider<HomeViewModel> homeViewModelProvider;

    private Provider<LoginViewModel> loginViewModelProvider;

    private Provider<OrdersViewModel> ordersViewModelProvider;

    private Provider<PaymentViewModel> paymentViewModelProvider;

    private Provider<ProfileViewModel> profileViewModelProvider;

    private Provider<com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel> profileViewModelProvider2;

    private Provider<SearchViewModel> searchViewModelProvider;

    private Provider<VideoPlayerViewModel> videoPlayerViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.savedStateHandle = savedStateHandleParam;
      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    private StartDownloadUseCase startDownloadUseCase() {
      return new StartDownloadUseCase(singletonCImpl.downloadManagerProvider.get(), singletonCImpl.videoRepositoryImplProvider.get());
    }

    private GetOrdersUseCase getOrdersUseCase() {
      return new GetOrdersUseCase(singletonCImpl.userRepositoryImplProvider.get());
    }

    private GetProfileDataUseCase getProfileDataUseCase() {
      return new GetProfileDataUseCase(singletonCImpl.userRepositoryImplProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.cacheManagerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.earningsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.homeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.loginViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.ordersViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.paymentViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.profileViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.profileViewModelProvider2 = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.searchViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.videoPlayerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
    }

    @Override
    public Map<Class<?>, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return LazyClassKeyMap.<javax.inject.Provider<ViewModel>>of(ImmutableMap.<String, javax.inject.Provider<ViewModel>>builderWithExpectedSize(10).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_CacheManagerViewModel, ((Provider) cacheManagerViewModelProvider)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_EarningsViewModel, ((Provider) earningsViewModelProvider)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_HomeViewModel, ((Provider) homeViewModelProvider)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_LoginViewModel, ((Provider) loginViewModelProvider)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_orders_OrdersViewModel, ((Provider) ordersViewModelProvider)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_PaymentViewModel, ((Provider) paymentViewModelProvider)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_ProfileViewModel, ((Provider) profileViewModelProvider)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_profile_ProfileViewModel, ((Provider) profileViewModelProvider2)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_SearchViewModel, ((Provider) searchViewModelProvider)).put(LazyClassKeyProvider.com_shuimu_course_presentation_viewmodel_VideoPlayerViewModel, ((Provider) videoPlayerViewModelProvider)).build());
    }

    @Override
    public Map<Class<?>, Object> getHiltViewModelAssistedMap() {
      return ImmutableMap.<Class<?>, Object>of();
    }

    @IdentifierNameString
    private static final class LazyClassKeyProvider {
      static String com_shuimu_course_presentation_viewmodel_CacheManagerViewModel = "com.shuimu.course.presentation.viewmodel.CacheManagerViewModel";

      static String com_shuimu_course_presentation_viewmodel_ProfileViewModel = "com.shuimu.course.presentation.viewmodel.ProfileViewModel";

      static String com_shuimu_course_presentation_viewmodel_SearchViewModel = "com.shuimu.course.presentation.viewmodel.SearchViewModel";

      static String com_shuimu_course_presentation_viewmodel_HomeViewModel = "com.shuimu.course.presentation.viewmodel.HomeViewModel";

      static String com_shuimu_course_presentation_viewmodel_LoginViewModel = "com.shuimu.course.presentation.viewmodel.LoginViewModel";

      static String com_shuimu_course_presentation_viewmodel_EarningsViewModel = "com.shuimu.course.presentation.viewmodel.EarningsViewModel";

      static String com_shuimu_course_presentation_viewmodel_PaymentViewModel = "com.shuimu.course.presentation.viewmodel.PaymentViewModel";

      static String com_shuimu_course_presentation_viewmodel_VideoPlayerViewModel = "com.shuimu.course.presentation.viewmodel.VideoPlayerViewModel";

      static String com_shuimu_course_presentation_viewmodel_orders_OrdersViewModel = "com.shuimu.course.presentation.viewmodel.orders.OrdersViewModel";

      static String com_shuimu_course_presentation_viewmodel_profile_ProfileViewModel = "com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel";

      @KeepFieldType
      CacheManagerViewModel com_shuimu_course_presentation_viewmodel_CacheManagerViewModel2;

      @KeepFieldType
      ProfileViewModel com_shuimu_course_presentation_viewmodel_ProfileViewModel2;

      @KeepFieldType
      SearchViewModel com_shuimu_course_presentation_viewmodel_SearchViewModel2;

      @KeepFieldType
      HomeViewModel com_shuimu_course_presentation_viewmodel_HomeViewModel2;

      @KeepFieldType
      LoginViewModel com_shuimu_course_presentation_viewmodel_LoginViewModel2;

      @KeepFieldType
      EarningsViewModel com_shuimu_course_presentation_viewmodel_EarningsViewModel2;

      @KeepFieldType
      PaymentViewModel com_shuimu_course_presentation_viewmodel_PaymentViewModel2;

      @KeepFieldType
      VideoPlayerViewModel com_shuimu_course_presentation_viewmodel_VideoPlayerViewModel2;

      @KeepFieldType
      OrdersViewModel com_shuimu_course_presentation_viewmodel_orders_OrdersViewModel2;

      @KeepFieldType
      com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel com_shuimu_course_presentation_viewmodel_profile_ProfileViewModel2;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.shuimu.course.presentation.viewmodel.CacheManagerViewModel 
          return (T) new CacheManagerViewModel(singletonCImpl.cacheRepositoryImplProvider.get(), singletonCImpl.seriesRepositoryImplProvider.get());

          case 1: // com.shuimu.course.presentation.viewmodel.EarningsViewModel 
          return (T) new EarningsViewModel(singletonCImpl.shareRepositoryImplProvider.get());

          case 2: // com.shuimu.course.presentation.viewmodel.HomeViewModel 
          return (T) new HomeViewModel(singletonCImpl.seriesRepositoryImplProvider.get(), singletonCImpl.cacheRepositoryImplProvider.get(), singletonCImpl.userPreferenceRepositoryImplProvider.get(), viewModelCImpl.startDownloadUseCase(), singletonCImpl.downloadManagerProvider.get());

          case 3: // com.shuimu.course.presentation.viewmodel.LoginViewModel 
          return (T) new LoginViewModel(singletonCImpl.userRepositoryImplProvider.get());

          case 4: // com.shuimu.course.presentation.viewmodel.orders.OrdersViewModel 
          return (T) new OrdersViewModel(viewModelCImpl.getOrdersUseCase());

          case 5: // com.shuimu.course.presentation.viewmodel.PaymentViewModel 
          return (T) new PaymentViewModel(singletonCImpl.paymentRepositoryImplProvider.get(), singletonCImpl.dataSyncManagerProvider.get(), viewModelCImpl.savedStateHandle);

          case 6: // com.shuimu.course.presentation.viewmodel.ProfileViewModel 
          return (T) new ProfileViewModel(singletonCImpl.userRepositoryImplProvider.get());

          case 7: // com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel 
          return (T) new com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel(viewModelCImpl.getProfileDataUseCase());

          case 8: // com.shuimu.course.presentation.viewmodel.SearchViewModel 
          return (T) new SearchViewModel(singletonCImpl.searchRepositoryImplProvider.get());

          case 9: // com.shuimu.course.presentation.viewmodel.VideoPlayerViewModel 
          return (T) new VideoPlayerViewModel(singletonCImpl.seriesRepositoryImplProvider.get(), singletonCImpl.playProgressRepositoryImplProvider.get(), singletonCImpl.videoRepositoryImplProvider.get(), singletonCImpl.playlistRepositoryImplProvider.get(), viewModelCImpl.savedStateHandle);

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends CourseApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends CourseApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends CourseApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<AppDatabase> provideAppDatabaseProvider;

    private Provider<CacheInfoDao> provideCacheInfoDaoProvider;

    private Provider<VideoDao> provideVideoDaoProvider;

    private Provider<HttpLoggingInterceptor> provideLoggingInterceptorProvider;

    private Provider<Interceptor> provideHostInterceptorProvider;

    private Provider<HostnameVerifier> provideHostnameVerifierProvider;

    private Provider<Dns> provideCustomDnsProvider;

    private Provider<UserIdInterceptor> userIdInterceptorProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<Retrofit> provideRetrofitProvider;

    private Provider<CacheApi> provideCacheApiProvider;

    private Provider<CacheRepositoryImpl> cacheRepositoryImplProvider;

    private Provider<PaymentApi> providePaymentApiProvider;

    private Provider<DataSyncManager> dataSyncManagerProvider;

    private Provider<VideoDownloadWorker_AssistedFactory> videoDownloadWorker_AssistedFactoryProvider;

    private Provider<AppConfigManager> appConfigManagerProvider;

    private Provider<SeriesApi> provideSeriesApiProvider;

    private Provider<SeriesDao> provideSeriesDaoProvider;

    private Provider<CategoryDao> provideCategoryDaoProvider;

    private Provider<PlayProgressDao> providePlayProgressDaoProvider;

    private Provider<SeriesRepositoryImpl> seriesRepositoryImplProvider;

    private Provider<UserApi> provideUserApiProvider;

    private Provider<ShareRepositoryImpl> shareRepositoryImplProvider;

    private Provider<UserPreferenceRepositoryImpl> userPreferenceRepositoryImplProvider;

    private Provider<DownloadManager> downloadManagerProvider;

    private Provider<VideoApi> provideVideoApiProvider;

    private Provider<VideoRepositoryImpl> videoRepositoryImplProvider;

    private Provider<AuthApi> provideAuthApiProvider;

    private Provider<UserDao> provideUserDaoProvider;

    private Provider<UserRepositoryImpl> userRepositoryImplProvider;

    private Provider<PaymentRepositoryImpl> paymentRepositoryImplProvider;

    private Provider<SearchApi> provideSearchApiProvider;

    private Provider<SearchRepositoryImpl> searchRepositoryImplProvider;

    private Provider<PlayProgressRepositoryImpl> playProgressRepositoryImplProvider;

    private Provider<PlaylistApi> providePlaylistApiProvider;

    private Provider<PlaylistRepositoryImpl> playlistRepositoryImplProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);
      initialize2(applicationContextModuleParam);

    }

    private SyncCacheStateUseCase syncCacheStateUseCase() {
      return new SyncCacheStateUseCase(cacheRepositoryImplProvider.get());
    }

    private Map<String, javax.inject.Provider<WorkerAssistedFactory<? extends ListenableWorker>>> mapOfStringAndProviderOfWorkerAssistedFactoryOf(
        ) {
      return ImmutableMap.<String, javax.inject.Provider<WorkerAssistedFactory<? extends ListenableWorker>>>of("com.shuimu.course.data.workers.VideoDownloadWorker", ((Provider) videoDownloadWorker_AssistedFactoryProvider));
    }

    private HiltWorkerFactory hiltWorkerFactory() {
      return WorkerFactoryModule_ProvideFactoryFactory.provideFactory(mapOfStringAndProviderOfWorkerAssistedFactoryOf());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideAppDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<AppDatabase>(singletonCImpl, 3));
      this.provideCacheInfoDaoProvider = DoubleCheck.provider(new SwitchingProvider<CacheInfoDao>(singletonCImpl, 2));
      this.provideVideoDaoProvider = DoubleCheck.provider(new SwitchingProvider<VideoDao>(singletonCImpl, 4));
      this.provideLoggingInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<HttpLoggingInterceptor>(singletonCImpl, 8));
      this.provideHostInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<Interceptor>(singletonCImpl, 9));
      this.provideHostnameVerifierProvider = DoubleCheck.provider(new SwitchingProvider<HostnameVerifier>(singletonCImpl, 10));
      this.provideCustomDnsProvider = DoubleCheck.provider(new SwitchingProvider<Dns>(singletonCImpl, 11));
      this.userIdInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<UserIdInterceptor>(singletonCImpl, 12));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 7));
      this.provideRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 6));
      this.provideCacheApiProvider = DoubleCheck.provider(new SwitchingProvider<CacheApi>(singletonCImpl, 5));
      this.cacheRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<CacheRepositoryImpl>(singletonCImpl, 1));
      this.providePaymentApiProvider = DoubleCheck.provider(new SwitchingProvider<PaymentApi>(singletonCImpl, 14));
      this.dataSyncManagerProvider = DoubleCheck.provider(new SwitchingProvider<DataSyncManager>(singletonCImpl, 13));
      this.videoDownloadWorker_AssistedFactoryProvider = SingleCheck.provider(new SwitchingProvider<VideoDownloadWorker_AssistedFactory>(singletonCImpl, 0));
      this.appConfigManagerProvider = DoubleCheck.provider(new SwitchingProvider<AppConfigManager>(singletonCImpl, 15));
      this.provideSeriesApiProvider = DoubleCheck.provider(new SwitchingProvider<SeriesApi>(singletonCImpl, 17));
      this.provideSeriesDaoProvider = DoubleCheck.provider(new SwitchingProvider<SeriesDao>(singletonCImpl, 18));
      this.provideCategoryDaoProvider = DoubleCheck.provider(new SwitchingProvider<CategoryDao>(singletonCImpl, 19));
      this.providePlayProgressDaoProvider = DoubleCheck.provider(new SwitchingProvider<PlayProgressDao>(singletonCImpl, 20));
      this.seriesRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<SeriesRepositoryImpl>(singletonCImpl, 16));
      this.provideUserApiProvider = DoubleCheck.provider(new SwitchingProvider<UserApi>(singletonCImpl, 22));
      this.shareRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<ShareRepositoryImpl>(singletonCImpl, 21));
      this.userPreferenceRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserPreferenceRepositoryImpl>(singletonCImpl, 23));
      this.downloadManagerProvider = DoubleCheck.provider(new SwitchingProvider<DownloadManager>(singletonCImpl, 24));
    }

    @SuppressWarnings("unchecked")
    private void initialize2(final ApplicationContextModule applicationContextModuleParam) {
      this.provideVideoApiProvider = DoubleCheck.provider(new SwitchingProvider<VideoApi>(singletonCImpl, 26));
      this.videoRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<VideoRepositoryImpl>(singletonCImpl, 25));
      this.provideAuthApiProvider = DoubleCheck.provider(new SwitchingProvider<AuthApi>(singletonCImpl, 28));
      this.provideUserDaoProvider = DoubleCheck.provider(new SwitchingProvider<UserDao>(singletonCImpl, 29));
      this.userRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserRepositoryImpl>(singletonCImpl, 27));
      this.paymentRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<PaymentRepositoryImpl>(singletonCImpl, 30));
      this.provideSearchApiProvider = DoubleCheck.provider(new SwitchingProvider<SearchApi>(singletonCImpl, 32));
      this.searchRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<SearchRepositoryImpl>(singletonCImpl, 31));
      this.playProgressRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<PlayProgressRepositoryImpl>(singletonCImpl, 33));
      this.providePlaylistApiProvider = DoubleCheck.provider(new SwitchingProvider<PlaylistApi>(singletonCImpl, 35));
      this.playlistRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<PlaylistRepositoryImpl>(singletonCImpl, 34));
    }

    @Override
    public void injectCourseApplication(CourseApplication courseApplication) {
      injectCourseApplication2(courseApplication);
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private CourseApplication injectCourseApplication2(CourseApplication instance) {
      CourseApplication_MembersInjector.injectWorkerFactory(instance, hiltWorkerFactory());
      CourseApplication_MembersInjector.injectAppConfigManager(instance, appConfigManagerProvider.get());
      CourseApplication_MembersInjector.injectDataSyncManager(instance, dataSyncManagerProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.shuimu.course.data.workers.VideoDownloadWorker_AssistedFactory 
          return (T) new VideoDownloadWorker_AssistedFactory() {
            @Override
            public VideoDownloadWorker create(Context context, WorkerParameters workerParams) {
              return new VideoDownloadWorker(context, workerParams, singletonCImpl.cacheRepositoryImplProvider.get(), singletonCImpl.syncCacheStateUseCase(), singletonCImpl.dataSyncManagerProvider.get());
            }
          };

          case 1: // com.shuimu.course.data.repository.CacheRepositoryImpl 
          return (T) new CacheRepositoryImpl(singletonCImpl.provideCacheInfoDaoProvider.get(), singletonCImpl.provideVideoDaoProvider.get(), singletonCImpl.provideCacheApiProvider.get());

          case 2: // com.shuimu.course.data.local.dao.CacheInfoDao 
          return (T) DatabaseModule_ProvideCacheInfoDaoFactory.provideCacheInfoDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 3: // com.shuimu.course.data.local.database.AppDatabase 
          return (T) DatabaseModule_ProvideAppDatabaseFactory.provideAppDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 4: // com.shuimu.course.data.local.dao.VideoDao 
          return (T) DatabaseModule_ProvideVideoDaoFactory.provideVideoDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 5: // com.shuimu.course.data.remote.api.CacheApi 
          return (T) NetworkModule_ProvideCacheApiFactory.provideCacheApi(singletonCImpl.provideRetrofitProvider.get());

          case 6: // retrofit2.Retrofit 
          return (T) NetworkModule_ProvideRetrofitFactory.provideRetrofit(singletonCImpl.provideOkHttpClientProvider.get());

          case 7: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient(singletonCImpl.provideLoggingInterceptorProvider.get(), singletonCImpl.provideHostInterceptorProvider.get(), singletonCImpl.provideHostnameVerifierProvider.get(), singletonCImpl.provideCustomDnsProvider.get(), singletonCImpl.userIdInterceptorProvider.get());

          case 8: // okhttp3.logging.HttpLoggingInterceptor 
          return (T) NetworkModule_ProvideLoggingInterceptorFactory.provideLoggingInterceptor();

          case 9: // okhttp3.Interceptor 
          return (T) NetworkModule_ProvideHostInterceptorFactory.provideHostInterceptor();

          case 10: // javax.net.ssl.HostnameVerifier 
          return (T) NetworkModule_ProvideHostnameVerifierFactory.provideHostnameVerifier();

          case 11: // okhttp3.Dns 
          return (T) NetworkModule_ProvideCustomDnsFactory.provideCustomDns();

          case 12: // com.shuimu.course.data.remote.interceptors.UserIdInterceptor 
          return (T) new UserIdInterceptor();

          case 13: // com.shuimu.course.data.manager.DataSyncManager 
          return (T) new DataSyncManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.providePaymentApiProvider.get(), singletonCImpl.provideCacheApiProvider.get(), singletonCImpl.cacheRepositoryImplProvider.get());

          case 14: // com.shuimu.course.data.remote.api.PaymentApi 
          return (T) NetworkModule_ProvidePaymentApiFactory.providePaymentApi(singletonCImpl.provideRetrofitProvider.get());

          case 15: // com.shuimu.course.data.manager.AppConfigManager 
          return (T) new AppConfigManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideCacheApiProvider.get());

          case 16: // com.shuimu.course.data.repository.SeriesRepositoryImpl 
          return (T) new SeriesRepositoryImpl(singletonCImpl.provideSeriesApiProvider.get(), singletonCImpl.provideSeriesDaoProvider.get(), singletonCImpl.provideCategoryDaoProvider.get(), singletonCImpl.provideVideoDaoProvider.get(), singletonCImpl.providePlayProgressDaoProvider.get(), singletonCImpl.provideCacheInfoDaoProvider.get());

          case 17: // com.shuimu.course.data.remote.api.SeriesApi 
          return (T) NetworkModule_ProvideSeriesApiFactory.provideSeriesApi(singletonCImpl.provideRetrofitProvider.get());

          case 18: // com.shuimu.course.data.local.dao.SeriesDao 
          return (T) DatabaseModule_ProvideSeriesDaoFactory.provideSeriesDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 19: // com.shuimu.course.data.local.dao.CategoryDao 
          return (T) DatabaseModule_ProvideCategoryDaoFactory.provideCategoryDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 20: // com.shuimu.course.data.local.dao.PlayProgressDao 
          return (T) DatabaseModule_ProvidePlayProgressDaoFactory.providePlayProgressDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 21: // com.shuimu.course.data.repository.ShareRepositoryImpl 
          return (T) new ShareRepositoryImpl(singletonCImpl.provideUserApiProvider.get());

          case 22: // com.shuimu.course.data.remote.api.UserApi 
          return (T) NetworkModule_ProvideUserApiFactory.provideUserApi(singletonCImpl.provideRetrofitProvider.get());

          case 23: // com.shuimu.course.data.repository.UserPreferenceRepositoryImpl 
          return (T) new UserPreferenceRepositoryImpl(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 24: // com.shuimu.course.data.workers.DownloadManager 
          return (T) new DownloadManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.cacheRepositoryImplProvider.get(), singletonCImpl.appConfigManagerProvider.get());

          case 25: // com.shuimu.course.data.repository.VideoRepositoryImpl 
          return (T) new VideoRepositoryImpl(singletonCImpl.provideVideoApiProvider.get(), singletonCImpl.provideVideoDaoProvider.get());

          case 26: // com.shuimu.course.data.remote.api.VideoApi 
          return (T) NetworkModule_ProvideVideoApiFactory.provideVideoApi(singletonCImpl.provideRetrofitProvider.get());

          case 27: // com.shuimu.course.data.repository.UserRepositoryImpl 
          return (T) new UserRepositoryImpl(singletonCImpl.provideAuthApiProvider.get(), singletonCImpl.provideUserApiProvider.get(), singletonCImpl.provideUserDaoProvider.get());

          case 28: // com.shuimu.course.data.remote.api.AuthApi 
          return (T) NetworkModule_ProvideAuthApiFactory.provideAuthApi(singletonCImpl.provideRetrofitProvider.get());

          case 29: // com.shuimu.course.data.local.dao.UserDao 
          return (T) DatabaseModule_ProvideUserDaoFactory.provideUserDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 30: // com.shuimu.course.data.repository.PaymentRepositoryImpl 
          return (T) new PaymentRepositoryImpl(singletonCImpl.providePaymentApiProvider.get());

          case 31: // com.shuimu.course.data.repository.SearchRepositoryImpl 
          return (T) new SearchRepositoryImpl(singletonCImpl.provideSearchApiProvider.get());

          case 32: // com.shuimu.course.data.remote.api.SearchApi 
          return (T) NetworkModule_ProvideSearchApiFactory.provideSearchApi(singletonCImpl.provideRetrofitProvider.get());

          case 33: // com.shuimu.course.data.repository.PlayProgressRepositoryImpl 
          return (T) new PlayProgressRepositoryImpl(singletonCImpl.providePlayProgressDaoProvider.get());

          case 34: // com.shuimu.course.data.repository.PlaylistRepositoryImpl 
          return (T) new PlaylistRepositoryImpl(singletonCImpl.providePlaylistApiProvider.get());

          case 35: // com.shuimu.course.data.remote.api.PlaylistApi 
          return (T) NetworkModule_ProvidePlaylistApiFactory.providePlaylistApi(singletonCImpl.provideRetrofitProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
