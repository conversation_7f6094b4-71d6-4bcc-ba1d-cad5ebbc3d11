package com.shuimu.course.presentation.ui.components.dialog

import androidx.compose.animation.core.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.course.presentation.ui.components.base.PaymentPrimaryButton
import com.shuimu.course.presentation.ui.theme.DangerRed
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.ui.theme.TextSecondaryLight
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

@Composable
fun PaymentSlideUpSheet(
    visible: Boolean,
    courseTitle: String,
    price: String,
    onPay: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    rewardHint: String = "购买后可享受30%分享分成",
    loading: Boolean = false,
    sheetHeight: androidx.compose.ui.unit.Dp = 198.dp,
    enableDragToClose: Boolean = true
) {
    if (!visible) return
    
    val scope = rememberCoroutineScope()
    val offsetYPx = remember { Animatable(sheetHeight.value * 3) } // Start from bottom
    
    // Animate in when visible
    LaunchedEffect(visible) {
        if (visible) {
            offsetYPx.animateTo(0f, tween(300, easing = FastOutSlowInEasing))
        }
    }
    
    val dragAwareModifier = if (enableDragToClose) {
        Modifier.pointerInput(Unit) {
            detectVerticalDragGestures(
                onDragEnd = {
                    if (offsetYPx.value > sheetHeight.value * 0.3f) {
                        // Drag to close
                        scope.launch {
                            offsetYPx.animateTo(sheetHeight.value * 3, tween(200))
                            onDismiss()
                        }
                    } else {
                        // Snap back
                        scope.launch {
                            offsetYPx.animateTo(0f, tween(200))
                        }
                    }
                },
                onVerticalDrag = { _, dragAmount ->
                    val newY = (offsetYPx.value + dragAmount).coerceAtLeast(0f)
                    scope.launch {
                        offsetYPx.snapTo(newY)
                    }
                }
            )
        }
    } else Modifier
    
    // Only return the Surface, no wrapping Box
    Surface(
        modifier = modifier
            .offset { IntOffset(0, offsetYPx.value.roundToInt()) }
            .fillMaxWidth()
            .height(sheetHeight)
            .then(dragAwareModifier),
        shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
        color = Color.White,
        shadowElevation = 8.dp
    ) {
        PaymentBottomSheetContent(
            courseTitle = courseTitle,
            price = price,
            onPay = onPay,
            onDismiss = {
                // Animate out then dismiss
                scope.launch {
                    offsetYPx.animateTo(sheetHeight.value * 3, tween(200))
                    onDismiss()
                }
            },
            rewardHint = rewardHint,
            loading = loading
        )
    }
}

@Composable
private fun PaymentBottomSheetContent(
    courseTitle: String,
    price: String,
    onPay: () -> Unit,
    onDismiss: () -> Unit,
    rewardHint: String,
    loading: Boolean
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
            .padding(top = 8.dp, bottom = 2.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 标题与关闭图标同一行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 标题（左）
            Text(
                text = courseTitle,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(end = 8.dp) // 防止过窄时文字挤到图标
            )

            // 关闭图标（右）
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "关闭",
                tint = Color.Gray,
                modifier = Modifier
                    .size(24.dp)
                    .clickable(onClick = onDismiss)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Price (large red text)
        Text(
            text = if (price.startsWith("¥")) price else "¥$price",
            fontSize = 36.sp,
            fontWeight = FontWeight.Bold,
            color = DangerRed,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(6.dp))
        
        // Reward hint (gray subtitle)
        Text(
            text = rewardHint,
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal,
            color = TextSecondaryLight,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Payment button
        PaymentPrimaryButton(
            onClick = onPay,
            text = "立即支付",
            loading = loading,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

// Keep the old PaymentBottomSheet as a wrapper for backward compatibility
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentBottomSheet(
    courseTitle: String,
    price: String,
    onPay: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    rewardHint: String = "购买后可享受30%分享分成",
    loading: Boolean = false,
    sheetState: SheetState? = null // Deprecated parameter, kept for compatibility
) {
    PaymentSlideUpSheet(
        visible = true,
        courseTitle = courseTitle,
        price = price,
        onPay = onPay,
        onDismiss = onDismiss,
        modifier = modifier,
        rewardHint = rewardHint,
        loading = loading
    )
}

@Preview
@Composable
fun PaymentSlideUpSheetPreview() {
    ShuimuCourseTheme {
        PaymentSlideUpSheet(
            visible = true,
            courseTitle = "道：恋爱宝典系列",
            price = "600",
            onPay = {},
            onDismiss = {},
            rewardHint = "购买后可享受30%分享分成",
            loading = false
        )
    }
}

@Preview
@Composable
fun PaymentSlideUpSheetLoadingPreview() {
    ShuimuCourseTheme {
        PaymentSlideUpSheet(
            visible = true,
            courseTitle = "道：恋爱宝典系列",
            price = "600",
            onPay = {},
            onDismiss = {},
            rewardHint = "购买后可享受30%分享分成",
            loading = true
        )
    }
} 