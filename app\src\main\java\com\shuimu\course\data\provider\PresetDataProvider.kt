package com.shuimu.course.data.provider

import com.shuimu.course.domain.model.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 预置数据提供者
 * 🔥 基于服务器真实数据，包含完整的4个系列，正确的初始化状态
 */
@Singleton
class PresetDataProvider @Inject constructor() {
    
    /**
     * 获取预置的系列数据
     * 基于服务器真实数据，确保离线时的用户体验
     */
    fun getPresetSeries(): List<Series> {
        return listOf(
            // 🔥 真实数据1：全套课程（套餐）
            Series(
                id = "complete-package",
                title = "全套课程",
                icon = "crown",
                price = 130000, // 1300元
                isFree = false,
                isPurchased = false, // 🔥 预置数据：未购买
                isPackage = true,
                defaultExpanded = false, // 🔥 预置数据：收起状态
                categories = emptyList() // 套餐没有分类
            ),
            
            // 🔥 真实数据2：免费精品系列
            Series(
                id = "free-series",
                title = "免费精品系列",
                icon = "play",
                price = 0,
                isFree = true,
                isPurchased = false, // 🔥 系列本身未购买，但分类已购买
                isPackage = false,
                defaultExpanded = true, // 🔥 免费系列默认展开
                categories = listOf(
                    Category(
                        id = "free-dating-tips",
                        title = "约会技巧",
                        seriesId = "free-series",
                        price = 0,
                        isFree = true,
                        isPurchased = false, // 🔥 预置数据：初始化状态，即使免费也是false
                        defaultExpanded = true,
                        videos = listOf(
                            Video(
                                id = "free-dating-prep",
                                title = "01. 约会前的准备工作",
                                duration = 930,
                                description = "学习约会前的各种准备工作，包括形象打理、心理准备等。",
                                categoryId = "free-dating-tips",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 12000,
                                cloudUrl = "https://vip.123pan.cn/1822199090/test_mock/%E6%8A%96%E9%9F%B3202569-395747.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "free-dating-location",
                                title = "02. 约会地点的选择",
                                duration = 765,
                                description = "如何选择合适的约会地点，营造浪漫氛围。",
                                categoryId = "free-dating-tips",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 8500,
                                cloudUrl = "https://example.com/videos/video2.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            )
                        ),
                        displayTitle = "约会技巧",
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    ),
                    Category(
                        id = "free-pickup-skills",
                        title = "搭讪技巧",
                        seriesId = "free-series",
                        price = 0,
                        isFree = true,
                        isPurchased = false, // 🔥 预置数据：初始化状态，即使免费也是false
                        defaultExpanded = false,
                        videos = listOf(
                            Video(
                                id = "free-pickup-mindset",
                                title = "01. 搭讪基础心态",
                                duration = 1100,
                                description = "建立正确的搭讪心态，克服内心恐惧。",
                                categoryId = "free-pickup-skills",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 21000,
                                cloudUrl = "https://example.com/videos/video3.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "free-pickup-opening",
                                title = "02. 自然开场技巧",
                                duration = 975,
                                description = "学习自然而然的开场白技巧。",
                                categoryId = "free-pickup-skills",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 18000,
                                cloudUrl = "https://example.com/videos/video4.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "free-pickup-anxiety",
                                title = "03. 克服紧张情绪",
                                duration = 870,
                                description = "有效方法帮助你克服搭讪时的紧张情绪。",
                                categoryId = "free-pickup-skills",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 15000,
                                cloudUrl = "https://example.com/videos/video5.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            )
                        ),
                        displayTitle = "搭讪技巧",
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    )
                )
            ),

            // 🔥 真实数据3：道：恋爱宝典系列
            Series(
                id = "love-guide-series",
                title = "道：恋爱宝典系列",
                icon = "heart",
                price = 60000, // 🔥 修复：600元（6个分类×100元）
                isFree = false,
                isPurchased = false, // 🔥 预置数据：未购买
                isPackage = false,
                defaultExpanded = false, // 🔥 预置数据：收起状态
                categories = listOf(
                    Category(
                        id = "love-guide-1",
                        title = "恋爱宝典1",
                        seriesId = "love-guide-series",
                        price = 10000, // 100元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = listOf(
                            Video(
                                id = "love-guide-1-attraction",
                                title = "01. 初识吸引力法则",
                                duration = 1350,
                                description = "深入了解吸引力的本质和运作原理。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 956,
                                cloudUrl = "https://example.com/videos/video6.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-confidence",
                                title = "02. 建立自信的方法",
                                duration = 1185,
                                description = "系统性方法帮助你建立内在自信。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 1200,
                                cloudUrl = "https://example.com/videos/video7.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-first-impression",
                                title = "03. 第一印象的重要性",
                                duration = 1040,
                                description = "如何在初次见面时留下深刻的好印象。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 2300,
                                cloudUrl = "https://example.com/videos/video8.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-body-language",
                                title = "04. 肢体语言的艺术",
                                duration = 1275,
                                description = "掌握肢体语言在恋爱中的重要作用。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 3100,
                                cloudUrl = "https://example.com/videos/video11.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-emotional-intelligence",
                                title = "05. 情商提升技巧",
                                duration = 1110,
                                description = "提高情商，更好地理解和处理情感。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 4500,
                                cloudUrl = "https://example.com/videos/video12.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-conversation-skills",
                                title = "06. 对话技巧进阶",
                                duration = 1425,
                                description = "掌握高级对话技巧，让聊天更有趣。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 5200,
                                cloudUrl = "https://example.com/videos/video13.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-date-planning",
                                title = "07. 约会策划大师",
                                duration = 1220,
                                description = "学会策划完美的约会，留下美好回忆。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 6800,
                                cloudUrl = "https://example.com/videos/video14.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-relationship-building",
                                title = "08. 关系建立与维护",
                                duration = 1510,
                                description = "建立稳固的恋爱关系并长期维护。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 7300,
                                cloudUrl = "https://example.com/videos/video15.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-conflict-resolution",
                                title = "09. 冲突解决智慧",
                                duration = 1170,
                                description = "学会处理恋爱中的矛盾和冲突。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 8900,
                                cloudUrl = "https://example.com/videos/video16.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-commitment",
                                title = "10. 承诺与责任",
                                duration = 1305,
                                description = "学会在恋爱中承担责任和承诺。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 9400,
                                cloudUrl = "https://example.com/videos/video17.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            ),
                            Video(
                                id = "love-guide-1-future-planning",
                                title = "11. 未来规划共识",
                                duration = 1470,
                                description = "与伴侣共同规划美好的未来。",
                                categoryId = "love-guide-1",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 11000,
                                cloudUrl = "https://example.com/videos/video18.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            )
                        ), // 🔥 预置数据：包含完整的视频数据，但状态初始化
                        displayTitle = "恋爱宝典1（¥100）", // 🔥 修复：未购买分类显示价格
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    ),
                    Category(
                        id = "love-guide-2",
                        title = "恋爱宝典2",
                        seriesId = "love-guide-series",
                        price = 10000, // 100元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = listOf(
                            Video(
                                id = "love-guide-2-deep-communication",
                                title = "01. 深度沟通技巧",
                                duration = 1575,
                                description = "进阶的深度沟通技巧和方法。",
                                categoryId = "love-guide-2",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 18000,
                                cloudUrl = "https://example.com/videos/video19.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            )
                        ), // 🔥 预置数据：包含完整的视频数据
                        displayTitle = "恋爱宝典2（¥100）", // 🔥 修复：未购买分类显示价格
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    ),
                    Category(
                        id = "love-guide-3",
                        title = "恋爱宝典3",
                        seriesId = "love-guide-series",
                        price = 10000, // 100元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = emptyList(), // 🔥 预置数据：简化
                        displayTitle = "恋爱宝典3（¥100）", // 🔥 修复：未购买分类显示价格
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    ),
                    Category(
                        id = "love-guide-4",
                        title = "恋爱宝典4",
                        seriesId = "love-guide-series",
                        price = 10000, // 100元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = emptyList(), // 🔥 预置数据：简化
                        displayTitle = "恋爱宝典4（¥100）", // 🔥 修复：未购买分类显示价格
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    ),
                    Category(
                        id = "love-guide-5",
                        title = "恋爱宝典5",
                        seriesId = "love-guide-series",
                        price = 10000, // 100元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = emptyList(), // 🔥 预置数据：简化
                        displayTitle = "恋爱宝典5（¥100）", // 🔥 修复：未购买分类显示价格
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    ),
                    Category(
                        id = "love-guide-6",
                        title = "恋爱宝典6",
                        seriesId = "love-guide-series",
                        price = 10000, // 100元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = emptyList(), // 🔥 预置数据：简化
                        displayTitle = "恋爱宝典6", // 🔥 修复：只提供分类名称
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    )
                )
            ),

            // 🔥 真实数据4：术：聊天技术系列
            Series(
                id = "chat-tech-series",
                title = "术：聊天技术系列",
                icon = "comment",
                price = 100000, // 🔥 修复：1000元（300+300+400）
                isFree = false,
                isPurchased = false, // 🔥 预置数据：未购买
                isPackage = false,
                defaultExpanded = false, // 🔥 预置数据：收起状态
                categories = listOf(
                    Category(
                        id = "long-term-relationship",
                        title = "长期关系",
                        seriesId = "chat-tech-series",
                        price = 30000, // 🔥 修复：300元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = listOf(
                            Video(
                                id = "long-term-relationship-building",
                                title = "01. 长期关系建立",
                                duration = 1320,
                                description = "学会建立稳定的长期恋爱关系。",
                                categoryId = "long-term-relationship",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 25000,
                                cloudUrl = "https://example.com/videos/video20.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            )
                        ), // 🔥 预置数据：包含完整的视频数据
                        displayTitle = "长期关系", // 🔥 修复：只提供分类名称
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    ),
                    Category(
                        id = "short-term-relationship",
                        title = "短期关系",
                        seriesId = "chat-tech-series",
                        price = 30000, // 🔥 修复：300元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = listOf(
                            Video(
                                id = "short-term-relationship-skills",
                                title = "01. 短期关系技巧",
                                duration = 1680,
                                description = "掌握短期关系的相处技巧。",
                                categoryId = "short-term-relationship",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 8000,
                                cloudUrl = "https://example.com/videos/video21.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            )
                        ), // 🔥 预置数据：包含完整的视频数据
                        displayTitle = "短期关系", // 🔥 修复：只提供分类名称
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    ),
                    Category(
                        id = "chat-skills",
                        title = "聊天技巧",
                        seriesId = "chat-tech-series",
                        price = 40000, // 🔥 修复：400元
                        isFree = false,
                        isPurchased = false, // 🔥 预置数据：未购买
                        defaultExpanded = false,
                        videos = listOf(
                            Video(
                                id = "chat-skills-advanced",
                                title = "01. 高级聊天技巧",
                                duration = 1450,
                                description = "掌握高级聊天技巧和方法。",
                                categoryId = "chat-skills",
                                watchCount = 0, // 🔥 预置数据：未观看
                                playCount = 3200,
                                cloudUrl = "https://example.com/videos/video22.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED, // 🔥 预置数据：未缓存
                                progress = 0f, // 🔥 预置数据：未观看
                                isPurchasable = false
                            )
                        ), // 🔥 预置数据：包含完整的视频数据
                        displayTitle = "聊天技巧", // 🔥 修复：只提供分类名称
                        watchCount = 0, // 🔥 预置数据：未观看
                        progress = 0f // 🔥 预置数据：未观看
                    )
                )
            )
        )
    }
}
