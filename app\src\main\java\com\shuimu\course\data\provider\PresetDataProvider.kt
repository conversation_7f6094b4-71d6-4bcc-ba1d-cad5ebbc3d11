package com.shuimu.course.data.provider

import com.shuimu.course.domain.model.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 预置数据提供者
 * 提供App内置的默认课程数据，作为兜底方案
 */
@Singleton
class PresetDataProvider @Inject constructor() {
    
    /**
     * 获取预置的系列数据
     * 基于当前的课程结构设计
     */
    fun getPresetSeries(): List<Series> {
        return listOf(
            // 系列1：自信的建立
            Series(
                id = "preset_series_1",
                title = "自信的建立",
                icon = null,
                price = 199,
                isFree = false,
                isPurchased = false, // 预置数据默认未购买
                isPackage = false,
                defaultExpanded = true,
                categories = listOf(
                    Category(
                        id = "preset_category_1_1",
                        title = "基础理论",
                        seriesId = "preset_series_1",
                        price = null,
                        isFree = false,
                        isPurchased = false,
                        defaultExpanded = true,
                        videos = listOf(
                            Video(
                                id = "preset_video_1_1_1",
                                title = "01. 自信的本质",
                                duration = 1200, // 20分钟
                                description = "深入了解自信的心理学基础",
                                categoryId = "preset_category_1_1",
                                watchCount = 0,
                                playCount = null,
                                cloudUrl = "https://example.com/video1.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED,
                                progress = 0f,
                                isPurchasable = false
                            ),
                            Video(
                                id = "preset_video_1_1_2",
                                title = "02. 自信与自尊的区别",
                                duration = 900, // 15分钟
                                description = "理解自信和自尊的本质差异",
                                categoryId = "preset_category_1_1",
                                watchCount = 0,
                                playCount = null,
                                cloudUrl = "https://example.com/video2.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED,
                                progress = 0f,
                                isPurchasable = false
                            )
                        ),
                        displayTitle = "基础理论",
                        watchCount = 0,
                        progress = 0f
                    ),
                    Category(
                        id = "preset_category_1_2",
                        title = "实践方法",
                        seriesId = "preset_series_1",
                        price = null,
                        isFree = false,
                        isPurchased = false,
                        defaultExpanded = false,
                        videos = listOf(
                            Video(
                                id = "preset_video_1_2_1",
                                title = "03. 日常自信练习",
                                duration = 1500, // 25分钟
                                description = "实用的自信建立练习方法",
                                categoryId = "preset_category_1_2",
                                watchCount = 0,
                                playCount = null,
                                cloudUrl = "https://example.com/video3.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED,
                                progress = 0f,
                                isPurchasable = false
                            )
                        ),
                        displayTitle = "实践方法",
                        watchCount = 0,
                        progress = 0f
                    )
                )
            ),
            
            // 系列2：沟通技巧
            Series(
                id = "preset_series_2",
                title = "沟通技巧",
                icon = null,
                price = 299,
                isFree = false,
                isPurchased = false,
                isPackage = false,
                defaultExpanded = false,
                categories = listOf(
                    Category(
                        id = "preset_category_2_1",
                        title = "基础沟通",
                        seriesId = "preset_series_2",
                        price = null,
                        isFree = false,
                        isPurchased = false,
                        defaultExpanded = true,
                        videos = listOf(
                            Video(
                                id = "preset_video_2_1_1",
                                title = "01. 有效倾听的艺术",
                                duration = 1800, // 30分钟
                                description = "学会真正的倾听技巧",
                                categoryId = "preset_category_2_1",
                                watchCount = 0,
                                playCount = null,
                                cloudUrl = "https://example.com/video4.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED,
                                progress = 0f,
                                isPurchasable = false
                            ),
                            Video(
                                id = "preset_video_2_1_2",
                                title = "02. 表达的技巧",
                                duration = 1200, // 20分钟
                                description = "清晰有效的表达方法",
                                categoryId = "preset_category_2_1",
                                watchCount = 0,
                                playCount = null,
                                cloudUrl = "https://example.com/video5.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED,
                                progress = 0f,
                                isPurchasable = false
                            )
                        ),
                        displayTitle = "基础沟通",
                        watchCount = 0,
                        progress = 0f
                    )
                )
            ),
            
            // 系列3：免费体验课程
            Series(
                id = "preset_series_3",
                title = "免费体验课程",
                icon = null,
                price = 0,
                isFree = true,
                isPurchased = true, // 免费课程默认已购买
                isPackage = false,
                defaultExpanded = true,
                categories = listOf(
                    Category(
                        id = "preset_category_3_1",
                        title = "入门指导",
                        seriesId = "preset_series_3",
                        price = null,
                        isFree = true,
                        isPurchased = true,
                        defaultExpanded = true,
                        videos = listOf(
                            Video(
                                id = "preset_video_3_1_1",
                                title = "欢迎使用水幕情感课程",
                                duration = 600, // 10分钟
                                description = "了解如何使用本App学习情感课程",
                                categoryId = "preset_category_3_1",
                                watchCount = 0,
                                playCount = null,
                                cloudUrl = "https://example.com/welcome.mp4",
                                localPath = null,
                                cacheStatus = CacheStatus.NOT_CACHED,
                                progress = 0f,
                                isPurchasable = true // 免费课程可以播放
                            )
                        ),
                        displayTitle = "入门指导",
                        watchCount = 0,
                        progress = 0f
                    )
                )
            )
        )
    }
    
    /**
     * 获取预置数据的统计信息
     */
    fun getPresetDataStats(): PresetDataStats {
        val series = getPresetSeries()
        val totalVideos = series.sumOf { it.categories.sumOf { category -> category.videos.size } }
        val totalDuration = series.sumOf { it.categories.sumOf { category -> 
            category.videos.sumOf { video -> video.duration } 
        } }
        
        return PresetDataStats(
            seriesCount = series.size,
            categoryCount = series.sumOf { it.categories.size },
            videoCount = totalVideos,
            totalDurationSeconds = totalDuration
        )
    }
}

/**
 * 预置数据统计信息
 */
data class PresetDataStats(
    val seriesCount: Int,
    val categoryCount: Int,
    val videoCount: Int,
    val totalDurationSeconds: Int
)
