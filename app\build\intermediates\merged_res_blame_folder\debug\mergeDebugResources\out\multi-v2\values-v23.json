{"logs": [{"outputFile": "com.shuimu.course.app-mergeDebugResources-85:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\ad601f1f1ae6fb89202723eea59af52d\\transformed\\material-1.12.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,5,9,13,16,19,22,25,28,32", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,271,413,574,784,991,1198,1401,1603,1868", "endLines": "4,8,12,15,18,21,24,27,31,35", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,408,569,779,986,1193,1396,1598,1863,2136"}, "to": {"startLines": "55,58,62,66,69,72,75,78,81,85", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3511,3727,3869,4030,4240,4447,4654,4857,5059,5324", "endLines": "57,61,65,68,71,74,77,80,84,88", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "3722,3864,4025,4235,4442,4649,4852,5054,5319,5592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\e66f0f998bbab9a0850368189bf6197d\\transformed\\work-runtime-2.9.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\886ce1740e58ab542309752b74bbc803\\transformed\\appcompat-1.6.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}, "to": {"startLines": "4,5,6,7,8,22,36,37,38,41,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,319,454,529,616,1354,2104,2223,2350,2572,2796,2911,3018,3131", "endLines": "4,5,6,7,21,35,36,37,40,44,45,46,47,51", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "314,449,524,611,1349,2099,2218,2345,2567,2791,2906,3013,3126,3356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\9.0-milestone-1\\transforms\\b81bb2fc572c58bfc9779f1dc79b499d\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "3361", "endLines": "54", "endColumns": "12", "endOffsets": "3506"}}]}]}