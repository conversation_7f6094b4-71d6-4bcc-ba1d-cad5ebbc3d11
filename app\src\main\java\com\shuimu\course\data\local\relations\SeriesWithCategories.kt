package com.shuimu.course.data.local.relations

import androidx.room.Embedded
import androidx.room.Relation
import com.shuimu.course.data.local.entities.CategoryEntity
import com.shuimu.course.data.local.entities.SeriesEntity

data class SeriesWithCategories(
    @Embedded val series: SeriesEntity,
    @Relation(
        entity = CategoryEntity::class,
        parentColumn = "id",
        entityColumn = "series_id"
    )
    val categoriesWithVideos: List<CategoryWithVideos>
) 