package com.shuimu.course.domain.model

/**
 * 缓存视频 Domain 模型
 */
data class CachedVideo(
    val videoId: String,
    val title: String,
    val localPath: String?,
    val downloadedBytes: Long,
    val totalBytes: Long,
    val status: CacheDownloadStatus,
    val progress: Int, // 0-100
    val cloudUrl: String?,
    val updatedAt: Long,
    val errorMessage: String? = null
) {
    /**
     * 获取下载进度百分比
     */
    fun getProgressPercentage(): Int {
        return if (totalBytes > 0) {
            ((downloadedBytes * 100) / totalBytes).toInt()
        } else {
            progress
        }
    }

    /**
     * 是否已缓存完成
     */
    fun isCached(): Boolean = status == CacheDownloadStatus.DOWNLOADED

    /**
     * 是否正在下载
     */
    fun isDownloading(): Boolean = status == CacheDownloadStatus.DOWNLOADING

    /**
     * 是否可以播放
     */
    fun canPlay(): Boolean = isCached() && !localPath.isNullOrEmpty()

    /**
     * 获取显示文本（用于UI显示）
     */
    fun getDisplayText(): String = when (status) {
        CacheDownloadStatus.DOWNLOADED -> "（已缓存）"
        CacheDownloadStatus.DOWNLOADING -> "（${getProgressPercentage()}%）"
        CacheDownloadStatus.WAITING_NETWORK -> "（等待网络）"
        CacheDownloadStatus.PENDING -> "（等待下载）"
        CacheDownloadStatus.PAUSED -> "（已暂停）"
        CacheDownloadStatus.FAILED -> "（下载失败）"
        CacheDownloadStatus.NOT_CACHED -> ""
    }

    /**
     * 获取显示颜色（用于UI显示）
     */
    fun getDisplayColor(): String = when (status) {
        CacheDownloadStatus.DOWNLOADED -> "#4CAF50" // 绿色
        CacheDownloadStatus.DOWNLOADING -> "#2196F3" // 蓝色
        CacheDownloadStatus.WAITING_NETWORK -> "#FF5722" // 深橙色
        CacheDownloadStatus.PENDING -> "#FF9800" // 橙色
        CacheDownloadStatus.PAUSED -> "#9E9E9E" // 灰色
        CacheDownloadStatus.FAILED -> "#F44336" // 红色
        CacheDownloadStatus.NOT_CACHED -> "#000000" // 黑色
    }
}

/**
 * 缓存下载状态枚举
 */
enum class CacheDownloadStatus {
    NOT_CACHED,     // 未缓存
    WAITING_NETWORK, // 等待网络条件满足
    DOWNLOADING,    // 正在下载
    DOWNLOADED,     // 已下载完成
    PAUSED,         // 下载暂停
    FAILED,         // 下载失败
    PENDING         // 等待下载（队列中）
} 