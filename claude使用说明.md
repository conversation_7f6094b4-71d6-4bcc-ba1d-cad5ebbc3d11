# Claude CLI 使用说明 - 简单版

## 🎯 现在你有什么？

你现在有一个 **claude 命令**，可以在任何地方使用！

## 📱 两种使用方式

### 方式1：离线版（现在就能用）
不需要API密钥，提供基本文本处理功能

**可以做什么：**
- 简单翻译
- 统计字数
- 格式化文本
- 提取邮箱/网址等

**示例：**
```bash
# 简单翻译
claude --translate zh "Hello World"        # 英译中
claude --translate en "你好世界"            # 中译英

# 统计字数
echo "你的文本" | claude --count

# 格式化文本
echo "hello world" | claude --format title  # 变成 Hello World

# 提取邮箱
echo "联系我：<EMAIL>" | claude --extract emails
```

### 方式2：完整版（需要API密钥）
连接真正的Claude AI，功能超强！

**获取API密钥步骤：**
1. 打开 https://console.anthropic.com/
2. 用你的Claude账户登录（和网页版同一个）
3. 点击 "API Keys" → "Create Key"
4. 复制密钥（像这样：sk-ant-api03-xxx...）
5. 运行：`claude --config api_key 你的密钥`

**API密钥 vs Claude Pro的区别：**
- Claude Pro = 网页版订阅，只能在浏览器用
- API密钥 = 让程序调用Claude，命令行工具需要这个

## 🚀 常用命令

```bash
# 查看帮助
claude --help

# 查看版本  
claude --version

# 直接问问题（需要API密钥）
claude "解释什么是人工智能"

# 处理文件（管道操作）
type 文件.txt | claude -p "总结这个文件"

# 翻译文件
type english.txt | claude -p "翻译成中文" > chinese.txt
```

## 💡 实用技巧

### 代码相关
```bash
# 代码审查
type mycode.js | claude -p "审查这段代码"

# 解释代码
type complex.py | claude -p "解释这个算法"
```

### 文档处理
```bash
# 文档总结
type 长文档.txt | claude -p "写个摘要"

# 提取关键词
type 文章.md | claude --extract emails  # 提取邮箱（离线功能）
```

## 🔧 如果遇到问题

### 问题1：找不到claude命令
**解决**：重启终端，或者运行：
```bash
# Windows
$env:PATH += ";$env:USERPROFILE"

# WSL
export PATH="$HOME/bin:$PATH"
```

### 问题2：需要API密钥
**解决**：
1. 去 https://console.anthropic.com/ 获取
2. 运行：`claude --config api_key 你的密钥`

### 问题3：网络连接错误
**解决**：检查网络，或者先用离线功能

## 🎉 开始使用

**试试这些命令：**
```bash
# 离线功能测试
claude --translate zh "Hello Programming"
echo "测试文本123" | claude --count

# 如果有API密钥
claude "写一个编程励志语录"
```

就这么简单！🚀 