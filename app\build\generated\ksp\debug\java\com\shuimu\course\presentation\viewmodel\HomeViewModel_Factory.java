package com.shuimu.course.presentation.viewmodel;

import com.shuimu.course.data.workers.DownloadManager;
import com.shuimu.course.domain.repository.CacheRepository;
import com.shuimu.course.domain.repository.SeriesRepository;
import com.shuimu.course.domain.repository.UserPreferenceRepository;
import com.shuimu.course.domain.usecase.cache.StartDownloadUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class HomeViewModel_Factory implements Factory<HomeViewModel> {
  private final Provider<SeriesRepository> seriesRepositoryProvider;

  private final Provider<CacheRepository> cacheRepositoryProvider;

  private final Provider<UserPreferenceRepository> userPreferenceRepositoryProvider;

  private final Provider<StartDownloadUseCase> startDownloadUseCaseProvider;

  private final Provider<DownloadManager> downloadManagerProvider;

  public HomeViewModel_Factory(Provider<SeriesRepository> seriesRepositoryProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider,
      Provider<StartDownloadUseCase> startDownloadUseCaseProvider,
      Provider<DownloadManager> downloadManagerProvider) {
    this.seriesRepositoryProvider = seriesRepositoryProvider;
    this.cacheRepositoryProvider = cacheRepositoryProvider;
    this.userPreferenceRepositoryProvider = userPreferenceRepositoryProvider;
    this.startDownloadUseCaseProvider = startDownloadUseCaseProvider;
    this.downloadManagerProvider = downloadManagerProvider;
  }

  @Override
  public HomeViewModel get() {
    return newInstance(seriesRepositoryProvider.get(), cacheRepositoryProvider.get(), userPreferenceRepositoryProvider.get(), startDownloadUseCaseProvider.get(), downloadManagerProvider.get());
  }

  public static HomeViewModel_Factory create(Provider<SeriesRepository> seriesRepositoryProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider,
      Provider<StartDownloadUseCase> startDownloadUseCaseProvider,
      Provider<DownloadManager> downloadManagerProvider) {
    return new HomeViewModel_Factory(seriesRepositoryProvider, cacheRepositoryProvider, userPreferenceRepositoryProvider, startDownloadUseCaseProvider, downloadManagerProvider);
  }

  public static HomeViewModel newInstance(SeriesRepository seriesRepository,
      CacheRepository cacheRepository, UserPreferenceRepository userPreferenceRepository,
      StartDownloadUseCase startDownloadUseCase, DownloadManager downloadManager) {
    return new HomeViewModel(seriesRepository, cacheRepository, userPreferenceRepository, startDownloadUseCase, downloadManager);
  }
}
