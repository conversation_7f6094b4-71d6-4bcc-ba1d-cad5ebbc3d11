package com.shuimu.course.presentation.ui.components.share

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.components.user.UserAvatar
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun RankingItem(
    modifier: Modifier = Modifier,
    rank: Int,
    avatarUrl: String?,
    username: String,
    earnings: String,
    isCurrentUser: Boolean = false
) {
    val backgroundColor = if (isCurrentUser) {
        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
    } else {
        MaterialTheme.colorScheme.surface
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(backgroundColor, shape = MaterialTheme.shapes.medium)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.onSurface.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = rank.toString(),
                fontWeight = FontWeight.Bold,
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        UserAvatar(avatarUrl = avatarUrl, size = 40.dp)

        Spacer(modifier = Modifier.width(12.dp))

        Text(
            text = username,
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.SemiBold
        )

        Text(
            text = "¥$earnings",
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

@Preview(name = "Ranking Item - Top User")
@Composable
fun RankingItemTopPreview() {
    ShuimuCourseTheme {
        RankingItem(
            rank = 1,
            avatarUrl = "",
            username = "阿强",
            earnings = "3,245.00"
        )
    }
}

@Preview(name = "Ranking Item - Current User")
@Composable
fun RankingItemCurrentUserPreview() {
    ShuimuCourseTheme {
        RankingItem(
            rank = 15,
            avatarUrl = "",
            username = "张三 (我)",
            earnings = "456.50",
            isCurrentUser = true
        )
    }
} 