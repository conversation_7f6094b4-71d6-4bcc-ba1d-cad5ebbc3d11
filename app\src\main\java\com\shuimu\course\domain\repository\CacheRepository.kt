package com.shuimu.course.domain.repository

import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.CacheStatus
import kotlinx.coroutines.flow.Flow

/**
 * 缓存管理 Repository 接口
 */
interface CacheRepository {
    
    /**
     * 获取指定视频的缓存信息
     */
    fun getCacheInfo(videoId: String): Flow<CachedVideo?>
    
    /**
     * 获取指定视频的缓存信息（同步）
     */
    suspend fun getCacheInfoSync(videoId: String): CachedVideo?
    
    /**
     * 获取所有缓存信息
     */
    fun getAllCacheInfo(): Flow<List<CachedVideo>>
    
    /**
     * 获取指定状态的缓存列表
     */
    fun getCachesByStatus(status: CacheStatus): Flow<List<CachedVideo>>

    /**
     * 获取已完成的缓存列表
     */
    fun getCompletedCaches(): Flow<List<CachedVideo>>

    /**
     * 获取正在下载的缓存列表
     */
    fun getDownloadingCaches(): Flow<List<CachedVideo>>

    /**
     * 获取待下载的缓存列表
     */
    fun getPendingCaches(): Flow<List<CachedVideo>>
    
    /**
     * 获取当前下载任务数量
     */
    suspend fun getDownloadingCount(): Int
    
    /**
     * 创建或更新缓存信息
     */
    suspend fun upsertCacheInfo(cachedVideo: CachedVideo)
    
    /**
     * 更新下载进度
     */
    suspend fun updateDownloadProgress(videoId: String, progress: Int, downloadedBytes: Long, totalBytes: Long)
    
    /**
     * 更新下载状态
     */
    suspend fun updateDownloadStatus(videoId: String, status: CacheStatus, errorMessage: String? = null)

    /**
     * 安全更新下载状态（带状态转换验证）
     */
    suspend fun updateDownloadStatusSafely(videoId: String, newStatus: CacheStatus, errorMessage: String? = null): Result<Unit>
    
    /**
     * 删除缓存信息
     */
    suspend fun deleteCacheInfo(videoId: String)
    
    /**
     * 清理失败的缓存记录
     */
    suspend fun clearFailedCaches()
    
    /**
     * 检查本地文件是否存在
     */
    suspend fun checkLocalFileExists(localPath: String): Boolean
    
    /**
     * 删除本地缓存文件
     */
    suspend fun deleteLocalFile(localPath: String): Boolean
    
    /**
     * 同步缓存状态到服务端
     */
    suspend fun syncCacheStateToServer(videoId: String, isCached: Boolean, localPath: String? = null): Result<Unit>
    
    /**
     * 从服务端获取缓存配置
     */
    suspend fun getCacheConfig(): Result<CacheConfig>


    
    /**
     * 批量同步缓存状态到服务端
     */
    suspend fun batchSyncCacheStates(cachedVideos: List<CachedVideo>): Result<Unit>
    
    /**
     * 清理孤儿下载状态
     * 将所有DOWNLOADING状态但没有对应WorkManager任务的记录重置为FAILED
     */
    suspend fun cleanupOrphanDownloads(): Result<Int>
}

/**
 * 缓存配置
 */
data class CacheConfig(
    val maxConcurrentDownloads: Int,
    val cacheDirectory: String,
    val supportedFormats: List<String>
) 