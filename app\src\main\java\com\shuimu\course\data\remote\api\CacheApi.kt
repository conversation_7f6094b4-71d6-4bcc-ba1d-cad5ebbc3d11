package com.shuimu.course.data.remote.api

import com.shuimu.course.data.remote.dto.cache.CacheConfigDto
import com.shuimu.course.data.remote.dto.cache.CacheStateDto
import com.shuimu.course.data.remote.dto.cache.CacheStateUpdateDto
import com.shuimu.course.data.remote.dto.cache.SyncCacheResponseDto
import retrofit2.Response
import retrofit2.http.*

/**
 * 缓存管理 API 接口
 */
interface CacheApi {
    
    @GET("cache/config")
    suspend fun getCacheConfig(): Response<CacheConfigDto>
    
    @POST("cache/config")
    suspend fun updateCacheConfig(@Body config: Map<String, Int>): Response<Map<String, Any>>
    
    @POST("cache/state")
    suspend fun syncCacheState(@Body cacheUpdate: CacheStateUpdateDto): Response<SyncCacheResponseDto>
    
    @GET("cache/state/{videoId}")
    suspend fun getCacheState(@Path("videoId") videoId: String): Response<CacheStateDto>
    
    @GET("cache/states")
    suspend fun getAllCacheStates(): Response<Map<String, Any>>
    
    @DELETE("cache/state/{videoId}")
    suspend fun removeCacheState(@Path("videoId") videoId: String): Response<Map<String, Any>>
    
    @POST("cache/batch-sync")
    suspend fun batchSyncCacheStates(@Body cacheUpdates: List<CacheStateUpdateDto>): Response<Map<String, Any>>
} 