# PowerShell Profile设置脚本
# 自动配置PowerShell以加载AI-Tools模块

Write-Host "🔧 配置PowerShell Profile..." -ForegroundColor Cyan

# 获取PowerShell Profile路径
$profilePath = $PROFILE

Write-Host "Profile路径: $profilePath" -ForegroundColor Yellow

# 创建Profile目录（如果不存在）
$profileDir = Split-Path $profilePath -Parent
if (!(Test-Path $profileDir)) {
    New-Item -Path $profileDir -ItemType Directory -Force
    Write-Host "✅ 创建Profile目录: $profileDir" -ForegroundColor Green
}

# Profile配置内容
$profileContent = @"
# AI工具自动加载配置
# 由setup-ai-profile.ps1自动生成

# 导入AI-Tools模块
if (Test-Path "D:\Tools\AI-Tools.psm1") {
    try {
        Import-Module "D:\Tools\AI-Tools.psm1" -Force -DisableNameChecking
        Write-Host "✅ AI-Tools模块已加载" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ AI-Tools模块加载失败: $_" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️ AI-Tools.psm1文件不存在" -ForegroundColor Yellow
}

# 设置编码
`$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 显示AI工具可用提示
Write-Host "🤖 AI工具已就绪！可用命令：" -ForegroundColor Cyan
Write-Host "  • claude -p '问题'    # 调用Claude" -ForegroundColor White
Write-Host "  • ai-env             # 环境检测" -ForegroundColor White
Write-Host "  • ai-diagnosis       # 全面诊断" -ForegroundColor White
Write-Host "  • ai-pipe '来源' '目标' # 管道处理" -ForegroundColor White
Write-Host "  • ai-chain '问题' '动作' # 链式处理" -ForegroundColor White
"@

# 检查是否已经配置
if (Test-Path $profilePath) {
    $existingContent = Get-Content $profilePath -Raw -ErrorAction SilentlyContinue
    if ($existingContent -like "*AI-Tools*") {
        Write-Host "⚠️ PowerShell Profile已包含AI-Tools配置" -ForegroundColor Yellow
        $overwrite = Read-Host "是否覆盖现有配置？(y/N)"
        if ($overwrite -ne 'y' -and $overwrite -ne 'Y') {
            Write-Host "❌ 取消配置" -ForegroundColor Red
            return
        }
    }
}

# 写入Profile配置
try {
    $profileContent | Out-File -FilePath $profilePath -Encoding UTF8 -Force
    Write-Host "✅ PowerShell Profile配置完成！" -ForegroundColor Green
    
    Write-Host "`n📋 配置说明：" -ForegroundColor Cyan
    Write-Host "  • Profile位置: $profilePath" -ForegroundColor White
    Write-Host "  • 重启PowerShell后AI工具将自动可用" -ForegroundColor White
    Write-Host "  • 或者运行: . `$PROFILE 立即生效" -ForegroundColor White
    
    Write-Host "`n🚀 立即测试配置..." -ForegroundColor Magenta
    . $PROFILE
    
} catch {
    Write-Host "❌ Profile配置失败: $_" -ForegroundColor Red
}

Write-Host "`n🎉 AI工具配置完成！请重启PowerShell或运行 '. `$PROFILE'" -ForegroundColor Green 