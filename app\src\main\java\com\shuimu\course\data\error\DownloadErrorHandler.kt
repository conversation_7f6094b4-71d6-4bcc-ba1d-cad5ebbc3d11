package com.shuimu.course.data.error

import android.util.Log
import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.CacheRepository
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 下载错误处理器
 * 负责错误分类、重试策略、错误恢复等
 */
@Singleton
class DownloadErrorHandler @Inject constructor(
    private val cacheRepository: CacheRepository
) {
    companion object {
        private const val TAG = "DownloadErrorHandler"
        private const val MAX_RETRY_COUNT = 3
        private const val INITIAL_RETRY_DELAY = 2000L // 2秒
        private const val MAX_RETRY_DELAY = 30000L // 30秒
    }
    
    /**
     * 错误类型枚举
     */
    enum class ErrorType {
        NETWORK_ERROR,      // 网络错误
        SERVER_ERROR,       // 服务器错误
        STORAGE_ERROR,      // 存储错误
        PERMISSION_ERROR,   // 权限错误
        TIMEOUT_ERROR,      // 超时错误
        UNKNOWN_ERROR       // 未知错误
    }
    
    /**
     * 错误信息
     */
    data class DownloadError(
        val type: ErrorType,
        val originalException: Throwable,
        val message: String,
        val isRetryable: Boolean,
        val suggestedAction: String
    )
    
    /**
     * 重试结果
     */
    data class RetryResult(
        val shouldRetry: Boolean,
        val delayMs: Long,
        val newRetryCount: Int,
        val reason: String
    )
    
    /**
     * 分析错误并返回错误信息
     */
    fun analyzeError(exception: Throwable): DownloadError {
        return when (exception) {
            is UnknownHostException -> DownloadError(
                type = ErrorType.NETWORK_ERROR,
                originalException = exception,
                message = "网络连接失败，请检查网络设置",
                isRetryable = true,
                suggestedAction = "检查网络连接后重试"
            )
            
            is ConnectException -> DownloadError(
                type = ErrorType.NETWORK_ERROR,
                originalException = exception,
                message = "无法连接到服务器",
                isRetryable = true,
                suggestedAction = "检查网络连接或稍后重试"
            )
            
            is SocketTimeoutException -> DownloadError(
                type = ErrorType.TIMEOUT_ERROR,
                originalException = exception,
                message = "连接超时",
                isRetryable = true,
                suggestedAction = "网络较慢，建议稍后重试"
            )
            
            is IOException -> {
                when {
                    exception.message?.contains("ENOSPC") == true -> DownloadError(
                        type = ErrorType.STORAGE_ERROR,
                        originalException = exception,
                        message = "存储空间不足",
                        isRetryable = false,
                        suggestedAction = "清理存储空间后重试"
                    )
                    
                    exception.message?.contains("Permission denied") == true -> DownloadError(
                        type = ErrorType.PERMISSION_ERROR,
                        originalException = exception,
                        message = "存储权限不足",
                        isRetryable = false,
                        suggestedAction = "检查应用存储权限"
                    )
                    
                    else -> DownloadError(
                        type = ErrorType.STORAGE_ERROR,
                        originalException = exception,
                        message = "文件操作失败: ${exception.message}",
                        isRetryable = true,
                        suggestedAction = "检查存储空间和权限后重试"
                    )
                }
            }
            
            else -> {
                val message = exception.message ?: "未知错误"
                when {
                    message.contains("HTTP") && (message.contains("5") || message.contains("50")) -> DownloadError(
                        type = ErrorType.SERVER_ERROR,
                        originalException = exception,
                        message = "服务器错误: $message",
                        isRetryable = true,
                        suggestedAction = "服务器暂时不可用，请稍后重试"
                    )
                    
                    message.contains("HTTP") && message.contains("4") -> DownloadError(
                        type = ErrorType.SERVER_ERROR,
                        originalException = exception,
                        message = "请求错误: $message",
                        isRetryable = false,
                        suggestedAction = "文件可能已被删除或链接无效"
                    )
                    
                    else -> DownloadError(
                        type = ErrorType.UNKNOWN_ERROR,
                        originalException = exception,
                        message = message,
                        isRetryable = true,
                        suggestedAction = "请稍后重试，如问题持续请联系客服"
                    )
                }
            }
        }
    }
    
    /**
     * 处理下载错误
     */
    suspend fun handleDownloadError(
        videoId: String,
        exception: Throwable,
        currentRetryCount: Int = 0
    ): Result<RetryResult> {
        return try {
            Log.e(TAG, "处理下载错误: $videoId", exception)
            
            val error = analyzeError(exception)
            Log.d(TAG, "错误分析结果: ${error.type}, ${error.message}")
            
            // 更新缓存记录中的错误信息
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo != null) {
                val updatedCache = cacheInfo.copy(
                    status = CacheStatus.FAILED,
                    errorMessage = error.message,
                    retryCount = currentRetryCount,
                    lastRetryTime = System.currentTimeMillis()
                )
                cacheRepository.upsertCacheInfo(updatedCache)
            }
            
            // 决定是否重试
            val retryResult = shouldRetry(error, currentRetryCount)
            
            if (retryResult.shouldRetry) {
                Log.d(TAG, "将在${retryResult.delayMs}ms后重试 (第${retryResult.newRetryCount}次)")
            } else {
                Log.d(TAG, "不再重试: ${retryResult.reason}")
                // 更新为最终失败状态
                cacheRepository.updateDownloadStatus(videoId, CacheStatus.FAILED, error.message)
            }
            
            Result.success(retryResult)
            
        } catch (e: Exception) {
            Log.e(TAG, "错误处理失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 判断是否应该重试
     */
    private fun shouldRetry(error: DownloadError, currentRetryCount: Int): RetryResult {
        return when {
            !error.isRetryable -> RetryResult(
                shouldRetry = false,
                delayMs = 0L,
                newRetryCount = currentRetryCount,
                reason = "错误类型不可重试: ${error.type}"
            )
            
            currentRetryCount >= MAX_RETRY_COUNT -> RetryResult(
                shouldRetry = false,
                delayMs = 0L,
                newRetryCount = currentRetryCount,
                reason = "已达到最大重试次数"
            )
            
            else -> {
                val delay = calculateRetryDelay(currentRetryCount, error.type)
                RetryResult(
                    shouldRetry = true,
                    delayMs = delay,
                    newRetryCount = currentRetryCount + 1,
                    reason = "可重试错误，使用指数退避策略"
                )
            }
        }
    }
    
    /**
     * 计算重试延迟（指数退避）
     */
    private fun calculateRetryDelay(retryCount: Int, errorType: ErrorType): Long {
        val baseDelay = when (errorType) {
            ErrorType.NETWORK_ERROR -> INITIAL_RETRY_DELAY
            ErrorType.TIMEOUT_ERROR -> INITIAL_RETRY_DELAY * 2 // 超时错误延迟更长
            ErrorType.SERVER_ERROR -> INITIAL_RETRY_DELAY * 3 // 服务器错误延迟最长
            else -> INITIAL_RETRY_DELAY
        }
        
        // 指数退避：delay = baseDelay * 2^retryCount
        val exponentialDelay = baseDelay * (1L shl retryCount)
        
        // 限制最大延迟
        return exponentialDelay.coerceAtMost(MAX_RETRY_DELAY)
    }
    
    /**
     * 执行重试
     */
    suspend fun executeRetry(
        videoId: String,
        retryAction: suspend () -> Result<Unit>
    ): Result<Unit> {
        var cacheInfo: CachedVideo? = null
        return try {
            cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo == null) {
                return Result.failure(Exception("缓存信息不存在"))
            }

            // 检查是否可以重试
            if (!cacheInfo.canRetryNow()) {
                val remainingDelay = cacheInfo.getNextRetryDelay() - (System.currentTimeMillis() - cacheInfo.lastRetryTime)
                return Result.failure(Exception("请等待${remainingDelay / 1000}秒后重试"))
            }

            Log.d(TAG, "执行重试: $videoId (第${cacheInfo.retryCount + 1}次)")

            // 更新重试信息
            val updatedCache = cacheInfo.copy(
                retryCount = cacheInfo.retryCount + 1,
                lastRetryTime = System.currentTimeMillis(),
                status = CacheStatus.DOWNLOADING,
                errorMessage = null
            )
            cacheRepository.upsertCacheInfo(updatedCache)

            // 执行重试动作
            retryAction()

        } catch (e: Exception) {
            Log.e(TAG, "重试执行失败: $videoId", e)
            handleDownloadError(videoId, e, cacheInfo?.retryCount ?: 0)
            Result.failure(e)
        }
    }
    
    /**
     * 批量重试失败的下载
     */
    suspend fun batchRetryFailedDownloads(): Result<BatchRetryResult> {
        return try {
            Log.d(TAG, "开始批量重试失败的下载")
            
            // 获取所有失败的缓存
            val failedCaches = cacheRepository.getCachesByStatus(CacheStatus.FAILED).first()
                .filter { it.canRetryNow() } // 只重试可以重试的
            
            Log.d(TAG, "找到${failedCaches.size}个可重试的失败下载")
            
            var successCount = 0
            var failureCount = 0
            val retryResults = mutableListOf<String>()
            
            for (cache in failedCaches) {
                try {
                    // 重置状态为等待下载
                    cacheRepository.updateDownloadStatus(cache.videoId, CacheStatus.PENDING)
                    retryResults.add("${cache.title}: 已重新加入下载队列")
                    successCount++
                } catch (e: Exception) {
                    Log.e(TAG, "重试失败: ${cache.videoId}", e)
                    retryResults.add("${cache.title}: 重试失败 - ${e.message}")
                    failureCount++
                }
            }
            
            Log.d(TAG, "批量重试完成: 成功${successCount}个, 失败${failureCount}个")
            
            Result.success(BatchRetryResult(successCount, failureCount, retryResults))
            
        } catch (e: Exception) {
            Log.e(TAG, "批量重试失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量重试结果
     */
    data class BatchRetryResult(
        val successCount: Int,
        val failureCount: Int,
        val details: List<String>
    )
    
    /**
     * 获取错误统计信息
     */
    suspend fun getErrorStatistics(): ErrorStatistics {
        return try {
            val allCaches = cacheRepository.getAllCacheInfo().first()
            
            val errorCounts = mutableMapOf<ErrorType, Int>()
            val totalErrors = allCaches.count { it.status == CacheStatus.FAILED }
            val retryableErrors = allCaches.count { it.status == CacheStatus.FAILED && it.needsRetry() }
            
            // 分析错误类型分布
            allCaches.filter { it.status == CacheStatus.FAILED }
                .forEach { cache ->
                    cache.errorMessage?.let { message ->
                        val errorType = when {
                            message.contains("网络") || message.contains("连接") -> ErrorType.NETWORK_ERROR
                            message.contains("超时") -> ErrorType.TIMEOUT_ERROR
                            message.contains("存储") || message.contains("空间") -> ErrorType.STORAGE_ERROR
                            message.contains("权限") -> ErrorType.PERMISSION_ERROR
                            message.contains("服务器") -> ErrorType.SERVER_ERROR
                            else -> ErrorType.UNKNOWN_ERROR
                        }
                        errorCounts[errorType] = errorCounts.getOrDefault(errorType, 0) + 1
                    }
                }
            
            ErrorStatistics(
                totalErrors = totalErrors,
                retryableErrors = retryableErrors,
                errorTypeDistribution = errorCounts
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "获取错误统计失败", e)
            ErrorStatistics(0, 0, emptyMap())
        }
    }
    
    /**
     * 错误统计信息
     */
    data class ErrorStatistics(
        val totalErrors: Int,
        val retryableErrors: Int,
        val errorTypeDistribution: Map<ErrorType, Int>
    )
}
