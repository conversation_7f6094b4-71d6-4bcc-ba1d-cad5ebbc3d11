package com.shuimu.course.data.repository

import com.shuimu.course.data.remote.api.PaymentApi
import com.shuimu.course.data.remote.api.PaymentCreateRequest
import com.shuimu.course.domain.repository.PaymentRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import retrofit2.HttpException
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PaymentRepositoryImpl @Inject constructor(
    private val paymentApi: PaymentApi
) : PaymentRepository {
    override suspend fun createPayment(itemId: String, itemType: String, amount: Float): Flow<Resource<String>> = flow {
        emit(Resource.Loading())
        try {
            val request = PaymentCreateRequest(itemId, itemType, amount, "alipay")
            val response = paymentApi.createPayment(request)
            if (response.isSuccessful && response.body()?.status == "completed") {
                emit(Resource.Success("Payment successful: ${response.body()?.orderId}"))
            } else {
                emit(Resource.Error(response.body()?.message ?: "Payment failed"))
            }
        } catch (e: IOException) {
            emit(Resource.Error("Couldn't reach server. Check your internet connection."))
        } catch (e: HttpException) {
            emit(Resource.Error("An unexpected error occurred."))
        }
    }
} 