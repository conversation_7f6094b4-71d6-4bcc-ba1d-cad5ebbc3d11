#!/usr/bin/node

const fs = require('fs');
const path = require('path');
const https = require('https');
const os = require('os');

// 配置文件路径
const configDir = path.join(os.homedir(), '.claude');
const configFile = path.join(configDir, 'config.json');

// 默认配置
const defaultConfig = {
    api_key: '',
    default_model: 'claude-3-sonnet-20240229',
    default_temperature: 0.7,
    default_max_tokens: 1000,
    proxy: '',
    base_url: 'https://api.anthropic.com'
};

// 读取配置文件
function loadConfig() {
    try {
        if (fs.existsSync(configFile)) {
            const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
            return { ...defaultConfig, ...config };
        }
    } catch (error) {
        console.error('Warning: Error reading config file:', error.message);
    }
    return defaultConfig;
}

// 保存配置文件
function saveConfig(config) {
    try {
        if (!fs.existsSync(configDir)) {
            fs.mkdirSync(configDir, { recursive: true });
        }
        fs.writeFileSync(configFile, JSON.stringify(config, null, 2));
        console.log('Configuration saved to:', configFile);
    } catch (error) {
        console.error('Error saving config:', error.message);
    }
}

// 发送API请求
function callClaudeAPI(prompt, options = {}) {
    return new Promise((resolve, reject) => {
        const config = loadConfig();
        
        if (!config.api_key) {
            reject(new Error('API key not configured. Use: claude --config api_key YOUR_KEY'));
            return;
        }

        const data = JSON.stringify({
            model: options.model || config.default_model,
            max_tokens: options.max_tokens || config.default_max_tokens,
            temperature: options.temperature || config.default_temperature,
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ]
        });

        const requestOptions = {
            hostname: 'api.anthropic.com',
            port: 443,
            path: '/v1/messages',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': data.length,
                'x-api-key': config.api_key,
                'anthropic-version': '2023-06-01'
            }
        };

        const req = https.request(requestOptions, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(responseData);
                    if (response.content && response.content[0]) {
                        resolve(response.content[0].text);
                    } else if (response.error) {
                        reject(new Error(response.error.message || 'API Error'));
                    } else {
                        reject(new Error('Unexpected response format'));
                    }
                } catch (error) {
                    reject(new Error('Failed to parse response: ' + error.message));
                }
            });
        });

        req.on('error', (error) => {
            reject(new Error('Request failed: ' + error.message));
        });

        req.write(data);
        req.end();
    });
}

// 从stdin读取输入
function readStdin() {
    return new Promise((resolve) => {
        let input = '';
        
        if (process.stdin.isTTY) {
            resolve('');
            return;
        }
        
        process.stdin.setEncoding('utf8');
        
        process.stdin.on('readable', () => {
            let chunk;
            while ((chunk = process.stdin.read()) !== null) {
                input += chunk;
            }
        });
        
        process.stdin.on('end', () => {
            resolve(input.trim());
        });
    });
}

// 解析命令行参数
function parseArgs(args) {
    const options = {
        prompt: '',
        model: null,
        temperature: null,
        max_tokens: null,
        system: null,
        help: false,
        version: false,
        config: null
    };
    
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        
        switch (arg) {
            case '--help':
            case '-h':
                options.help = true;
                break;
            case '--version':
            case '-v':
                options.version = true;
                break;
            case '-p':
            case '--prompt':
                if (i + 1 < args.length) {
                    options.prompt = args[++i];
                }
                break;
            case '--model':
                if (i + 1 < args.length) {
                    options.model = args[++i];
                }
                break;
            case '--temperature':
                if (i + 1 < args.length) {
                    options.temperature = parseFloat(args[++i]);
                }
                break;
            case '--max-tokens':
                if (i + 1 < args.length) {
                    options.max_tokens = parseInt(args[++i]);
                }
                break;
            case '--system':
                if (i + 1 < args.length) {
                    options.system = args[++i];
                }
                break;
            case '--config':
                if (i + 1 < args.length) {
                    options.config = args[++i];
                }
                break;
            default:
                if (!arg.startsWith('-') && !options.prompt) {
                    options.prompt = arg;
                }
        }
    }
    
    return options;
}

// 显示帮助信息
function showHelp() {
    console.log(`Claude CLI v2.0.0 - Enhanced Version

Usage: claude [options] [prompt]

Options:
  -p, --prompt <text>      Prompt text to send to Claude
  --model <model>          Model to use (default: claude-3-sonnet-20240229)
  --temperature <num>      Temperature (0-1, default: 0.7)
  --max-tokens <num>       Maximum tokens (default: 1000)
  --system <text>          System message
  --config <key> <value>   Set configuration value
  -h, --help              Show this help message
  -v, --version           Show version

Examples:
  claude "Explain quantum physics"
  claude -p "Write a poem" --temperature 0.9
  echo "Hello world" | claude -p "Translate to French"
  claude --config api_key sk-your-key-here
  
Configuration:
  Config file: ${configFile}
  
Available models:
  - claude-3-opus-20240229
  - claude-3-sonnet-20240229
  - claude-3-haiku-20240307`);
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    const options = parseArgs(args);
    
    // 处理版本显示
    if (options.version) {
        console.log('Claude CLI v2.0.0 - Enhanced Version');
        return;
    }
    
    // 处理帮助显示
    if (options.help) {
        showHelp();
        return;
    }
    
    // 处理配置设置
    if (options.config) {
        const config = loadConfig();
        const key = options.config;
        const value = args[args.indexOf('--config') + 2];
        
        if (!value) {
            console.log(`Current ${key}:`, config[key] || 'not set');
            return;
        }
        
        config[key] = value;
        saveConfig(config);
        return;
    }
    
    // 读取stdin输入（管道支持）
    const stdinInput = await readStdin();
    
    // 确定最终的prompt
    let finalPrompt = options.prompt || stdinInput;
    
    if (!finalPrompt) {
        console.log('Claude CLI v2.0.0 - Enhanced Version');
        console.log('Usage: claude [options] [prompt]');
        console.log('Use --help for detailed usage information');
        return;
    }
    
    // 如果有系统消息，添加到prompt前面
    if (options.system) {
        finalPrompt = `System: ${options.system}\n\nUser: ${finalPrompt}`;
    }
    
    // 如果有stdin输入且有prompt参数，组合它们
    if (stdinInput && options.prompt) {
        finalPrompt = `${options.prompt}\n\nInput:\n${stdinInput}`;
    }
    
    try {
        console.log('🤖 Claude is thinking...\n');
        
        const response = await callClaudeAPI(finalPrompt, {
            model: options.model,
            temperature: options.temperature,
            max_tokens: options.max_tokens
        });
        
        console.log(response);
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        
        if (error.message.includes('API key')) {
            console.log('\n💡 To configure your API key:');
            console.log('claude --config api_key YOUR_ANTHROPIC_API_KEY');
        }
        
        process.exit(1);
    }
}

// 运行主函数
main().catch(console.error); 