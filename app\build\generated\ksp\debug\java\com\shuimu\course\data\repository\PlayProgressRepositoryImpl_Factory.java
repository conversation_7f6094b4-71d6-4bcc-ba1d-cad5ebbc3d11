package com.shuimu.course.data.repository;

import com.shuimu.course.data.local.dao.PlayProgressDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class PlayProgressRepositoryImpl_Factory implements Factory<PlayProgressRepositoryImpl> {
  private final Provider<PlayProgressDao> daoProvider;

  public PlayProgressRepositoryImpl_Factory(Provider<PlayProgressDao> daoProvider) {
    this.daoProvider = daoProvider;
  }

  @Override
  public PlayProgressRepositoryImpl get() {
    return newInstance(daoProvider.get());
  }

  public static PlayProgressRepositoryImpl_Factory create(Provider<PlayProgressDao> daoProvider) {
    return new PlayProgressRepositoryImpl_Factory(daoProvider);
  }

  public static PlayProgressRepositoryImpl newInstance(PlayProgressDao dao) {
    return new PlayProgressRepositoryImpl(dao);
  }
}
