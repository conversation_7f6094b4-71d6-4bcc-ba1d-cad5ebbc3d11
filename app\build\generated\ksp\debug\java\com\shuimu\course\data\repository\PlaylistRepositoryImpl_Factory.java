package com.shuimu.course.data.repository;

import com.shuimu.course.data.remote.api.PlaylistApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class PlaylistRepositoryImpl_Factory implements Factory<PlaylistRepositoryImpl> {
  private final Provider<PlaylistApi> playlistApiProvider;

  public PlaylistRepositoryImpl_Factory(Provider<PlaylistApi> playlistApiProvider) {
    this.playlistApiProvider = playlistApiProvider;
  }

  @Override
  public PlaylistRepositoryImpl get() {
    return newInstance(playlistApiProvider.get());
  }

  public static PlaylistRepositoryImpl_Factory create(Provider<PlaylistApi> playlistApiProvider) {
    return new PlaylistRepositoryImpl_Factory(playlistApiProvider);
  }

  public static PlaylistRepositoryImpl newInstance(PlaylistApi playlistApi) {
    return new PlaylistRepositoryImpl(playlistApi);
  }
}
