# Claude CLI PATH Setup Script
# This script adds D:\01-shuimu_01 to your system PATH permanently

Write-Host "Setting up Claude CLI PATH..." -ForegroundColor Green

# Get current user PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

# Check if our directory is already in PATH
$claudeDir = "D:\01-shuimu_01"
if ($currentPath -notlike "*$claudeDir*") {
    # Add our directory to PATH
    $newPath = $currentPath + ";" + $claudeDir
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    Write-Host "✅ Added $claudeDir to user PATH" -ForegroundColor Green
    Write-Host "✅ You can now use 'claude' command from any directory!" -ForegroundColor Green
    Write-Host "⚠️  Please restart your terminal for changes to take effect" -ForegroundColor Yellow
} else {
    Write-Host "✅ Claude CLI directory already in PATH" -ForegroundColor Green
}

Write-Host "`nTesting claude command..." -ForegroundColor Cyan
try {
    & "D:\01-shuimu_01\claude.bat" -p "Hello from setup script!"
    Write-Host "✅ Claude CLI is working correctly!" -ForegroundColor Green
} catch {
    Write-Host "❌ Error testing claude command: $_" -ForegroundColor Red
}

Write-Host "`n🎉 Setup complete! You can now use:" -ForegroundColor Green
Write-Host "   claude -p 'your question'" -ForegroundColor White
Write-Host "   claude --help" -ForegroundColor White
Write-Host "   echo 'text' | claude -p" -ForegroundColor White 