package com.shuimu.course.presentation.ui.components.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun PurchaseModal(
    onDismissRequest: () -> Unit,
    onConfirm: () -> Unit,
    itemName: String,
    itemContents: List<String>,
    price: String
) {
    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = {
            Text(text = "确认购买")
        },
        text = {
            Column {
                Text(
                    text = itemName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(16.dp))
                itemContents.forEach { content ->
                    Text(
                        text = "• $content",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(bottom = 4.dp)
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "价格: ¥$price",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm
            ) {
                Text("确认支付")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismissRequest
            ) {
                Text("取消")
            }
        }
    )
}

@Preview
@Composable
fun PurchaseModalPreview() {
    ShuimuCourseTheme {
        PurchaseModal(
            onDismissRequest = {},
            onConfirm = {},
            itemName = "道：恋爱宝典系列 (全套)",
            itemContents = listOf("恋爱宝典1", "恋爱宝典2", "恋爱宝典3"),
            price = "600.00"
        )
    }
} 