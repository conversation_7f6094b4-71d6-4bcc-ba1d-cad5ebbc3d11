package com.shuimu.course.data.remote.dto.cache

import com.google.gson.annotations.SerializedName

/**
 * 缓存状态 DTO
 */
data class CacheStateDto(
    @SerializedName("videoId")
    val videoId: String,
    
    @SerializedName("isCached")
    val isCached: Bo<PERSON>an,
    
    @SerializedName("localPath")
    val localPath: String?,
    
    @SerializedName("size")
    val size: Long? = null,
    
    @SerializedName("updatedAt")
    val updatedAt: String? = null
)

/**
 * 缓存状态更新 DTO
 */
data class CacheStateUpdateDto(
    @SerializedName("videoId")
    val videoId: String,
    
    @SerializedName("isCached")
    val isCached: Boolean,
    
    @SerializedName("localPath")
    val localPath: String? = null,
    
    @SerializedName("deviceId")
    val deviceId: String? = null
)

/**
 * 同步缓存响应 DTO
 */
data class SyncCacheResponseDto(
    @SerializedName("message")
    val message: String,
    
    @SerializedName("videoId")
    val videoId: String,
    
    @SerializedName("isCached")
    val isCached: Boolean,
    
    @SerializedName("syncedAt")
    val syncedAt: String
) 