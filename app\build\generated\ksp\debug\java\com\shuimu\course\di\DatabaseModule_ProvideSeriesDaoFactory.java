package com.shuimu.course.di;

import com.shuimu.course.data.local.dao.SeriesDao;
import com.shuimu.course.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvideSeriesDaoFactory implements Factory<SeriesDao> {
  private final Provider<AppDatabase> dbProvider;

  public DatabaseModule_ProvideSeriesDaoFactory(Provider<AppDatabase> dbProvider) {
    this.dbProvider = dbProvider;
  }

  @Override
  public SeriesDao get() {
    return provideSeriesDao(dbProvider.get());
  }

  public static DatabaseModule_ProvideSeriesDaoFactory create(Provider<AppDatabase> dbProvider) {
    return new DatabaseModule_ProvideSeriesDaoFactory(dbProvider);
  }

  public static SeriesDao provideSeriesDao(AppDatabase db) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideSeriesDao(db));
  }
}
