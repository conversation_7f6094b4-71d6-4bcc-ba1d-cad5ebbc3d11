# Shuimu Mock API Server Restart Script
# For Development and Cloudflare Deployment

param(
    [switch]$Development = $true,
    [switch]$Production = $false,
    [string]$Port = "8000",
    [string]$ServerHost = "0.0.0.0"
)

Write-Host "=== Shuimu Mock API Server Restart ===" -ForegroundColor Cyan
Write-Host "Development Mode: $Development | Production Mode: $Production" -ForegroundColor White
Write-Host ""

# 1. Stop existing processes
Write-Host "Stopping existing servers..." -ForegroundColor Yellow
try {
    Get-Process -Name python -ErrorAction SilentlyContinue | Stop-Process -Force
    Get-Process -Name cloudflared -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "Successfully stopped existing processes" -ForegroundColor Green
} catch {
    Write-Host "No running processes found" -ForegroundColor DarkYellow
}

Start-Sleep -Seconds 2

# 2. Navigate to mock server directory
Write-Host "Navigating to mock server directory..." -ForegroundColor Blue
Set-Location "mock_server"

# 3. Check dependencies
Write-Host "Checking Python dependencies..." -ForegroundColor Blue
if (-not (Test-Path "requirements.txt")) {
    Write-Host "requirements.txt not found" -ForegroundColor Red
    exit 1
}

# 4. Start server based on mode
if ($Development) {
    Write-Host "Starting development server..." -ForegroundColor Green
    $serverArgs = "-m uvicorn src.main:app --host $ServerHost --port $Port --reload --log-level info"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "python $serverArgs" -WindowStyle Normal
} else {
    Write-Host "Starting production server..." -ForegroundColor Green
    $serverArgs = "-m uvicorn src.main:app --host $ServerHost --port $Port --workers 4"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "python $serverArgs" -WindowStyle Minimized
}

# 5. Wait for server startup
Write-Host "Waiting for server startup..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

# 6. Health check
Write-Host "Performing health check..." -ForegroundColor Blue
$maxRetries = 10
$retryCount = 0
$serverReady = $false

while ($retryCount -lt $maxRetries -and -not $serverReady) {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:$Port/" -Method GET -TimeoutSec 5
        if ($response.message -like "*Welcome*") {
            $serverReady = $true
            Write-Host "Mock server health check passed" -ForegroundColor Green
        }
    } catch {
        $retryCount++
        Write-Host "Retry $retryCount/$maxRetries..." -ForegroundColor DarkYellow
        Start-Sleep -Seconds 2
    }
}

if (-not $serverReady) {
    Write-Host "Mock server startup failed" -ForegroundColor Red
    exit 1
}

# 7. Start Cloudflare tunnel
Write-Host "Starting Cloudflare tunnel..." -ForegroundColor Green
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cloudflared tunnel --url http://localhost:$Port" -WindowStyle Minimized

# 8. Wait for tunnel establishment
Write-Host "Waiting for Cloudflare tunnel..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 9. Test APIs
Write-Host "Testing Cache APIs..." -ForegroundColor Magenta
$testResults = @()

# Test 1: Basic connectivity
try {
    $response = Invoke-RestMethod -Uri "https://api.shuimu.us.kg/api/" -Method GET -TimeoutSec 10
    $testResults += "✓ Basic API connectivity"
} catch {
    $testResults += "✗ Basic API connectivity failed"
}

# Test 2: Cache config API
try {
    $response = Invoke-RestMethod -Uri "https://api.shuimu.us.kg/api/cache/config" -Method GET -TimeoutSec 10
    $testResults += "✓ Cache config API"
} catch {
    $testResults += "✗ Cache config API failed"
}

# Test 3: User data API
try {
    $response = Invoke-RestMethod -Uri "https://api.shuimu.us.kg/api/user/data" -Method GET -TimeoutSec 10
    $testResults += "✓ User data API"
} catch {
    $testResults += "✗ User data API failed"
}

# 10. Display results
Write-Host ""
Write-Host "=== Restart Complete ===" -ForegroundColor Cyan
Write-Host "Local URL: http://localhost:$Port" -ForegroundColor White
Write-Host "Public URL: https://api.shuimu.us.kg" -ForegroundColor White
Write-Host "API Docs: https://api.shuimu.us.kg/docs" -ForegroundColor White
Write-Host ""

Write-Host "API Test Results:" -ForegroundColor Yellow
foreach ($result in $testResults) {
    Write-Host "   $result" -ForegroundColor White
}

Write-Host ""
Write-Host "Tips:" -ForegroundColor Cyan
Write-Host "   - Development mode supports hot reload" -ForegroundColor Gray
Write-Host "   - Code changes will auto-restart server" -ForegroundColor Gray
Write-Host "   - Use Ctrl+C to stop servers" -ForegroundColor Gray
Write-Host ""

Write-Host "Server restart complete!" -ForegroundColor Green 