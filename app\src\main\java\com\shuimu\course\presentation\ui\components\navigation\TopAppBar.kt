package com.shuimu.course.presentation.ui.components.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import androidx.compose.foundation.layout.RowScope

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShuimuTopAppBar(
    modifier: Modifier = Modifier,
    title: String,
    onNavigateUp: (() -> Unit)? = null,
    actions: @Composable RowScope.() -> Unit = {}
) {
    TopAppBar(
        title = { Text(text = title) },
        modifier = modifier,
        navigationIcon = {
            if (onNavigateUp != null) {
                IconButton(onClick = onNavigateUp) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Navigate back"
                    )
                }
            }
        },
        actions = actions,
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.primary,
            titleContentColor = MaterialTheme.colorScheme.onPrimary,
            navigationIconContentColor = MaterialTheme.colorScheme.onPrimary,
            actionIconContentColor = MaterialTheme.colorScheme.onPrimary
        )
    )
}

@Preview(name = "Top App Bar with Navigation")
@Composable
fun ShuimuTopAppBarWithNavPreview() {
    ShuimuCourseTheme {
        ShuimuTopAppBar(
            title = "课程详情",
            onNavigateUp = {},
            actions = {
                IconButton(onClick = { /* do something */ }) {
                    Icon(
                        imageVector = Icons.Default.Share,
                        contentDescription = "Share"
                    )
                }
            }
        )
    }
}

@Preview(name = "Top App Bar without Navigation")
@Composable
fun ShuimuTopAppBarWithoutNavPreview() {
    ShuimuCourseTheme {
        ShuimuTopAppBar(
            title = "首页"
        )
    }
} 