package com.shuimu.course.presentation.ui.components.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ShareModal(
    onDismissRequest: () -> Unit,
) {
    // In a real app, this would use ModalBottomSheet from Material3
    // For this component, we'll lay out the content of a potential bottom sheet.
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = "分享推广计划",
            style = MaterialTheme.typography.titleLarge,
            modifier = Modifier
                .padding(bottom = 8.dp)
                .align(Alignment.CenterHorizontally)
        )
        Text(
            text = "分享成功购买可获得30%收益",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier
                .padding(bottom = 16.dp)
                .align(Alignment.CenterHorizontally)
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            ShareOption(icon = Icons.Default.Share, label = "微信好友", onClick = { /* TODO */ })
            ShareOption(icon = Icons.Default.Share, label = "朋友圈", onClick = { /* TODO */ })
            ShareOption(icon = Icons.Default.ContentCopy, label = "复制链接", onClick = { /* TODO */ })
        }
        Spacer(modifier = Modifier.height(16.dp))
        Button(
            onClick = onDismissRequest,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("取消")
        }
    }
}

@Composable
private fun ShareOption(
    icon: ImageVector,
    label: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.clickable(onClick = onClick)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(48.dp)
        )
        Text(text = label, style = MaterialTheme.typography.bodySmall)
    }
}

@Preview
@Composable
fun ShareModalPreview() {
    ShuimuCourseTheme {
        Surface {
            ShareModal(onDismissRequest = {})
        }
    }
} 