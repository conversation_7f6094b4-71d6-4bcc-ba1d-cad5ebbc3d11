package com.shuimu.course.data.remote.api

import com.shuimu.course.data.remote.dto.VideoDto
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.PUT
import retrofit2.http.Path

interface VideoApi {
    @GET("categories/{id}/videos")
    suspend fun getVideosForCategory(@Path("id") categoryId: String): Response<List<VideoDto>>

    @GET("videos/{id}")
    suspend fun getVideoDetails(@Path("id") videoId: String): Response<VideoDto>

    @PUT("videos/{id}/progress")
    suspend fun updateVideoProgress(@Path("id") videoId: String, @Body progress: Int): Response<Unit>

    @PUT("videos/{id}/watch-count")
    suspend fun updateWatchCount(@Path("id") videoId: String): Response<Unit>

    @PUT("videos/{id}/cache-status")
    suspend fun updateVideoCacheStatus(@Path("id") videoId: String, @Body isCached: Boolean): Response<Unit>
}
