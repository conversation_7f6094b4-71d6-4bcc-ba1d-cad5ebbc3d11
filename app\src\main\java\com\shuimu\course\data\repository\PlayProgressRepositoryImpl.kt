package com.shuimu.course.data.repository

import com.shuimu.course.data.local.dao.PlayProgressDao
import com.shuimu.course.data.local.entities.PlayProgressEntity
import com.shuimu.course.domain.model.PlayProgress
import com.shuimu.course.domain.repository.PlayProgressRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PlayProgressRepositoryImpl @Inject constructor(
    private val dao: PlayProgressDao
) : PlayProgressRepository {
    override fun getProgressForVideo(videoId: String): Flow<PlayProgress?> {
        return dao.getProgressForVideo(videoId).map { it?.toDomain() }
    }

    override suspend fun saveProgress(progress: PlayProgress) {
        dao.upsertProgress(progress.toEntity())
    }
}

private fun PlayProgressEntity.toDomain(): PlayProgress = PlayProgress(
    videoId = videoId,
    watchCount = watchCount,
    lastPositionSeconds = lastPositionSeconds,
    totalDurationSeconds = totalDurationSeconds,
    updatedAt = updatedAt
)

private fun PlayProgress.toEntity(): PlayProgressEntity = PlayProgressEntity(
    videoId = videoId,
    watchCount = watchCount,
    lastPositionSeconds = lastPositionSeconds,
    totalDurationSeconds = totalDurationSeconds,
    updatedAt = updatedAt
) 