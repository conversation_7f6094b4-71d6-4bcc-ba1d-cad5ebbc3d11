package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

data class CachedVideoItem(
    val videoId: String,
    val title: String,
    val seriesTitle: String,
    val sizeMb: Double,
    val status: com.shuimu.course.domain.model.CacheStatus,
    val progress: Int,
    val priority: Int,
    val errorMessage: String? = null,
    val retryCount: Int = 0,
    val canRetry: Boolean = false
)

data class CacheManagerState(
    val isLoading: Boolean = false,
    val groupedVideos: Map<String, List<CachedVideoItem>> = emptyMap(),
    val totalSizeMb: Double = 0.0
    // 暂时注释掉这些字段，因为依赖的类还有编译问题
    // val storageInfo: com.shuimu.course.data.storage.StorageManager.StorageInfo? = null,
    // val errorStatistics: com.shuimu.course.data.error.DownloadErrorHandler.ErrorStatistics? = null,
    // val showRetryDialog: Boolean = false,
    // val selectedVideoForRetry: String? = null
)

@HiltViewModel
class CacheManagerViewModel @Inject constructor(
    private val cacheRepository: CacheRepository,
    private val seriesRepository: SeriesRepository // Inject series repo to get titles
    // 暂时注释掉以解决编译问题
    // private val downloadManager: com.shuimu.course.data.workers.DownloadManager
) : ViewModel() {

    private val _state = MutableStateFlow(CacheManagerState())
    val state = _state.asStateFlow()

    init {
        loadCacheInfo()
        // 暂时注释掉以解决编译问题
        // loadStorageInfo()
        // loadErrorStatistics()
    }

    private fun loadCacheInfo() {
        // This combines the cache info with the series info to allow grouping
        combine(
            cacheRepository.getAllCacheInfo(),
            seriesRepository.getSeries()
        ) { cacheList, seriesResource ->
            _state.value = _state.value.copy(isLoading = true)
            if (seriesResource is Resource.Success) {
                val cacheMap = cacheList.associateBy { it.videoId }
                
                val allVideos = seriesResource.data?.flatMap { series ->
                    series.categories.flatMap { category ->
                        category.videos.map { video ->
                            Triple(video, series.title, category.title)
                        }
                    }
                } ?: emptyList()

                val cachedVideos = allVideos
                    .filter { (video, _, _) -> cacheMap.containsKey(video.id) }
                    .map { (video, seriesTitle, _) ->
                        val cacheInfo = cacheMap[video.id]!!
                        CachedVideoItem(
                            videoId = video.id,
                            title = video.title,
                            seriesTitle = seriesTitle,
                            sizeMb = cacheInfo.totalBytes / 1_000_000.0,
                            status = cacheInfo.status,
                            progress = cacheInfo.progress,
                            priority = cacheInfo.priority,
                            errorMessage = cacheInfo.errorMessage,
                            retryCount = cacheInfo.retryCount,
                            canRetry = cacheInfo.needsRetry()
                        )
                    }

                _state.value = _state.value.copy(
                    isLoading = false,
                    groupedVideos = cachedVideos.groupBy { it.seriesTitle },
                    totalSizeMb = cachedVideos.sumOf { it.sizeMb }
                )
            }
        }.launchIn(viewModelScope)
    }

    fun deleteVideoCache(videoId: String) {
        viewModelScope.launch {
            cacheRepository.deleteCacheInfo(videoId)
        }
    }

    fun deleteAllCache() {
        viewModelScope.launch {
            state.value.groupedVideos.values.flatten().forEach { item ->
                cacheRepository.deleteCacheInfo(item.videoId)
            }
        }
    }

    // 暂时注释掉这些方法，因为依赖的类还有编译问题
    /*
    private fun loadStorageInfo() {
        viewModelScope.launch {
            try {
                val storageInfo = downloadManager.getStorageInfo()
                _state.value = _state.value.copy(storageInfo = storageInfo)
            } catch (e: Exception) {
                android.util.Log.e("CacheManagerViewModel", "加载存储信息失败", e)
            }
        }
    }

    private fun loadErrorStatistics() {
        viewModelScope.launch {
            try {
                val errorStats = downloadManager.getErrorStatistics()
                _state.value = _state.value.copy(errorStatistics = errorStats)
            } catch (e: Exception) {
                android.util.Log.e("CacheManagerViewModel", "加载错误统计失败", e)
            }
        }
    }
    */

    /**
     * 设置下载优先级（暂时不支持）
     */
    fun setDownloadPriority(videoId: String, priority: Int) {
        // 暂时不支持
    }

    /**
     * 重试失败的下载（暂时不支持）
     */
    fun retryFailedDownload(videoId: String) {
        // 暂时不支持
    }

    /**
     * 批量重试所有失败的下载（暂时不支持）
     */
    fun batchRetryFailedDownloads() {
        // 暂时不支持
    }

    /**
     * 清理缓存（暂时不支持）
     */
    fun cleanupCache() {
        // 暂时不支持
    }

    /**
     * 显示重试对话框
     */
    fun showRetryDialog(videoId: String) {
        _state.value = _state.value.copy(
            showRetryDialog = true,
            selectedVideoForRetry = videoId
        )
    }

    /**
     * 隐藏重试对话框
     */
    fun hideRetryDialog() {
        _state.value = _state.value.copy(
            showRetryDialog = false,
            selectedVideoForRetry = null
        )
    }
} 