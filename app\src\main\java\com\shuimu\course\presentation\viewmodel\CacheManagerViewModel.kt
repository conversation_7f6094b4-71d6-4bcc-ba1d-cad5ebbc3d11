package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

data class CachedVideoItem(
    val videoId: String,
    val title: String,
    val seriesTitle: String,
    val sizeMb: Double
)

data class CacheManagerState(
    val isLoading: Boolean = false,
    val groupedVideos: Map<String, List<CachedVideoItem>> = emptyMap(),
    val totalSizeMb: Double = 0.0
)

@HiltViewModel
class CacheManagerViewModel @Inject constructor(
    private val cacheRepository: CacheRepository,
    private val seriesRepository: SeriesRepository // Inject series repo to get titles
) : ViewModel() {

    private val _state = MutableStateFlow(CacheManagerState())
    val state = _state.asStateFlow()

    init {
        loadCacheInfo()
    }

    private fun loadCacheInfo() {
        // This combines the cache info with the series info to allow grouping
        combine(
            cacheRepository.getAllCacheInfo(),
            seriesRepository.getSeries()
        ) { cacheList, seriesResource ->
            _state.value = _state.value.copy(isLoading = true)
            if (seriesResource is Resource.Success) {
                val cacheMap = cacheList.associateBy { it.videoId }
                
                val allVideos = seriesResource.data?.flatMap { series ->
                    series.categories.flatMap { category ->
                        category.videos.map { video ->
                            Triple(video, series.title, category.title)
                        }
                    }
                } ?: emptyList()

                val cachedVideos = allVideos
                    .filter { (video, _, _) -> cacheMap.containsKey(video.id) }
                    .map { (video, seriesTitle, _) ->
                        CachedVideoItem(
                            videoId = video.id,
                            title = video.title,
                            seriesTitle = seriesTitle,
                            sizeMb = (cacheMap[video.id]?.totalBytes ?: 0) / 1_000_000.0
                        )
                    }

                _state.value = _state.value.copy(
                    isLoading = false,
                    groupedVideos = cachedVideos.groupBy { it.seriesTitle },
                    totalSizeMb = cachedVideos.sumOf { it.sizeMb }
                )
            }
        }.launchIn(viewModelScope)
    }

    fun deleteVideoCache(videoId: String) {
        viewModelScope.launch {
            cacheRepository.deleteCacheInfo(videoId)
        }
    }

    fun deleteAllCache() {
        viewModelScope.launch {
            state.value.groupedVideos.values.flatten().forEach { item ->
                cacheRepository.deleteCacheInfo(item.videoId)
            }
        }
    }
} 