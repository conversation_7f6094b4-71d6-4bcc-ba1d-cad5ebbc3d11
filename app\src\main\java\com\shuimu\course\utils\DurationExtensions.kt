package com.shuimu.course.utils

/**
 * 将时长字符串（如"15:48"）转换为总秒数
 */
fun String.toSecondsInt(): Int {
    return try {
        val parts = this.split(":")
        when (parts.size) {
            2 -> {
                // MM:SS 格式
                val minutes = parts[0].toInt()
                val seconds = parts[1].toInt()
                minutes * 60 + seconds
            }
            3 -> {
                // HH:MM:SS 格式
                val hours = parts[0].toInt()
                val minutes = parts[1].toInt()
                val seconds = parts[2].toInt()
                hours * 3600 + minutes * 60 + seconds
            }
            else -> 0
        }
    } catch (e: Exception) {
        0
    }
}

/**
 * 将秒数转换为时长字符串格式
 */
fun Int.toTimeString(): String {
    val hours = this / 3600
    val minutes = (this % 3600) / 60
    val seconds = this % 60
    
    return if (hours > 0) {
        String.format("%d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%d:%02d", minutes, seconds)
    }
} 