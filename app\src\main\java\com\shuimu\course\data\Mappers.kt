package com.shuimu.course.data

import com.shuimu.course.data.local.entities.*
import com.shuimu.course.data.local.relations.*
import com.shuimu.course.data.remote.dto.*
import com.shuimu.course.data.remote.dto.cache.*
import com.shuimu.course.domain.model.*
import com.shuimu.course.domain.repository.CacheConfig

// DTO to Domain
fun CategoryDto.toDomainModel(): Category {
    // 根据购买状态和价格动态生成displayTitle
    val displayTitle = if (!(this.isPurchased ?: false) && !this.isFree && this.price != null && this.price != 0) {
        "${this.title} (¥${this.price / 100})"
    } else {
        this.title
    }
    
    return Category(
        id = this.id,
        title = this.title,
        seriesId = this.seriesId,
        price = this.price,
        isFree = this.isFree,
        isPurchased = this.isPurchased,
        defaultExpanded = this.defaultExpanded,
        videos = this.videos.map { it.toModel() }, // 🔥 直接使用API返回的videos
        displayTitle = displayTitle,
        watchCount = this.watchCount,
        progress = this.progress
    )
}

fun SeriesDto.toDomainModel(): Series {
    return Series(
        id = this.id,
        title = this.title,
        icon = this.icon,
        price = this.price,
        isFree = this.isFree,
        isPurchased = this.isPurchased,
        isPackage = this.isPackage,
        defaultExpanded = this.defaultExpanded,
        categories = this.categories.map { it.toDomainModel() }
    )
}

fun UserDto.toDomainModel(): User = User(id, username, nickname, avatarUrl)

// Entity to Domain
fun VideoEntity.toDomainModel(progress: PlayProgressEntity?, cache: CacheInfoEntity?): Video {
    val durationSeconds = progress?.totalDurationSeconds ?: 1
    val positionSeconds = progress?.lastPositionSeconds ?: 0
    val calculatedProgress = if (durationSeconds > 0) {
        (positionSeconds.toFloat() / durationSeconds.toFloat())
    } else {
        0f
    }

    return Video(
        id = this.id,
        title = this.title,
        duration = this.duration,
        description = this.description,
        categoryId = this.categoryId,
        watchCount = this.watchCount ?: (progress?.watchCount ?: 0),
        playCount = null, // 本地数据库不存储全局播放量
        cloudUrl = this.cloudUrl,
        localPath = this.localPath ?: cache?.localPath,
        cacheStatus = CacheStatus.fromString(this.cacheStatus),
        
        // 🔥 统一进度参数
        progress = calculatedProgress,
        isPurchasable = false // 默认值，真实状态由 Category -> Video 映射时决定
    )
}

fun CategoryWithVideos.toDomainModel(allProgress: List<PlayProgressEntity>, allCaches: List<CacheInfoEntity>): Category {
    return this.category.let { cat ->
        // 🔥 防御点 #1: 权限是所有状态的前置条件
        val isPurchasable = cat.isPurchased ?: false || cat.isFree

        Category(
            id = cat.id,
            title = cat.title,
            seriesId = cat.seriesId,
            price = cat.price,
            isFree = cat.isFree,
            isPurchased = isPurchasable, // 直接使用计算后的权限
            defaultExpanded = cat.defaultExpanded,
            videos = this.videos.map { vid ->
                // 🔥 如果未购买，则强制清除所有本地状态 (进度和缓存)
                val progress = if (isPurchasable) allProgress.find { p -> p.videoId == vid.id } else null
                val cache = if (isPurchasable) allCaches.find { c -> c.videoId == vid.id } else null
                val domainVideo = vid.toDomainModel(progress, cache)
                // 🔥 权限传递: 将分类的购买状态传递给其下的所有视频
                domainVideo.copy(isPurchasable = isPurchasable)
            },
            
            // 🔥 本地数据库映射：使用默认值，真实数据来自服务器
            displayTitle = if (!isPurchasable && cat.price != null && cat.price != 0) {
                "${cat.title} (¥${cat.price / 100})"
            } else {
                cat.title
            },
            watchCount = if (isPurchasable) {
                this.videos.sumOf { vid ->
                    allProgress.find { p -> p.videoId == vid.id }?.watchCount ?: 0
                }
            } else null,
            
            // 🔥 统一进度参数
            progress = if (isPurchasable) {
                val totalVideos = this.videos.size.coerceAtLeast(1)
                (this.videos.sumOf { vid ->
                    val progressEntity = allProgress.find { p -> p.videoId == vid.id }
                    ((progressEntity?.lastPositionSeconds?.toFloat() ?: 0f) / 
                     (progressEntity?.totalDurationSeconds?.toFloat() ?: 1f)).toDouble()
                } / totalVideos).toFloat()
            } else null
        )
    }
}

fun SeriesWithCategories.toDomainModel(allProgress: List<PlayProgressEntity>, allCaches: List<CacheInfoEntity>): Series {
    return this.series.let { ser ->
        Series(
            id = ser.id,
            title = ser.title,
            icon = ser.icon,
            price = ser.price ?: 0,
            isFree = ser.isFree,
            isPurchased = ser.isPurchased ?: false,
            isPackage = ser.isPackage,
            defaultExpanded = ser.defaultExpanded,
            categories = this.categoriesWithVideos.map { catWithVids ->
                catWithVids.toDomainModel(allProgress, allCaches)
            }
        )
    }
}

// Domain to Entity
fun Category.toEntity(): CategoryEntity = CategoryEntity(id, title, seriesId, price, isFree, isPurchased, defaultExpanded)
fun Series.toEntity(): SeriesEntity = SeriesEntity(id, title, icon, price, isFree, isPurchased, isPackage, defaultExpanded)
fun User.toEntity(): UserEntity = UserEntity(id, username, nickname, avatarUrl)

// Alias helper extensions so existing repository code using `.toDomain` continues to work
fun CategoryWithVideos.toDomain(allProgress: List<PlayProgressEntity>, allCaches: List<CacheInfoEntity>): Category =
    this.toDomainModel(allProgress, allCaches)

fun SeriesWithCategories.toDomain(allProgress: List<PlayProgressEntity>, allCaches: List<CacheInfoEntity>): Series =
    this.toDomainModel(allProgress, allCaches)

fun UserEntity.toDomain(): User = User(id, username, nickname, avatarUrl)

// VideoDto to Video (Domain Model)
fun VideoDto.toModel(): Video {
    return Video(
        id = id,
        title = title,
        duration = duration,
        description = description,
        categoryId = categoryId,
        watchCount = watchCount,
        playCount = playCount,
        cloudUrl = cloudUrl,
        localPath = null,
        cacheStatus = when (cacheStatus) {
            "CACHED" -> CacheStatus.CACHED
            "DOWNLOADING" -> CacheStatus.DOWNLOADING
            "NOT_CACHED" -> CacheStatus.NOT_CACHED
            else -> CacheStatus.NOT_CACHED
        },
        progress = progress,
        isPurchasable = false // 默认值，真实状态由 Category -> Video 映射时决定
    )
}

// Video (Domain Model) to VideoEntity
fun Video.toEntity(): VideoEntity {
    return VideoEntity(
        id = id,
        title = title,
        duration = duration,
        description = description,
        categoryId = categoryId,
        watchCount = watchCount,
        cloudUrl = cloudUrl,
        localPath = localPath,
        cacheStatus = cacheStatus.name,
        progress = progress
    )
}

// VideoEntity to Video (Domain Model)
fun VideoEntity.toModel(): Video {
    return Video(
        id = id,
        title = title,
        duration = duration,
        description = description,
        categoryId = categoryId,
        watchCount = watchCount,
        playCount = null, // 本地数据库不存储全局播放量
        cloudUrl = cloudUrl,
        localPath = localPath,
        cacheStatus = when (cacheStatus) {
            "CACHED" -> CacheStatus.CACHED
            "DOWNLOADING" -> CacheStatus.DOWNLOADING
            "NOT_CACHED" -> CacheStatus.NOT_CACHED
            else -> CacheStatus.NOT_CACHED
        },
        progress = progress,
        isPurchasable = false // 默认值，真实状态由 Category -> Video 映射时决定
    )
}

// ======================
// Cache Mapping Extensions
// ======================

fun CacheInfoEntity.toDomain(videoTitle: String = ""): CachedVideo {
    return CachedVideo(
        videoId = videoId,
        title = videoTitle,
        localPath = localPath,
        downloadedBytes = downloadedBytes,
        totalBytes = totalBytes,
        status = when (status) {
            com.shuimu.course.data.local.entities.CacheStatus.NOT_CACHED -> CacheDownloadStatus.NOT_CACHED
            com.shuimu.course.data.local.entities.CacheStatus.WAITING_NETWORK -> CacheDownloadStatus.WAITING_NETWORK
            com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADING -> CacheDownloadStatus.DOWNLOADING
            com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADED -> CacheDownloadStatus.DOWNLOADED
            com.shuimu.course.data.local.entities.CacheStatus.PAUSED -> CacheDownloadStatus.PAUSED
            com.shuimu.course.data.local.entities.CacheStatus.FAILED -> CacheDownloadStatus.FAILED
            com.shuimu.course.data.local.entities.CacheStatus.PENDING -> CacheDownloadStatus.PENDING
        },
        progress = progress,
        cloudUrl = cloudUrl,
        updatedAt = updatedAt,
        errorMessage = errorMessage
    )
}

fun CachedVideo.toEntity(): CacheInfoEntity {
    return CacheInfoEntity(
        videoId = videoId,
        localPath = localPath,
        downloadedBytes = downloadedBytes,
        totalBytes = totalBytes,
        status = when (status) {
            CacheDownloadStatus.NOT_CACHED -> com.shuimu.course.data.local.entities.CacheStatus.NOT_CACHED
            CacheDownloadStatus.WAITING_NETWORK -> com.shuimu.course.data.local.entities.CacheStatus.WAITING_NETWORK
            CacheDownloadStatus.DOWNLOADING -> com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADING
            CacheDownloadStatus.DOWNLOADED -> com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADED
            CacheDownloadStatus.PAUSED -> com.shuimu.course.data.local.entities.CacheStatus.PAUSED
            CacheDownloadStatus.FAILED -> com.shuimu.course.data.local.entities.CacheStatus.FAILED
            CacheDownloadStatus.PENDING -> com.shuimu.course.data.local.entities.CacheStatus.PENDING
        },
        progress = progress,
        cloudUrl = cloudUrl,
        updatedAt = updatedAt,
        errorMessage = errorMessage
    )
}

fun CacheConfigDto.toDomain(): CacheConfig {
    return CacheConfig(
        maxConcurrentDownloads = maxConcurrentDownloads,
        cacheDirectory = cacheDirectory,
        supportedFormats = supportedFormats
    )
}

fun CacheStateDto.toDomain(): CachedVideo {
    return CachedVideo(
        videoId = videoId,
        title = "",
        localPath = localPath,
        downloadedBytes = size ?: 0L,
        totalBytes = size ?: 0L,
        status = if (isCached) CacheDownloadStatus.DOWNLOADED else CacheDownloadStatus.NOT_CACHED,
        progress = if (isCached) 100 else 0,
        cloudUrl = null,
        updatedAt = System.currentTimeMillis(),
        errorMessage = null
    )
}

fun CachedVideo.toCacheStateUpdateDto(): CacheStateUpdateDto {
    return CacheStateUpdateDto(
        videoId = videoId,
        isCached = status == CacheDownloadStatus.DOWNLOADED,
        localPath = if (status == CacheDownloadStatus.DOWNLOADED) localPath else null,
        deviceId = null // 可以从设备信息获取
    )
} 