package com.shuimu.course.domain.usecase.video;

import com.shuimu.course.domain.repository.VideoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DeleteCachedVideoUseCase_Factory implements Factory<DeleteCachedVideoUseCase> {
  private final Provider<VideoRepository> videoRepositoryProvider;

  public DeleteCachedVideoUseCase_Factory(Provider<VideoRepository> videoRepositoryProvider) {
    this.videoRepositoryProvider = videoRepositoryProvider;
  }

  @Override
  public DeleteCachedVideoUseCase get() {
    return newInstance(videoRepositoryProvider.get());
  }

  public static DeleteCachedVideoUseCase_Factory create(
      Provider<VideoRepository> videoRepositoryProvider) {
    return new DeleteCachedVideoUseCase_Factory(videoRepositoryProvider);
  }

  public static DeleteCachedVideoUseCase newInstance(VideoRepository videoRepository) {
    return new DeleteCachedVideoUseCase(videoRepository);
  }
}
