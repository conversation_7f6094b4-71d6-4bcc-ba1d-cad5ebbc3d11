package com.shuimu.course.presentation.ui.components.video

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.utils.formatPlayCount

@Composable
fun VideoInfoPanel(
    modifier: Modifier = Modifier,
    title: String,
    playCount: Long?,
    description: String
) {
    Column(modifier = modifier.padding(16.dp)) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(8.dp))
        Row {
            Text(
                text = "${formatPlayCount(playCount)} 次播放",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            // Other stats like 'likes' or 'shares' could go here
        }
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = description,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Preview
@Composable
fun VideoInfoPanelPreview() {
    ShuimuCourseTheme {
        VideoInfoPanel(
            title = "06. 深度沟通艺术",
            playCount = 52000L,
            description = "深入了解吸引力的本质和运作原理，学会深层次的沟通技巧和艺术，让你在情感交流中游刃有余。"
        )
    }
} 