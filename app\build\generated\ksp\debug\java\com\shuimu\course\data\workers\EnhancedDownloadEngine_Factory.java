package com.shuimu.course.data.workers;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class EnhancedDownloadEngine_Factory implements Factory<EnhancedDownloadEngine> {
  private final Provider<Context> contextProvider;

  public EnhancedDownloadEngine_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public EnhancedDownloadEngine get() {
    return newInstance(contextProvider.get());
  }

  public static EnhancedDownloadEngine_Factory create(Provider<Context> contextProvider) {
    return new EnhancedDownloadEngine_Factory(contextProvider);
  }

  public static EnhancedDownloadEngine newInstance(Context context) {
    return new EnhancedDownloadEngine(context);
  }
}
