import json
from fastapi import APIRouter, HTTPException, Body, Header
from typing import Dict, Any, Optional
from ..models.video import Video
from ..utils.user_data import (
    get_video_watch_progress,
    get_video_cache_status,
    update_video_watch_progress,
    update_video_cache_status
)
from pathlib import Path

router = APIRouter()

# Base directory for JSON data
DATA_DIR = Path(__file__).resolve().parent.parent / "data"

def load_data(file_path):
    with open(DATA_DIR / file_path, "r", encoding="utf-8") as f:
        return json.load(f)

def get_videos_db():
    """动态加载视频数据，确保获取最新内容"""
    return load_data("videos.json")

@router.get("/videos/{video_id}", response_model=Video)
def get_video_details(video_id: str, x_user_id: Optional[str] = Header("user_001")):
    """
    Returns details for a single video with user-specific data.
    """
    videos_db = get_videos_db()
    video_data = next((v for v in videos_db if v["id"] == video_id), None)
    if not video_data:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # 创建副本，添加用户个人化数据
    video_with_user_data = video_data.copy()
    
    if x_user_id:
        # 添加用户个人化字段
        progress_data = get_video_watch_progress(x_user_id, video_id)
        cache_data = get_video_cache_status(x_user_id, video_id)
        
        video_with_user_data["watch_count"] = progress_data.get("watchCount", 0)
        video_with_user_data["progress"] = (
            progress_data.get("position", 0) / progress_data.get("duration", 1) 
            if progress_data.get("duration", 0) > 0 else 0.0
        )
        video_with_user_data["cache_status"] = (
            "CACHED" if cache_data.get("isCached", False) else "NOT_CACHED"
        )
    else:
        # 未登录用户的默认值
        video_with_user_data["watch_count"] = 0
        video_with_user_data["progress"] = 0.0
        video_with_user_data["cache_status"] = "NOT_CACHED"
    
    return Video(**video_with_user_data)

@router.put("/videos/{video_id}/progress", response_model=Dict[str, Any])
def update_video_progress(
    video_id: str, 
    position: int = Body(...),
    x_user_id: Optional[str] = Header("user_001")
):
    """
    Updates the playback progress for a video.
    """
    videos_db = get_videos_db()
    video = next((v for v in videos_db if v["id"] == video_id), None)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if not x_user_id:
        raise HTTPException(status_code=401, detail="User authentication required")
    
    # 更新用户观看进度
    duration = video.get("duration", 0)
    update_video_watch_progress(x_user_id, video_id, position, duration)
    
    progress_percentage = (position / duration * 100) if duration > 0 else 0
    
    print(f"Progress updated: User [{x_user_id}] Video [{video_id}] position={position}s progress={progress_percentage:.1f}%")
    return {
        "message": "Progress updated successfully", 
        "videoId": video_id, 
        "position": position,
        "progress": progress_percentage
    }

@router.put("/videos/{video_id}/watch-count", response_model=Dict[str, Any])
def increment_watch_count(
    video_id: str,
    x_user_id: Optional[str] = Header("user_001")
):
    """
    Increments the watch count for a video.
    """
    videos_db = get_videos_db()
    video = next((v for v in videos_db if v["id"] == video_id), None)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if not x_user_id:
        raise HTTPException(status_code=401, detail="User authentication required")

    # 获取当前观看次数并增加1
    progress_data = get_video_watch_progress(x_user_id, video_id)
    current_watch_count = progress_data.get("watchCount", 0)
    new_watch_count = current_watch_count + 1
    
    # 更新观看次数（保持其他进度数据不变）
    duration = video.get("duration", 0)
    position = progress_data.get("position", 0)
    update_video_watch_progress(x_user_id, video_id, position, duration, new_watch_count)
    
    print(f"Watch count updated: User [{x_user_id}] Video [{video_id}] count: {current_watch_count} -> {new_watch_count}")
    return {
        "message": "Watch count updated successfully", 
        "videoId": video_id, 
        "newWatchCount": new_watch_count
    }

@router.put("/videos/{video_id}/cache", response_model=Dict[str, Any])
def update_video_cache(
    video_id: str,
    is_cached: bool = Body(...),
    local_path: Optional[str] = Body(None),
    x_user_id: Optional[str] = Header("user_001")
):
    """
    Updates the cache status for a video.
    """
    videos_db = get_videos_db()
    video = next((v for v in videos_db if v["id"] == video_id), None)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    if not x_user_id:
        raise HTTPException(status_code=401, detail="User authentication required")

    # 更新缓存状态
    update_video_cache_status(x_user_id, video_id, is_cached, local_path)
    
    action = "cached" if is_cached else "removed from cache"
    print(f"Cache updated: User [{x_user_id}] Video [{video_id}] {action}")
    
    return {
        "message": f"Video cache {action} successfully", 
        "videoId": video_id, 
        "isCached": is_cached,
        "localPath": local_path if is_cached else None
    }
