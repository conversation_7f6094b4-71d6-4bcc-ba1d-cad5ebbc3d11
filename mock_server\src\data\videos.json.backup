[{"id": "free-dating-prep", "title": "01. 约会前的准备工作", "description": "学习约会前的各种准备工作，包括形象打理、心理准备等。", "duration": "15:30", "playCount": "1.2万", "watch_count": 12000, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video1.mp4"}, {"id": "free-dating-location", "title": "02. 约会地点的选择", "description": "如何选择合适的约会地点，营造浪漫氛围。", "duration": "12:45", "playCount": "8.5千", "watch_count": 8500, "watch_progress": 0.65, "cache_status": "CACHED", "download_percent": null, "progressPercent": 65, "progressFloat": 0.65, "cloudUrl": "https://example.com/videos/video2.mp4"}, {"id": "free-pickup-mindset", "title": "01. 搭讪基础心态", "description": "建立正确的搭讪心态，克服内心恐惧。", "duration": "18:20", "playCount": "2.1万", "watch_count": 21000, "watch_progress": 0.0, "cache_status": "DOWNLOADING", "download_percent": 45, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video3.mp4"}, {"id": "free-pickup-opening", "title": "02. 自然开场技巧", "description": "学习自然而然的开场白技巧。", "duration": "16:15", "playCount": "1.8万", "watch_count": 18000, "watch_progress": 1.0, "cache_status": "CACHED", "download_percent": null, "progressPercent": 100, "progressFloat": 1.0, "cloudUrl": "https://example.com/videos/video4.mp4"}, {"id": "free-pickup-anxiety", "title": "03. 克服紧张情绪", "description": "有效方法帮助你克服搭讪时的紧张情绪。", "duration": "14:30", "playCount": "1.5万", "watch_count": 15000, "watch_progress": 0.25, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 25, "progressFloat": 0.25, "cloudUrl": "https://example.com/videos/video5.mp4"}, {"id": "love-guide-1-attraction", "title": "01. 初识吸引力法则", "description": "深入了解吸引力的本质和运作原理。", "duration": "22:30", "playCount": "0", "watch_count": 0, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video6.mp4"}, {"id": "love-guide-1-confidence", "title": "02. 建立自信的方法", "description": "系统性方法帮助你建立内在自信。", "duration": "19:45", "playCount": "1", "watch_count": 1, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video7.mp4"}, {"id": "love-guide-1-first-impression", "title": "03. 第一印象的重要性", "description": "如何在初次见面时留下深刻的好印象。", "duration": "17:20", "playCount": "2", "watch_count": 2, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video8.mp4"}, {"id": "love-guide-1-body-language", "title": "04. 肢体语言的艺术", "description": "掌握肢体语言在恋爱中的重要作用。", "duration": "21:15", "playCount": "3", "watch_count": 3, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video11.mp4"}, {"id": "love-guide-1-emotional-intelligence", "title": "05. 情商提升技巧", "description": "提高情商，更好地理解和处理情感。", "duration": "18:30", "playCount": "4", "watch_count": 4, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video12.mp4"}, {"id": "love-guide-1-conversation-skills", "title": "06. 对话技巧进阶", "description": "掌握高级对话技巧，让聊天更有趣。", "duration": "23:45", "playCount": "5", "watch_count": 5, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video13.mp4"}, {"id": "love-guide-1-date-planning", "title": "07. 约会策划大师", "description": "学会策划完美的约会，留下美好回忆。", "duration": "20:20", "playCount": "6", "watch_count": 6, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video14.mp4"}, {"id": "love-guide-1-relationship-building", "title": "08. 关系建立与维护", "description": "建立稳固的恋爱关系并长期维护。", "duration": "25:10", "playCount": "7", "watch_count": 7, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video15.mp4"}, {"id": "love-guide-1-conflict-resolution", "title": "09. 冲突解决智慧", "description": "学会处理恋爱中的矛盾和冲突。", "duration": "19:30", "playCount": "8", "watch_count": 8, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video16.mp4"}, {"id": "love-guide-1-commitment", "title": "10. 承诺与责任", "description": "理解恋爱中的承诺和责任的重要性。", "duration": "22:15", "playCount": "9", "watch_count": 9, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video17.mp4"}, {"id": "love-guide-1-future-planning", "title": "11. 未来规划共识", "description": "与伴侣共同规划美好的未来。", "duration": "24:30", "playCount": "10", "watch_count": 10, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://example.com/videos/video18.mp4"}, {"id": "love-guide-2-deep-communication", "title": "01. 深度沟通技巧", "description": "进阶的深度沟通技巧和方法。", "duration": "26:15", "playCount": "1.8万", "watch_count": 18000, "watch_progress": 0.8, "cache_status": "CACHED", "download_percent": null, "progressPercent": 80, "progressFloat": 0.8, "cloudUrl": "https://example.com/videos/video9.mp4"}, {"id": "chat-tech-1-opening", "title": "01. 开场白技巧", "description": "掌握各种场合的开场白技巧。", "duration": "20:15", "playCount": "3.2万", "watch_count": 32000, "watch_progress": 0.3, "cache_status": "DOWNLOADING", "download_percent": 78, "progressPercent": 30, "progressFloat": 0.3, "cloudUrl": "https://example.com/videos/video10.mp4"}, {"id": "love-guide-1-demo-0", "title": "01. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 1", "duration": "20:00", "categoryId": "love-guide-1", "playCount": "0", "watch_count": 0, "watch_progress": 0.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 0, "progressFloat": 0.0, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-1", "title": "02. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 2", "duration": "21:05", "categoryId": "love-guide-1", "playCount": "1", "watch_count": 1, "watch_progress": 0.1, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 10, "progressFloat": 0.1, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-2", "title": "03. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 3", "duration": "22:10", "playCount": "2", "watch_count": 2, "watch_progress": 0.2, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 20, "progressFloat": 0.2, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-3", "title": "04. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 4", "duration": "23:15", "playCount": "3", "watch_count": 3, "watch_progress": 0.3, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 30, "progressFloat": 0.3, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-4", "title": "05. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 5", "duration": "24:20", "playCount": "4", "watch_count": 4, "watch_progress": 0.4, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 40, "progressFloat": 0.4, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-5", "title": "06. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 6", "duration": "25:25", "playCount": "5", "watch_count": 5, "watch_progress": 0.5, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 50, "progressFloat": 0.5, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-6", "title": "07. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 7", "duration": "26:30", "playCount": "6", "watch_count": 6, "watch_progress": 0.6, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 60, "progressFloat": 0.6, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-7", "title": "08. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 8", "duration": "27:35", "playCount": "7", "watch_count": 7, "watch_progress": 0.7, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 70, "progressFloat": 0.7, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-8", "title": "09. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 9", "duration": "28:40", "playCount": "8", "watch_count": 8, "watch_progress": 0.8, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 80, "progressFloat": 0.8, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-9", "title": "10. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 10", "duration": "29:45", "playCount": "9", "watch_count": 9, "watch_progress": 0.9, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 90, "progressFloat": 0.9, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}, {"id": "love-guide-1-demo-10", "title": "11. 恋爱宝典演示视频", "description": "这是用于演示徽章的视频 11", "duration": "30:50", "playCount": "10", "watch_count": 10, "watch_progress": 1.0, "cache_status": "NOT_CACHED", "download_percent": null, "progressPercent": 100, "progressFloat": 1.0, "cloudUrl": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-v2.mp4"}]