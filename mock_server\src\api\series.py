import json
from fastapi import APIRouter, HTTPException, Header
from typing import List, Optional
from ..models.series import Series
from ..models.category import Category
from ..models.video import Video
from ..utils.user_data import (
    get_user_purchases, 
    get_video_watch_progress, 
    get_video_cache_status,
    calculate_category_progress,
    calculate_category_watch_count
)
from pathlib import Path

router = APIRouter()

# Base directory for JSON data
DATA_DIR = Path(__file__).resolve().parent.parent / "data"

# Helper function to load data
def load_data(file_path):
    with open(DATA_DIR / file_path, "r", encoding="utf-8") as f:
        return json.load(f)

videos_db = load_data("videos.json")
categories_db = load_data("categories.json")
series_db = load_data("series.json")

def get_video_by_id(video_id, user_id: str = None):
    """Get video info with user personalization data"""
    video_data = next((video for video in videos_db if video["id"] == video_id), None)
    if not video_data:
        return None
    
    video_with_user_data = video_data.copy()
    
    if user_id:
        progress_data = get_video_watch_progress(user_id, video_id)
        cache_data = get_video_cache_status(user_id, video_id)
        
        video_with_user_data["watch_count"] = progress_data.get("watchCount", 0)
        video_with_user_data["progress"] = (
            progress_data.get("position", 0) / progress_data.get("duration", 1) 
            if progress_data.get("duration", 0) > 0 else 0.0
        )
        video_with_user_data["cache_status"] = (
            "CACHED" if cache_data.get("isCached", False) else "NOT_CACHED"
        )
    else:
        video_with_user_data["watch_count"] = 0
        video_with_user_data["progress"] = 0.0
        video_with_user_data["cache_status"] = "NOT_CACHED"
    
    return video_with_user_data

def get_category_by_id(category_id, user_purchases: List[str], user_id: str = None):
    """Get category info with user personalization data"""
    category_data = next((cat for cat in categories_db if cat["id"] == category_id), None)
    if not category_data:
        return None
    
    category_with_user_data = category_data.copy()
    
    # Get video list with user data
    videos = []
    for vid in category_data.get("videoIds", []):
        video = get_video_by_id(vid, user_id)
        if video:
            videos.append(Video(**video))
    
    category_with_user_data["videos"] = videos
    category_with_user_data["totalVideos"] = len(videos)
    
    # Check if purchased
    series_id = category_data.get("seriesId")
    is_purchased = (
        category_id in user_purchases or 
        series_id in user_purchases or
        category_data.get("isFree", False)
    )
    category_with_user_data["isPurchased"] = is_purchased
    
    # Calculate progress
    if user_id and (is_purchased or category_data.get("isFree", False)):
        video_ids = category_data.get("videoIds", [])
        category_with_user_data["progress"] = calculate_category_progress(user_id, video_ids)
        category_with_user_data["watchCount"] = calculate_category_watch_count(user_id, video_ids)
    else:
        category_with_user_data["progress"] = 0.0
        category_with_user_data["watchCount"] = 0
    
    return Category(**category_with_user_data)

def calculate_complete_package_price(user_purchases: List[str]) -> int:
    """Calculate complete package price"""
    total_price = 0
    
    for category_data in categories_db:
        if not category_data.get("isFree", False):
            category_id = category_data["id"]
            series_id = category_data.get("seriesId")
            is_purchased = (
                category_id in user_purchases or 
                series_id in user_purchases
            )
            
            if not is_purchased:
                total_price += category_data.get("price", 0)
    
    return total_price

def calculate_series_display_price(series_id: str, user_purchases: List[str]) -> int:
    """Calculate series display price"""
    series_data = next((s for s in series_db if s["id"] == series_id), None)
    if not series_data:
        return 0
    
    if series_data.get("isPackage", False):
        return calculate_complete_package_price(user_purchases)
    
    if series_data.get("isFree", False):
        return 0
    
    if series_id in user_purchases:
        return 0
    
    total_price = 0
    for cat_id in series_data.get("categoryIds", []):
        category = get_category_by_id(cat_id, user_purchases, None)
        if category and not category.isPurchased and not category.isFree:
            total_price += category.price or 0
    
    return total_price

@router.get("/series", response_model=List[Series])
def get_all_series(x_user_id: Optional[str] = Header("user_001")):
    """Returns all course series with purchase status"""
    try:
        user_purchases = get_user_purchases(x_user_id) if x_user_id else []
        
        populated_series = []
        for series_data in series_db:
            series_id = series_data["id"]
            series_data = series_data.copy()  # Avoid modifying original
            series_data["isPurchased"] = series_id in user_purchases

            categories = [
                get_category_by_id(cat_id, user_purchases, x_user_id) 
                for cat_id in series_data.get("categoryIds", []) 
                if get_category_by_id(cat_id, user_purchases, x_user_id)
            ]
            
            if series_data.get("isPackage", False):
                total_price = calculate_complete_package_price(user_purchases)
                series_data["price"] = total_price
                series_data["isPurchased"] = total_price == 0
                series_data["categories"] = []
                series_data["totalVideos"] = 0
            else:
                display_price = calculate_series_display_price(series_id, user_purchases)
                series_data["price"] = display_price
                series_data["categories"] = categories
                series_data["totalVideos"] = sum(cat.totalVideos for cat in categories)
                
            populated_series.append(Series(**series_data))
        return populated_series
    
    except Exception as e:
        import traceback
        error_detail = f"Series API Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

@router.get("/series/{series_id}/categories", response_model=List[Category])
def get_categories_for_series(series_id: str, x_user_id: Optional[str] = Header("user_001")):
    """Returns categories for a specific series"""
    series_data = next((s for s in series_db if s["id"] == series_id), None)
    if not series_data:
        raise HTTPException(status_code=404, detail="Series not found")
    
    user_purchases = get_user_purchases(x_user_id) if x_user_id else []
    
    categories = [
        get_category_by_id(cat_id, user_purchases, x_user_id) 
        for cat_id in series_data.get("categoryIds", []) 
        if get_category_by_id(cat_id, user_purchases, x_user_id)
    ]
    
    if not categories:
        raise HTTPException(status_code=404, detail="No categories found for this series")
        
    return categories 