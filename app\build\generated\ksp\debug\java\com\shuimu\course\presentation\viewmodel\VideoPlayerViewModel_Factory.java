package com.shuimu.course.presentation.viewmodel;

import androidx.lifecycle.SavedStateHandle;
import com.shuimu.course.data.manager.DataLayerManager;
import com.shuimu.course.domain.repository.PlayProgressRepository;
import com.shuimu.course.domain.repository.PlaylistRepository;
import com.shuimu.course.domain.repository.SeriesRepository;
import com.shuimu.course.domain.repository.VideoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class VideoPlayerViewModel_Factory implements Factory<VideoPlayerViewModel> {
  private final Provider<SeriesRepository> seriesRepositoryProvider;

  private final Provider<PlayProgressRepository> playProgressRepositoryProvider;

  private final Provider<VideoRepository> videoRepositoryProvider;

  private final Provider<PlaylistRepository> playlistRepositoryProvider;

  private final Provider<DataLayerManager> dataLayerManagerProvider;

  private final Provider<SavedStateHandle> savedStateHandleProvider;

  public VideoPlayerViewModel_Factory(Provider<SeriesRepository> seriesRepositoryProvider,
      Provider<PlayProgressRepository> playProgressRepositoryProvider,
      Provider<VideoRepository> videoRepositoryProvider,
      Provider<PlaylistRepository> playlistRepositoryProvider,
      Provider<DataLayerManager> dataLayerManagerProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    this.seriesRepositoryProvider = seriesRepositoryProvider;
    this.playProgressRepositoryProvider = playProgressRepositoryProvider;
    this.videoRepositoryProvider = videoRepositoryProvider;
    this.playlistRepositoryProvider = playlistRepositoryProvider;
    this.dataLayerManagerProvider = dataLayerManagerProvider;
    this.savedStateHandleProvider = savedStateHandleProvider;
  }

  @Override
  public VideoPlayerViewModel get() {
    return newInstance(seriesRepositoryProvider.get(), playProgressRepositoryProvider.get(), videoRepositoryProvider.get(), playlistRepositoryProvider.get(), dataLayerManagerProvider.get(), savedStateHandleProvider.get());
  }

  public static VideoPlayerViewModel_Factory create(
      Provider<SeriesRepository> seriesRepositoryProvider,
      Provider<PlayProgressRepository> playProgressRepositoryProvider,
      Provider<VideoRepository> videoRepositoryProvider,
      Provider<PlaylistRepository> playlistRepositoryProvider,
      Provider<DataLayerManager> dataLayerManagerProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    return new VideoPlayerViewModel_Factory(seriesRepositoryProvider, playProgressRepositoryProvider, videoRepositoryProvider, playlistRepositoryProvider, dataLayerManagerProvider, savedStateHandleProvider);
  }

  public static VideoPlayerViewModel newInstance(SeriesRepository seriesRepository,
      PlayProgressRepository playProgressRepository, VideoRepository videoRepository,
      PlaylistRepository playlistRepository, DataLayerManager dataLayerManager,
      SavedStateHandle savedStateHandle) {
    return new VideoPlayerViewModel(seriesRepository, playProgressRepository, videoRepository, playlistRepository, dataLayerManager, savedStateHandle);
  }
}
