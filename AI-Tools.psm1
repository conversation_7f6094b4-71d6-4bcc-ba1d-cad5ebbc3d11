# AI Tools PowerShell Module
# Unified management of all AI-related functions with environment detection and error handling

# Set console encoding to UTF-8
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding

# Log function
function Write-AILog {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage -ForegroundColor $Color
}

# Environment detection function
function Test-AIEnvironment {
    Write-AILog "Checking AI environment configuration..." "INFO" "Cyan"
    
    $issues = @()
    
    # Check WSL
    try {
        $wslResult = & wsl --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-AILog "WSL is installed" "INFO" "Green"
        } else {
            $issues += "WSL is not installed or configured incorrectly"
        }
    } catch {
        $issues += "WSL detection failed: $_"
    }
    
    # Check Claude CLI
    try {
        $claudeResult = & wsl claude --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-AILog "Claude CLI is available" "INFO" "Green"
        } else {
            $issues += "Claude CLI is not available in WSL"
        }
    } catch {
        $issues += "Claude CLI detection failed: $_"
    }
    
    # Check PATH configuration
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentPath -like "*D:\Tools*") {
        Write-AILog "D:\Tools is in PATH" "INFO" "Green"
    } else {
        $issues += "D:\Tools is not in user PATH"
    }
    
    if ($issues.Count -eq 0) {
        Write-AILog "Environment check passed!" "INFO" "Green"
        return $true
    } else {
        Write-AILog "Found issues:" "WARN" "Yellow"
        foreach ($issue in $issues) {
            Write-AILog "  - $issue" "WARN" "Yellow"
        }
        return $false
    }
}

# Claude invocation function (replaces claude.cmd)
function Invoke-Claude {
    param(
        [Parameter(ValueFromPipeline=$true)]
        [string[]]$InputText,
        
        [string]$Prompt,
        [switch]$Help
    )
    
    begin {
        if ($Help) {
            Write-Host @"
Claude CLI Windows Wrapper

Usage:
  claude [options]                    # Interactive mode
  claude -p "prompt"                  # Single prompt
  echo "content" | claude             # Pipeline input
  
Options:
  -p, --prompt     Specify prompt
  -h, --help       Show help
"@ -ForegroundColor Cyan
            return
        }
        
        $allInput = @()
    }
    
    process {
        if ($InputText) {
            $allInput += $InputText
        }
    }
    
    end {
        try {
            Write-AILog "Calling Claude CLI..." "INFO" "Cyan"
            
            if ($allInput.Count -gt 0) {
                # Handle pipeline input
                $combinedInput = $allInput -join "`n"
                Write-AILog "Processing pipeline input ($($combinedInput.Length) chars)" "INFO" "Yellow"
                
                if ($Prompt) {
                    & wsl claude -p "$Prompt`n`n$combinedInput"
                } else {
                    & wsl claude -p $combinedInput
                }
            } elseif ($Prompt) {
                # Direct prompt
                & wsl claude -p $Prompt
            } else {
                # Interactive mode
                & wsl claude
            }
            
            if ($LASTEXITCODE -eq 0) {
                Write-AILog "Claude call successful" "INFO" "Green"
            } else {
                Write-AILog "Claude call failed (exit code: $LASTEXITCODE)" "ERROR" "Red"
            }
        } catch {
            Write-AILog "Claude call exception: $_" "ERROR" "Red"
        }
    }
}

# AI pipe processing function
function Invoke-AIPipe {
    param(
        [Parameter(Mandatory=$true)]
        [string]$SourceCommand,
        
        [Parameter(Mandatory=$true)]
        [string]$TargetCommand
    )
    
    Write-AILog "AI pipe processing..." "INFO" "Cyan"
    
    try {
        Write-AILog "Executing source command: $SourceCommand" "INFO" "Green"
        $sourceOutput = Invoke-Expression $SourceCommand
        
        $sourceText = $sourceOutput | Out-String
        $cleanText = $sourceText.Trim()
        
        Write-AILog "Source output obtained successfully ($($cleanText.Length) chars)" "INFO" "Green"
        
        $fullTargetCommand = "$TargetCommand '$cleanText'"
        Write-AILog "Executing target command: $fullTargetCommand" "INFO" "Green"
        
        Invoke-Expression $fullTargetCommand
        
        Write-AILog "AI pipe processing completed" "INFO" "Green"
    } catch {
        Write-AILog "AI pipe processing failed: $_" "ERROR" "Red"
    }
}

# Encoding pipe function
function ConvertTo-UTF8Pipe {
    param(
        [Parameter(ValueFromPipeline=$true)]
        [string[]]$InputObject
    )
    
    begin {
        $OutputEncoding = [System.Text.Encoding]::UTF8
        [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
    }
    
    process {
        foreach ($line in $InputObject) {
            $utf8Bytes = [System.Text.Encoding]::UTF8.GetBytes($line)
            $utf8String = [System.Text.Encoding]::UTF8.GetString($utf8Bytes)
            Write-Output $utf8String
        }
    }
}

# Export functions
Export-ModuleMember -Function Test-AIEnvironment, Invoke-Claude, Invoke-AIPipe, ConvertTo-UTF8Pipe

# Set aliases
Set-Alias -Name claude -Value Invoke-Claude
Set-Alias -Name ai-env -Value Test-AIEnvironment
Set-Alias -Name ai-pipe -Value Invoke-AIPipe
Set-Alias -Name encode-pipe -Value ConvertTo-UTF8Pipe

Export-ModuleMember -Alias claude, ai-env, ai-pipe, encode-pipe 