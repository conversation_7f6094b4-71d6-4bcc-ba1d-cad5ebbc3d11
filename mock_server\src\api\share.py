from fastapi import APIRouter
from typing import List, Dict, Any

router = APIRouter()

@router.get("/share/earnings", response_model=Dict[str, Any])
def get_share_earnings():
    """
    Returns mock share earnings data.
    """
    return {
        "total_earnings": 2468.50,
        "available_for_withdrawal": 1234.50,
        "withdrawn": 1234.00,
        "shared_users_count": 156,
        "monthly_earnings": [
            {"month": "1月", "earnings": 320.00},
            {"month": "2月", "earnings": 450.50},
            {"month": "3月", "earnings": 280.00},
            {"month": "4月", "earnings": 510.00},
            {"month": "5月", "earnings": 456.50},
            {"month": "6月", "earnings": 451.50}
        ]
    }

@router.post("/share/create", response_model=Dict[str, str])
def create_share_link():
    """
    Creates a mock share link.
    """
    return {"share_link": "https://shuimu.app/share?code=ABC123XYZ"}

@router.get("/share/ranking", response_model=List[Dict[str, Any]])
def get_share_ranking():
    """
    Returns a mock share ranking list.
    """
    return [
        {"rank": 1, "username": "阿强", "avatar": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face", "earnings": 3245.00},
        {"rank": 2, "username": "小美", "avatar": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face", "earnings": 1856.00},
        {"rank": 3, "username": "小雨", "avatar": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face", "earnings": 1234.00},
        {"rank": 4, "username": "小明", "avatar": "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop&crop=face", "earnings": 987.00},
        {"rank": 15, "username": "张三", "avatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face", "earnings": 456.50}
    ]

@router.get("/share/materials", response_model=Dict[str, List[Dict[str, str]]])
def get_share_materials():
    """
    Returns mock share materials (posters and copy).
    """
    return {
        "posters": [
            {"id": "poster1", "title": "恋爱宝典", "imageUrl": "https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?w=150&h=200&fit=crop"},
            {"id": "poster2", "title": "聊天技巧", "imageUrl": "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=150&h=200&fit=crop"}
        ],
        "copywriting": [
            {"id": "copy1", "title": "恋爱必修课", "content": "🔥 想要脱单？想要恋爱技巧？\n💕 水幕App - 专业恋爱学习平台\n✨ 系统化课程，实战技巧分享\n🎯 让你在恋爱路上更自信！\n👇 点击链接立即体验"},
            {"id": "copy2", "title": "聊天技巧", "content": "💬 还在为聊天没话题发愁？\n🎯 水幕App教你聊天技巧\n📚 从破冰到深入，全套聊天攻略\n💝 让你成为聊天高手！\n🔗 马上学习，告别尬聊"}
        ]
    } 