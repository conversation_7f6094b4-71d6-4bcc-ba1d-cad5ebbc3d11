package com.shuimu.course.data.local.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

@Entity(
    tableName = "categories",
    foreignKeys = [ForeignKey(
        entity = SeriesEntity::class,
        parentColumns = ["id"],
        childColumns = ["series_id"],
        onDelete = ForeignKey.CASCADE
    )],
    indices = [Index(value = ["series_id"])]
)
data class CategoryEntity(
    @PrimaryKey val id: String,
    val title: String,
    @ColumnInfo(name = "series_id") val seriesId: String,
    val price: Int?,
    @ColumnInfo(name = "is_free") val isFree: Boolean,
    @ColumnInfo(name = "is_purchased") val isPurchased: Boolean?,
    @ColumnInfo(name = "default_expanded") val defaultExpanded: Boolean = false
) 