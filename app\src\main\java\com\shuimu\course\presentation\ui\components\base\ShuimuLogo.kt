package com.shuimu.course.presentation.ui.components.base

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.layout.offset
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.shuimu.course.R
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun ShuimuLogo(
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    animated: Boolean = false
) {
    val scale = if (animated) {
        val infiniteTransition = rememberInfiniteTransition(label = "logo-animation")
        val animatedScale by infiniteTransition.animateFloat(
            initialValue = 1f,
            targetValue = 1.1f,
            animationSpec = infiniteRepeatable(
                animation = tween(1000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ),
            label = "logo-scale"
        )
        animatedScale
    } else {
        1f
    }

    Surface(
        modifier = modifier
            .size(size)
            .scale(scale),
        shape = CircleShape,
        color = Color(0xFF3B82F6) // 蓝色背景
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_water_fa),
                contentDescription = "水幕Logo",
                tint = Color.White,
                modifier = Modifier
                    .size(size * 0.6f) // Font Awesome图标适合的尺寸
                    .offset(y = 0.dp) // Font Awesome图标居中良好，无需偏移
            )
        }
    }
}

@Preview(name = "Logo - Small")
@Composable
fun ShuimuLogoSmallPreview() {
    ShuimuCourseTheme {
        ShuimuLogo(size = 32.dp)
    }
}

@Preview(name = "Logo - Medium")
@Composable
fun ShuimuLogoMediumPreview() {
    ShuimuCourseTheme {
        ShuimuLogo(size = 48.dp)
    }
}

@Preview(name = "Logo - Large Animated")
@Composable
fun ShuimuLogoLargePreview() {
    ShuimuCourseTheme {
        ShuimuLogo(size = 80.dp, animated = true)
    }
} 