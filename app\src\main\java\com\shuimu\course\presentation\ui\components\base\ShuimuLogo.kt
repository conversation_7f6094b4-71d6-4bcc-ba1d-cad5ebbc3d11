package com.shuimu.course.presentation.ui.components.base

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.layout.offset
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.font.FontWeight
import com.shuimu.course.R
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun ShuimuLogo(
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    animated: Boolean = false,
    useWaveIcon: Boolean = false, // 是否使用水波浪图标
    useTextMode: Boolean = false  // 🔥 新增：是否使用文字模式（显示"水幕"文字）
) {
    val scale = if (animated) {
        val infiniteTransition = rememberInfiniteTransition(label = "logo-animation")
        val animatedScale by infiniteTransition.animateFloat(
            initialValue = 1f,
            targetValue = 1.1f,
            animationSpec = infiniteRepeatable(
                animation = tween(1000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ),
            label = "logo-scale"
        )
        animatedScale
    } else {
        1f
    }

    Surface(
        modifier = modifier
            .size(size)
            .scale(scale),
        shape = CircleShape,
        color = Color(0xFF3B82F6) // 蓝色背景
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            if (useTextMode) {
                // 🔥 文字模式：显示白色"水幕"文字
                Text(
                    text = "水幕",
                    color = Color.White,
                    fontSize = (size * 0.25f).value.sp, // 根据Logo尺寸调整文字大小
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            } else {
                // 图标模式：显示水滴或水波浪图标
                Icon(
                    painter = painterResource(
                        id = if (useWaveIcon) R.drawable.ic_water_wave else R.drawable.ic_water_fa
                    ),
                    contentDescription = "水幕Logo",
                    tint = Color.White,
                    modifier = Modifier
                        .size(size * 0.6f) // 图标适合的尺寸
                        .offset(y = 0.dp) // 图标居中
                )
            }
        }
    }
}

@Preview(name = "Logo - Small")
@Composable
fun ShuimuLogoSmallPreview() {
    ShuimuCourseTheme {
        ShuimuLogo(size = 32.dp)
    }
}

@Preview(name = "Logo - Medium")
@Composable
fun ShuimuLogoMediumPreview() {
    ShuimuCourseTheme {
        ShuimuLogo(size = 48.dp)
    }
}

@Preview(name = "Logo - Large Animated")
@Composable
fun ShuimuLogoLargePreview() {
    ShuimuCourseTheme {
        ShuimuLogo(size = 80.dp, animated = true)
    }
}

@Preview(
    name = "Logo - 启动页文字版本",
    showBackground = true,
    widthDp = 200,
    heightDp = 200
)
@Composable
fun ShuimuLogoTextPreview() {
    ShuimuCourseTheme {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            ShuimuLogo(size = 120.dp, animated = true, useTextMode = true)
        }
    }
}

@Preview(
    name = "Logo对比 - 水滴 vs 水波浪",
    showBackground = true,
    widthDp = 412, // 🔥 手机屏幕宽度
    heightDp = 300  // 🔥 足够的高度显示对比
)
@Composable
fun ShuimuLogoComparisonPreview() {
    ShuimuCourseTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "水幕Logo设计对比",
                style = MaterialTheme.typography.headlineSmall,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
            Spacer(modifier = Modifier.height(32.dp))

            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 100.dp, useWaveIcon = false)
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "首页版本\n(水滴图标)",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 100.dp, useTextMode = true, animated = true)
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "启动页版本\n(水幕文字)",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Preview(
    name = "Logo尺寸对比",
    showBackground = true,
    widthDp = 412,
    heightDp = 400
)
@Composable
fun ShuimuLogoSizeComparisonPreview() {
    ShuimuCourseTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Logo尺寸对比",
                style = MaterialTheme.typography.headlineSmall
            )

            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 32.dp)
                    Text("32dp\n(小)", textAlign = TextAlign.Center, style = MaterialTheme.typography.bodySmall)
                }
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 48.dp)
                    Text("48dp\n(中)", textAlign = TextAlign.Center, style = MaterialTheme.typography.bodySmall)
                }
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 80.dp)
                    Text("80dp\n(大)", textAlign = TextAlign.Center, style = MaterialTheme.typography.bodySmall)
                }
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 120.dp, useTextMode = true)
                    Text("120dp\n(启动页文字)", textAlign = TextAlign.Center, style = MaterialTheme.typography.bodySmall)
                }
            }
        }
    }
}

@Preview(
    name = "Logo文字模式对比",
    showBackground = true,
    widthDp = 412,
    heightDp = 300
)
@Composable
fun ShuimuLogoTextModePreview() {
    ShuimuCourseTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "启动页Logo - 文字模式",
                style = MaterialTheme.typography.headlineSmall,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
            Spacer(modifier = Modifier.height(32.dp))

            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 80.dp, useTextMode = true)
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "80dp\n文字模式",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 120.dp, useTextMode = true, animated = true)
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "120dp\n启动页尺寸",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ShuimuLogo(size = 160.dp, useTextMode = true)
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "160dp\n超大尺寸",
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}