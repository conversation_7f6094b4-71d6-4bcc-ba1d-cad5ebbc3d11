package com.shuimu.course.data.remote.dto

import com.shuimu.course.domain.repository.SearchResult

data class SearchResultDto(
    val id: String,
    val title: String,
    val description: String,
    val type: String,
    val matchScore: Float,
    val thumbnailUrl: String? = null
)

fun SearchResultDto.toDomain(): SearchResult {
    return SearchResult(
        id = id,
        title = title,
        description = description,
        type = type,
        matchScore = matchScore,
        thumbnailUrl = thumbnailUrl
    )
} 