from pydantic import BaseModel
from typing import List, Optional
from .video import Video

class Category(BaseModel):
    id: str
    title: str
    seriesId: str
    price: Optional[int] = None  # 价格，单位：分
    isFree: bool
    isPurchased: bool = False
    defaultExpanded: bool = False
    totalVideos: int
    videos: List[Video]
    
    # 🔥 服务器计算的显示数据 - 遵循数据职责分离规则
    progress: float = 0.0  # 进度（0.0-1.0）
    watchCount: int = 0  # 观看次数
