package com.shuimu.course

import android.app.Application
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class CourseApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @Inject
    lateinit var appConfigManager: com.shuimu.course.data.manager.AppConfigManager

    override fun onCreate() {
        super.onCreate()
        android.util.Log.d("CourseApplication", "Application启动开始")

        // 静默预加载配置（不阻塞UI）
        kotlinx.coroutines.GlobalScope.launch {
            try {
                appConfigManager.preloadConfigs()
                android.util.Log.d("CourseApplication", "配置预加载完成")
            } catch (e: Exception) {
                android.util.Log.e("CourseApplication", "配置预加载失败", e)
            }
        }

        android.util.Log.d("CourseApplication", "Application启动完成")
    }
    
    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .setMinimumLoggingLevel(android.util.Log.DEBUG)
            .build()
} 