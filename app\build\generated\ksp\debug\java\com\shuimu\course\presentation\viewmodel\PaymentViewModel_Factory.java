package com.shuimu.course.presentation.viewmodel;

import androidx.lifecycle.SavedStateHandle;
import com.shuimu.course.data.manager.DataLayerManager;
import com.shuimu.course.data.manager.DataSyncManager;
import com.shuimu.course.domain.repository.PaymentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class PaymentViewModel_Factory implements Factory<PaymentViewModel> {
  private final Provider<PaymentRepository> paymentRepositoryProvider;

  private final Provider<DataSyncManager> dataSyncManagerProvider;

  private final Provider<DataLayerManager> dataLayerManagerProvider;

  private final Provider<SavedStateHandle> savedStateHandleProvider;

  public PaymentViewModel_Factory(Provider<PaymentRepository> paymentRepositoryProvider,
      Provider<DataSyncManager> dataSyncManagerProvider,
      Provider<DataLayerManager> dataLayerManagerProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    this.paymentRepositoryProvider = paymentRepositoryProvider;
    this.dataSyncManagerProvider = dataSyncManagerProvider;
    this.dataLayerManagerProvider = dataLayerManagerProvider;
    this.savedStateHandleProvider = savedStateHandleProvider;
  }

  @Override
  public PaymentViewModel get() {
    return newInstance(paymentRepositoryProvider.get(), dataSyncManagerProvider.get(), dataLayerManagerProvider.get(), savedStateHandleProvider.get());
  }

  public static PaymentViewModel_Factory create(
      Provider<PaymentRepository> paymentRepositoryProvider,
      Provider<DataSyncManager> dataSyncManagerProvider,
      Provider<DataLayerManager> dataLayerManagerProvider,
      Provider<SavedStateHandle> savedStateHandleProvider) {
    return new PaymentViewModel_Factory(paymentRepositoryProvider, dataSyncManagerProvider, dataLayerManagerProvider, savedStateHandleProvider);
  }

  public static PaymentViewModel newInstance(PaymentRepository paymentRepository,
      DataSyncManager dataSyncManager, DataLayerManager dataLayerManager,
      SavedStateHandle savedStateHandle) {
    return new PaymentViewModel(paymentRepository, dataSyncManager, dataLayerManager, savedStateHandle);
  }
}
