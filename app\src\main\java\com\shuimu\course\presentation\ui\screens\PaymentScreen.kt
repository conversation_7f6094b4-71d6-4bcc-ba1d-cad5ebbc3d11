package com.shuimu.course.presentation.ui.screens

import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.components.base.ShuimuButton
import com.shuimu.course.presentation.ui.components.base.ShuimuCard
import com.shuimu.course.presentation.ui.components.state.LoadingIndicator
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.PaymentViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentScreen(
    title: String,
    price: String,
    viewModel: PaymentViewModel = hiltViewModel(),
    onPaymentSuccess: () -> Unit
) {
    val state by viewModel.state.collectAsState()
    val context = LocalContext.current
    var selectedPaymentMethod by remember { mutableStateOf("alipay") }

    LaunchedEffect(key1 = state.paymentResult) {
        state.paymentResult?.let {
            Toast.makeText(context, it, Toast.LENGTH_LONG).show()
            onPaymentSuccess()
        }
    }
    LaunchedEffect(key1 = state.error) {
        state.error?.let {
            Toast.makeText(context, "Error: $it", Toast.LENGTH_LONG).show()
        }
    }

    Scaffold(
        topBar = { TopAppBar(title = { Text("确认支付") }) }
    ) { paddingValues ->
        Box(modifier = Modifier.fillMaxSize().padding(paddingValues)) {
            Column(modifier = Modifier.padding(16.dp)) {
                ShuimuCard {
                    Text("您正在购买:", style = MaterialTheme.typography.titleMedium)
                    Text(title, style = MaterialTheme.typography.headlineSmall)
                    Text("价格: ¥$price", style = MaterialTheme.typography.titleLarge, color = MaterialTheme.colorScheme.primary)
                }
                Spacer(Modifier.height(16.dp))
                Text("选择支付方式", style = MaterialTheme.typography.titleMedium)
                Spacer(Modifier.height(8.dp))
                PaymentMethodSelector(
                    selectedMethod = selectedPaymentMethod,
                    onMethodSelected = { selectedPaymentMethod = it }
                )
            }

            ShuimuButton(
                onClick = { viewModel.processPayment() },
                text = "确认支付 ¥$price",
                modifier = Modifier.align(Alignment.BottomCenter).padding(16.dp),
                enabled = !state.isLoading
            )

            if(state.isLoading) {
                LoadingIndicator()
            }
        }
    }
}

@Composable
fun PaymentMethodSelector(selectedMethod: String, onMethodSelected: (String) -> Unit) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth().selectable(
                selected = (selectedMethod == "alipay"),
                onClick = { onMethodSelected("alipay") }
            ).padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(selected = (selectedMethod == "alipay"), onClick = { onMethodSelected("alipay") })
            Text("支付宝", modifier = Modifier.padding(start = 8.dp))
        }
        Row(
            modifier = Modifier.fillMaxWidth().selectable(
                selected = (selectedMethod == "wechat"),
                onClick = { onMethodSelected("wechat") }
            ).padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(selected = (selectedMethod == "wechat"), onClick = { onMethodSelected("wechat") })
            Text("微信支付", modifier = Modifier.padding(start = 8.dp))
        }
    }
}

@Preview
@Composable
fun PaymentScreenPreview() {
    ShuimuCourseTheme {
        PaymentScreen(title = "道：恋爱宝典系列", price = "600.00", onPaymentSuccess = {})
    }
} 