package com.shuimu.course.data.manager;

import android.content.Context;
import com.shuimu.course.data.local.dao.CacheInfoDao;
import com.shuimu.course.data.local.dao.PlayProgressDao;
import com.shuimu.course.data.remote.api.PaymentApi;
import com.shuimu.course.data.remote.api.VideoApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DataLayerManager_Factory implements Factory<DataLayerManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CacheInfoDao> cacheInfoDaoProvider;

  private final Provider<PlayProgressDao> playProgressDaoProvider;

  private final Provider<VideoApi> videoApiProvider;

  private final Provider<PaymentApi> paymentApiProvider;

  private final Provider<DataSyncManager> dataSyncManagerProvider;

  public DataLayerManager_Factory(Provider<Context> contextProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider,
      Provider<PlayProgressDao> playProgressDaoProvider, Provider<VideoApi> videoApiProvider,
      Provider<PaymentApi> paymentApiProvider, Provider<DataSyncManager> dataSyncManagerProvider) {
    this.contextProvider = contextProvider;
    this.cacheInfoDaoProvider = cacheInfoDaoProvider;
    this.playProgressDaoProvider = playProgressDaoProvider;
    this.videoApiProvider = videoApiProvider;
    this.paymentApiProvider = paymentApiProvider;
    this.dataSyncManagerProvider = dataSyncManagerProvider;
  }

  @Override
  public DataLayerManager get() {
    return newInstance(contextProvider.get(), cacheInfoDaoProvider.get(), playProgressDaoProvider.get(), videoApiProvider.get(), paymentApiProvider.get(), dataSyncManagerProvider.get());
  }

  public static DataLayerManager_Factory create(Provider<Context> contextProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider,
      Provider<PlayProgressDao> playProgressDaoProvider, Provider<VideoApi> videoApiProvider,
      Provider<PaymentApi> paymentApiProvider, Provider<DataSyncManager> dataSyncManagerProvider) {
    return new DataLayerManager_Factory(contextProvider, cacheInfoDaoProvider, playProgressDaoProvider, videoApiProvider, paymentApiProvider, dataSyncManagerProvider);
  }

  public static DataLayerManager newInstance(Context context, CacheInfoDao cacheInfoDao,
      PlayProgressDao playProgressDao, VideoApi videoApi, PaymentApi paymentApi,
      DataSyncManager dataSyncManager) {
    return new DataLayerManager(context, cacheInfoDao, playProgressDao, videoApi, paymentApi, dataSyncManager);
  }
}
