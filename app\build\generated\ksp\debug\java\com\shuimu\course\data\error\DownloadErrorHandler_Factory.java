package com.shuimu.course.data.error;

import com.shuimu.course.domain.repository.CacheRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DownloadErrorHandler_Factory implements Factory<DownloadErrorHandler> {
  private final Provider<CacheRepository> cacheRepositoryProvider;

  public DownloadErrorHandler_Factory(Provider<CacheRepository> cacheRepositoryProvider) {
    this.cacheRepositoryProvider = cacheRepositoryProvider;
  }

  @Override
  public DownloadErrorHandler get() {
    return newInstance(cacheRepositoryProvider.get());
  }

  public static DownloadErrorHandler_Factory create(
      Provider<CacheRepository> cacheRepositoryProvider) {
    return new DownloadErrorHandler_Factory(cacheRepositoryProvider);
  }

  public static DownloadErrorHandler newInstance(CacheRepository cacheRepository) {
    return new DownloadErrorHandler(cacheRepository);
  }
}
