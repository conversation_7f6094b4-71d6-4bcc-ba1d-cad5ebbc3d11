from pydantic import BaseModel, Field
from typing import Optional

class User(BaseModel):
    id: str
    username: str
    nickname: str
    avatar: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str

class UserProfile(User):
    totalWatchTime: int = 0
    totalVideosWatched: int = 0
    totalDaysActive: int = 0
    streakDays: int = 0
    lastActiveDate: str = ""
