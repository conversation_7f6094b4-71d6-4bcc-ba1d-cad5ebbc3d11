package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.shuimu.course.CourseApplication",
    rootPackage = "com.shuimu.course",
    originatingRoot = "com.shuimu.course.CourseApplication",
    originatingRootPackage = "com.shuimu.course",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "CourseApplication",
    originatingRootSimpleNames = "CourseApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_shuimu_course_CourseApplication {
}
