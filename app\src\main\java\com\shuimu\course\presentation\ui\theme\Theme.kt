package com.shuimu.course.presentation.ui.theme

import android.app.Activity
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import androidx.compose.ui.graphics.Color

private val DarkColorScheme = darkColorScheme(
    primary = GreenPrimary,
    secondary = BlueAccent,
    tertiary = BlueAccentDark,
    background = BackgroundDark,
    surface = BackgroundDark,
    onPrimary = White,
    onSecondary = White,
    onTertiary = White,
    onBackground = TextPrimaryDark,
    onSurface = TextPrimaryDark,
    error = RedError,
    surfaceTint = Color.Transparent
)

private val LightColorScheme = lightColorScheme(
    primary = BlueAccent,
    secondary = GreenPrimary,
    tertiary = BlueAccentDark,
    background = BackgroundLightGray,
    surface = White,
    onPrimary = White,
    onSecondary = White,
    onTertiary = White,
    onBackground = TextPrimaryLight,
    onSurface = TextPrimaryLight,
    error = RedError,
    surfaceTint = Color.Transparent
)

@Composable
fun ShuimuCourseTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
} 