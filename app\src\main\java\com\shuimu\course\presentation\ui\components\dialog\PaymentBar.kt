package com.shuimu.course.presentation.ui.components.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.course.presentation.ui.components.base.ShuimuButton
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun PaymentBar(
    modifier: Modifier = Modifier,
    itemName: String,
    price: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min),
        shadowElevation = 8.dp,
        tonalElevation = 4.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = itemName,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp,
                    maxLines = 1
                )
                Text(
                    text = "价格: ¥$price",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.SemiBold
                )
            }
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                ShuimuButton(
                    onClick = onConfirm,
                    text = "立即支付",
                    modifier = Modifier.width(120.dp)
                )
                IconButton(onClick = onDismiss) {
                    Icon(Icons.Default.Close, contentDescription = "Close Payment Bar")
                }
            }
        }
    }
}

@Preview
@Composable
fun PaymentBarPreview() {
    ShuimuCourseTheme {
        PaymentBar(
            itemName = "道：恋爱宝典系列 (全套)",
            price = "600.00",
            onConfirm = {},
            onDismiss = {}
        )
    }
} 