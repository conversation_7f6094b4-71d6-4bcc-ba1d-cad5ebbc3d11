package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.data.manager.DataLayerManager
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.data.local.dao.SeriesDao
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject

/**
 * 启动页ViewModel - 负责数据预加载
 */
@HiltViewModel
class SplashViewModel @Inject constructor(
    private val seriesRepository: SeriesRepository,
    private val cacheRepository: CacheRepository,
    private val dataLayerManager: DataLayerManager,
    private val seriesDao: SeriesDao
) : ViewModel() {
    
    private val _state = MutableStateFlow(SplashState())
    val state: StateFlow<SplashState> = _state.asStateFlow()
    
    /**
     * 预加载数据 - 仿美团模式
     * 1. 尝试获取服务器数据（5秒超时）
     * 2. 失败则使用本地缓存数据
     * 3. 再失败则使用预置数据
     */
    fun preloadData() {
        viewModelScope.launch {
            android.util.Log.d("SplashViewModel", "开始数据预加载流程")
            
            try {
                // 第一步：尝试获取服务器数据（5秒超时）
                android.util.Log.d("SplashViewModel", "尝试获取服务器数据...")
                val serverDataResult = withTimeoutOrNull(5000L) {
                    seriesRepository.getSeries().collect { resource ->
                        when (resource) {
                            is Resource.Success -> {
                                if (resource.data?.isNotEmpty() == true) {
                                    android.util.Log.d("SplashViewModel", "服务器数据获取成功: ${resource.data.size}个系列")
                                    _state.value = _state.value.copy(
                                        isDataReady = true,
                                        dataSource = DataSource.SERVER
                                    )
                                    return@collect
                                }
                            }
                            is Resource.Error -> {
                                android.util.Log.w("SplashViewModel", "服务器数据获取失败: ${resource.message}")
                                throw Exception(resource.message ?: "服务器数据获取失败")
                            }
                            is Resource.Loading -> {
                                android.util.Log.d("SplashViewModel", "正在获取服务器数据...")
                            }
                        }
                    }
                }
                
                // 如果5秒内没有成功获取服务器数据
                if (!_state.value.isDataReady) {
                    android.util.Log.w("SplashViewModel", "服务器数据获取超时，尝试使用本地缓存")
                    tryUseLocalData()
                }
                
            } catch (e: Exception) {
                android.util.Log.e("SplashViewModel", "服务器数据获取异常", e)
                tryUseLocalData()
            }
        }
    }
    
    /**
     * 尝试使用本地缓存数据
     */
    private suspend fun tryUseLocalData() {
        try {
            android.util.Log.d("SplashViewModel", "尝试获取本地缓存数据...")
            
            // 检查本地是否有数据
            val hasLocalData = checkLocalDataAvailability()
            
            if (hasLocalData) {
                android.util.Log.d("SplashViewModel", "本地缓存数据可用")
                _state.value = _state.value.copy(
                    shouldUseLocalData = true,
                    dataSource = DataSource.LOCAL_CACHE
                )
            } else {
                android.util.Log.w("SplashViewModel", "本地缓存数据不可用，使用预置数据")
                usePresetData()
            }
            
        } catch (e: Exception) {
            android.util.Log.e("SplashViewModel", "本地缓存数据获取异常", e)
            usePresetData()
        }
    }
    
    /**
     * 使用预置数据
     */
    private fun usePresetData() {
        android.util.Log.d("SplashViewModel", "使用预置数据")
        _state.value = _state.value.copy(
            shouldUsePresetData = true,
            dataSource = DataSource.PRESET
        )
    }
    
    /**
     * 检查本地数据可用性
     */
    private suspend fun checkLocalDataAvailability(): Boolean {
        return try {
            // 检查是否有系列数据
            val seriesCount = seriesDao.getSeriesCount()
            android.util.Log.d("SplashViewModel", "本地系列数据数量: $seriesCount")
            seriesCount > 0
        } catch (e: Exception) {
            android.util.Log.e("SplashViewModel", "检查本地数据失败", e)
            false
        }
    }
}

/**
 * 启动页状态
 */
data class SplashState(
    val isDataReady: Boolean = false,           // 服务器数据是否准备完成
    val shouldUseLocalData: Boolean = false,    // 是否应该使用本地数据
    val shouldUsePresetData: Boolean = false,   // 是否应该使用预置数据
    val dataSource: DataSource = DataSource.UNKNOWN,
    val error: String? = null
)

/**
 * 数据来源
 */
enum class DataSource {
    UNKNOWN,      // 未知
    SERVER,       // 服务器数据
    LOCAL_CACHE,  // 本地缓存
    PRESET        // 预置数据
}
