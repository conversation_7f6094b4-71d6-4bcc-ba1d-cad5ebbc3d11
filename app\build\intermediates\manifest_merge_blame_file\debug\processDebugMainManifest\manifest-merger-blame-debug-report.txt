1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.shuimu.course"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:6:5-68
12-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
14-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:8:5-92
14-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:8:22-89
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->[androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:5-79
15-->[androidx.media3:media3-exoplayer:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\98772e44dd82187f64bd43b721368829\transformed\jetified-media3-exoplayer-1.3.1\AndroidManifest.xml:22:22-76
16    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
16-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
16-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
17
18    <permission
18-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
19        android:name="com.shuimu.course.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.shuimu.course.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
23
24    <application
24-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:10:5-45:19
25        android:name="com.shuimu.course.CourseApplication"
25-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:11:9-42
26        android:allowBackup="true"
26-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:12:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.13.1] D:\gradle-home\caches\9.0-milestone-1\transforms\c12dcf7643131f54deb56aa398bbca93\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:13:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:14:9-54
32        android:label="@string/app_name"
32-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:15:9-41
33        android:networkSecurityConfig="@xml/network_security_config"
33-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:18:9-69
34        android:supportsRtl="true"
34-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:16:9-35
35        android:theme="@style/Theme.ShuimuCourse" >
35-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:17:9-50
36        <activity
36-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:20:9-29:20
37            android:name="com.shuimu.course.MainActivity"
37-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:21:13-41
38            android:exported="true"
38-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:22:13-36
39            android:theme="@style/Theme.ShuimuCourse" >
39-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:23:13-54
40            <intent-filter>
40-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:24:13-28:29
41                <action android:name="android.intent.action.MAIN" />
41-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:25:17-69
41-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:25:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:27:17-77
43-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:27:27-74
44            </intent-filter>
45        </activity>
46
47        <service
47-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:31:9-38:19
48            android:name="com.shuimu.course.player.PlaybackService"
48-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:32:13-51
49            android:exported="true"
49-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:33:13-36
50            android:foregroundServiceType="mediaPlayback" >
50-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:34:13-58
51            <intent-filter>
51-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:35:13-37:29
52                <action android:name="androidx.media3.session.MediaSessionService" />
52-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:36:17-85
52-->D:\01-shuimu_01\app\src\main\AndroidManifest.xml:36:25-83
53            </intent-filter>
54        </service>
55        <service
55-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
56            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
56-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
57            android:directBootAware="false"
57-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
58            android:enabled="@bool/enable_system_alarm_service_default"
58-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
59            android:exported="false" />
59-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
60        <service
60-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
61            android:name="androidx.work.impl.background.systemjob.SystemJobService"
61-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
62            android:directBootAware="false"
62-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
63            android:enabled="@bool/enable_system_job_service_default"
63-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
64            android:exported="true"
64-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
65            android:permission="android.permission.BIND_JOB_SERVICE" />
65-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
66        <service
66-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
67            android:name="androidx.work.impl.foreground.SystemForegroundService"
67-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
68            android:directBootAware="false"
68-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
69            android:enabled="@bool/enable_system_foreground_service_default"
69-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
70            android:exported="false" />
70-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
71
72        <receiver
72-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
73            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
73-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
74            android:directBootAware="false"
74-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
75            android:enabled="true"
75-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
76            android:exported="false" />
76-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
77        <receiver
77-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
78            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
78-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
79            android:directBootAware="false"
79-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
80            android:enabled="false"
80-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
81            android:exported="false" >
81-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
82            <intent-filter>
82-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
83                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
83-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
83-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
84                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
84-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
84-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
85            </intent-filter>
86        </receiver>
87        <receiver
87-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
88            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
88-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
89            android:directBootAware="false"
89-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
90            android:enabled="false"
90-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
91            android:exported="false" >
91-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
92            <intent-filter>
92-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
93                <action android:name="android.intent.action.BATTERY_OKAY" />
93-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
93-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
94                <action android:name="android.intent.action.BATTERY_LOW" />
94-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
94-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
95            </intent-filter>
96        </receiver>
97        <receiver
97-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
98            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
98-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
99            android:directBootAware="false"
99-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
100            android:enabled="false"
100-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
101            android:exported="false" >
101-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
102            <intent-filter>
102-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
103                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
103-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
103-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
104                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
104-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
104-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
105            </intent-filter>
106        </receiver>
107        <receiver
107-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
108            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
108-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
109            android:directBootAware="false"
109-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
110            android:enabled="false"
110-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
111            android:exported="false" >
111-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
112            <intent-filter>
112-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
113                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
113-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
113-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
114            </intent-filter>
115        </receiver>
116        <receiver
116-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
117            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
117-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
118            android:directBootAware="false"
118-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
119            android:enabled="false"
119-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
120            android:exported="false" >
120-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
121            <intent-filter>
121-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
122                <action android:name="android.intent.action.BOOT_COMPLETED" />
122-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
122-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
123                <action android:name="android.intent.action.TIME_SET" />
123-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
123-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
124                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
124-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
124-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
125            </intent-filter>
126        </receiver>
127        <receiver
127-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
128            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
128-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
129            android:directBootAware="false"
129-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
130            android:enabled="@bool/enable_system_alarm_service_default"
130-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
131            android:exported="false" >
131-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
132            <intent-filter>
132-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
133                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
133-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
133-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
134            </intent-filter>
135        </receiver>
136        <receiver
136-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
137            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
137-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
138            android:directBootAware="false"
138-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
139            android:enabled="true"
139-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
140            android:exported="true"
140-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
141            android:permission="android.permission.DUMP" >
141-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
142            <intent-filter>
142-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
143                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
143-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
143-->[androidx.work:work-runtime:2.9.0] D:\gradle-home\caches\9.0-milestone-1\transforms\e66f0f998bbab9a0850368189bf6197d\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
144            </intent-filter>
145        </receiver>
146
147        <activity
147-->[androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
148            android:name="androidx.compose.ui.tooling.PreviewActivity"
148-->[androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
149            android:exported="true" />
149-->[androidx.compose.ui:ui-tooling-android:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\fc2b3a362e0f3ed1e584b87464f8b319\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
150        <activity
150-->[androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
151            android:name="androidx.activity.ComponentActivity"
151-->[androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
152            android:exported="true" />
152-->[androidx.compose.ui:ui-test-manifest:1.7.6] D:\gradle-home\caches\9.0-milestone-1\transforms\532af4bfffe73381502561907f81c362\transformed\jetified-ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
153
154        <service
154-->[androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
155            android:name="androidx.room.MultiInstanceInvalidationService"
155-->[androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
156            android:directBootAware="true"
156-->[androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
157            android:exported="false" />
157-->[androidx.room:room-runtime:2.6.1] D:\gradle-home\caches\9.0-milestone-1\transforms\46cba0a68df3b50319c43471a5f1ed2d\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
158
159        <receiver
159-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
160            android:name="androidx.profileinstaller.ProfileInstallReceiver"
160-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
161            android:directBootAware="false"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
162            android:enabled="true"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
163            android:exported="true"
163-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
164            android:permission="android.permission.DUMP" >
164-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
165            <intent-filter>
165-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
166                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
166-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
166-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
167            </intent-filter>
168            <intent-filter>
168-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
169                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
169-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
169-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
170            </intent-filter>
171            <intent-filter>
171-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
172                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
172-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
172-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
173            </intent-filter>
174            <intent-filter>
174-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
175                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
175-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
175-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\gradle-home\caches\9.0-milestone-1\transforms\66f7f08186629d9fc427a7e5e65f514b\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
176            </intent-filter>
177        </receiver>
178    </application>
179
180</manifest>
