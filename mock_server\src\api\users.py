import json
from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
from ..utils.user_data import load_user_data
from pathlib import Path

# Mock dependency for getting current user from a token
# In a real app, this would involve token validation
def get_current_user_id():
    return "user001" # For mocking, always return the same user

router = APIRouter()

# Base directory for JSON data
DATA_DIR = Path(__file__).resolve().parent.parent / "data"

def load_data(file_path):
    with open(DATA_DIR / file_path, "r", encoding="utf-8") as f:
        return json.load(f)

users_db = load_data("users.json")

@router.get("/user/profile")
def get_user_profile(user_id: str = Depends(get_current_user_id)):
    """
    Returns the profile for the current user.
    """
    user = next((u for u in users_db if u["id"] == user_id), None)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    # Here you could expand with more profile details if needed
    return user

@router.get("/user/purchases")
def get_user_purchases(user_id: str = Depends(get_current_user_id)) -> Dict[str, Any]:
    """
    Returns the purchase history for the current user.
    """
    user_data = load_user_data(user_id)
    purchases = user_data.get("purchases", {})
    
    # 转换为旧格式以保持兼容性
    purchase_history = []
    
    # 添加series购买记录
    for series_id in purchases.get("series", []):
        purchase_history.append({
            "userId": user_id,
            "itemId": series_id,
            "itemType": "series",
            "purchaseDate": "2024-01-01T00:00:00Z"  # 默认日期
        })
    
    # 添加categories购买记录
    for category_id in purchases.get("categories", []):
        purchase_history.append({
            "userId": user_id,
            "itemId": category_id,
            "itemType": "category",
            "purchaseDate": "2024-01-01T00:00:00Z"  # 默认日期
        })
    
    return {"purchases": purchase_history}

@router.get("/user/data")
def get_user_data(user_id: str = Depends(get_current_user_id)) -> Dict[str, Any]:
    """
    Returns all user data including purchases, progress, and cache.
    """
    return load_user_data(user_id)
