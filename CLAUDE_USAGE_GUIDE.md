# 🎉 Claude CLI 完美使用指南

## ✅ 安装完成！

恭喜！你现在拥有了完全功能的Claude Code CLI工具，支持原生的 `-p` 参数！

### 🔧 已完成的配置

1. ✅ **WSL环境修复** - 解决了DNS和网络问题
2. ✅ **Node.js环境** - 安装了Node.js 18.19.1和npm 9.2.0
3. ✅ **Claude Code安装** - 官方@anthropic-ai/claude-code v1.0.35
4. ✅ **Windows集成** - 创建了claude.bat和claude.cmd包装器
5. ✅ **全局PATH设置** - 可在任何目录使用claude命令

## 🚀 使用方法

### 核心语法（你想要的）

```bash
# 直接提问（使用原生-p参数）
claude -p "你的问题"

# 实际示例
claude -p "翻译：Hello World"
claude -p "计算 2+3*4"
claude -p "写一个Python函数计算斐波那契数列"
claude -p "解释什么是Kotlin协程"
```

### 完整功能对比

| 功能 | 命令语法 | 说明 |
|------|----------|------|
| **快速提问** | `claude -p "问题"` | 原生-p参数，最简洁 ✨ |
| **交互模式** | `claude` | 多轮对话模式 |
| **管道输入** | `echo "text" \| claude -p` | 处理管道数据 |
| **查看版本** | `claude --version` | 显示版本信息 |
| **查看帮助** | `claude --help` | 显示所有参数 |
| **指定模型** | `claude --model sonnet -p "问题"` | 选择特定模型 |
| **继续对话** | `claude --continue` | 恢复上次对话 |

### 🎯 实际使用示例

```bash
# 基础使用
claude -p "今天天气怎么样？"
claude -p "帮我写个TODO应用的需求文档"

# 代码相关
claude -p "优化这段JavaScript代码的性能"
claude -p "解释Android MVVM架构的最佳实践"
claude -p "如何在Kotlin中使用协程处理网络请求"

# 学习助手
claude -p "用简单的话解释机器学习的原理"
claude -p "制定一个30天学习Jetpack Compose的计划"

# 文本处理（管道操作）
echo "Hello, how are you?" | claude -p
type README.md | claude -p "总结这个文档"
git diff | claude -p "为这些代码变更生成commit消息"

# 高级功能
claude --model opus -p "设计一个高性能的缓存系统"
claude -p "分析这个错误日志" --verbose
```

### 🌟 原生功能优势

Claude Code原生支持的功能：

- ✅ **-p/--print参数** - 官方标准，非自定义
- ✅ **多种输出格式** - text, json, stream-json
- ✅ **管道完美支持** - 与Unix工具无缝集成
- ✅ **模型选择** - sonnet, opus等
- ✅ **会话管理** - continue, resume功能
- ✅ **权限控制** - allowedTools, disallowedTools
- ✅ **调试模式** - --debug, --verbose

### 📱 Android开发专用示例

```bash
# Kotlin代码分析
claude -p "优化这个Compose组件的性能"

# 架构建议
claude -p "在MVVM中如何处理网络错误和重试机制？"

# 代码生成
claude -p "为User数据类生成Repository和ViewModel"

# 测试用例
claude -p "为ProfileViewModel生成JUnit测试用例"

# API设计
claude -p "为用户管理功能设计RESTful API接口"
```

### 🔧 高级工作流

```bash
# 代码审查流程
git diff --cached | claude -p "审查这次提交的代码变更"

# 自动化文档生成
type src/main/java/com/shuimu/course/domain/model/User.kt | claude -p "生成API文档"

# 批量处理
foreach($file in Get-ChildItem *.kt) {
    Get-Content $file | claude -p "分析这个Kotlin文件的设计模式"
}

# 错误分析
type error.log | claude -p "分析这些错误日志，找出根本原因"
```

## 🎨 最佳实践

### 1. 选择合适的模式
- **-p模式**：快速问答、脚本化任务
- **交互模式**：复杂讨论、多轮对话

### 2. 利用管道威力
```bash
# 代码 → Claude → 分析
type app.js | claude -p "找出潜在的性能问题"

# Git → Claude → 提交消息
git log --oneline -5 | claude -p "分析最近的提交趋势"
```

### 3. 模型选择策略
- **日常任务**：默认sonnet（快速、经济）
- **复杂任务**：`--model opus`（更强大）

## 🔗 相关文件

- `claude.bat` - Windows批处理文件（推荐）
- `claude.cmd` - Windows命令文件（备用）
- `setup_claude_path.ps1` - PATH设置脚本

## 🎉 现在开始使用！

试试这个命令验证一切正常：

```bash
claude -p "你好Claude，我是一名Android开发者，请给我一些使用建议"
```

---

**享受与Claude Code的完美开发体验！** 🚀

*你现在拥有了业界标准的AI编程助手，支持所有原生功能！* 