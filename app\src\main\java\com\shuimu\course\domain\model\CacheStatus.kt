package com.shuimu.course.domain.model

enum class CacheStatus(val value: String) {
    NOT_CACHED("NOT_CACHED"),
    DOWNLOADING("DOWNLOADING"), 
    CACHED("CACHED"),
    FAILED("FAILED");
    
    companion object {
        fun fromString(value: String?): CacheStatus {
            if (value.isNullOrBlank()) return NOT_CACHED
            return values().find { it.value.equals(value, ignoreCase = true) } ?: NOT_CACHED
        }
    }
} 