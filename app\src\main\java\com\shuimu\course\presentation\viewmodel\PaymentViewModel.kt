package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.repository.PaymentRepository
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

data class PaymentState(
    val isLoading: Boolean = false,
    val paymentResult: String? = null,
    val error: String? = null
)

@HiltViewModel
class PaymentViewModel @Inject constructor(
    private val paymentRepository: PaymentRepository,
    private val dataSyncManager: com.shuimu.course.data.manager.DataSyncManager,
    private val dataLayerManager: com.shuimu.course.data.manager.DataLayerManager,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val _state = MutableStateFlow(PaymentState())
    val state = _state.asStateFlow()

    init {
        val itemId = savedStateHandle.get<String>("itemId")
        // Use itemId to fetch item details if necessary
    }

    fun processPayment(itemId: String, itemType: String, amount: Float) {
        viewModelScope.launch {
            paymentRepository.createPayment(itemId, itemType, amount).onEach { result ->
                _state.value = when (result) {
                    is Resource.Loading -> PaymentState(isLoading = true)
                    is Resource.Success -> {
                        // 购买成功后立即同步到服务器
                        android.util.Log.d("PaymentViewModel", "购买成功，开始同步数据")

                        // 从返回结果中提取订单ID（假设格式为 "Payment successful: orderId"）
                        val orderId = result.data?.substringAfter("Payment successful: ") ?: "unknown"

                        // 购买成功是关键数据，使用智能同步策略
                        launch {
                            try {
                                // 1. 传统同步方式（保持兼容性）
                                dataSyncManager.syncPurchaseImmediately(itemId, itemType, amount, orderId)

                                // 2. 使用数据分层管理器的智能同步
                                dataLayerManager.smartSync(
                                    data = mapOf(
                                        "itemId" to itemId,
                                        "itemType" to itemType,
                                        "amount" to amount,
                                        "orderId" to orderId
                                    ),
                                    priority = com.shuimu.course.data.manager.SyncPriority.CRITICAL
                                )

                                android.util.Log.d("PaymentViewModel", "购买数据同步成功")
                            } catch (e: Exception) {
                                android.util.Log.e("PaymentViewModel", "购买数据同步失败", e)
                                // 同步失败不影响用户体验，数据已保存到本地队列
                            }
                        }

                        PaymentState(isLoading = false, paymentResult = result.data)
                    }
                    is Resource.Error -> PaymentState(isLoading = false, error = result.message)
                }
            }.launchIn(this)
        }
    }

    fun processPayment() {
        val itemId = savedStateHandle.get<String>("itemId") ?: "unknown"
        val itemType = savedStateHandle.get<String>("itemType") ?: "unknown"
        val amount = savedStateHandle.get<Float>("amount") ?: 0f

        processPayment(itemId, itemType, amount)
    }

    fun clearPaymentState() {
        _state.value = PaymentState()
    }
} 