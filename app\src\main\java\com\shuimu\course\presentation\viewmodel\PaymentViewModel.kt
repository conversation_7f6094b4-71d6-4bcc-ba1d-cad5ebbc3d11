package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.repository.PaymentRepository
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

data class PaymentState(
    val isLoading: Boolean = false,
    val paymentResult: String? = null,
    val error: String? = null
)

@HiltViewModel
class PaymentViewModel @Inject constructor(
    private val paymentRepository: PaymentRepository,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val _state = MutableStateFlow(PaymentState())
    val state = _state.asStateFlow()

    init {
        val itemId = savedStateHandle.get<String>("itemId")
        // Use itemId to fetch item details if necessary
    }

    fun processPayment(itemId: String, itemType: String, amount: Float) {
        viewModelScope.launch {
            paymentRepository.createPayment(itemId, itemType, amount).onEach { result ->
                _state.value = when (result) {
                    is Resource.Loading -> PaymentState(isLoading = true)
                    is Resource.Success -> PaymentState(isLoading = false, paymentResult = result.data)
                    is Resource.Error -> PaymentState(isLoading = false, error = result.message)
                }
            }.launchIn(this)
        }
    }

    fun processPayment() {
        val itemId = savedStateHandle.get<String>("itemId") ?: "unknown"
        val itemType = savedStateHandle.get<String>("itemType") ?: "unknown"
        val amount = savedStateHandle.get<Float>("amount") ?: 0f

        processPayment(itemId, itemType, amount)
    }

    fun clearPaymentState() {
        _state.value = PaymentState()
    }
} 