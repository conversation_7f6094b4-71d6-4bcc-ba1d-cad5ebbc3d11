package com.shuimu.course.di;

import com.shuimu.course.data.remote.api.CacheApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideCacheApiFactory implements Factory<CacheApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideCacheApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public CacheApi get() {
    return provideCacheApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvideCacheApiFactory create(Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideCacheApiFactory(retrofitProvider);
  }

  public static CacheApi provideCacheApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideCacheApi(retrofit));
  }
}
