package com.shuimu.course.presentation.ui.components.base

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.draw.shadow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun SeriesShuimuCard(
    modifier: Modifier = Modifier,
    containerColor: Color = MaterialTheme.colorScheme.surface,
    content: @Composable ColumnScope.() -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .shadow(4.dp, RoundedCornerShape(16.dp), clip = false)   // 阴影但不裁剪
            .background(containerColor, RoundedCornerShape(16.dp))   // 背景圆角
            .graphicsLayer { clip = false }  // 🔥 关键修复：禁用裁剪，让徽章可以越界显示
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(0.dp) // 保持与原ShuimuCard一致的padding
        ) {
            content()
        }
    }
}

@Preview(name = "Series Shuimu Card Preview")
@Composable
fun SeriesShuimuCardPreview() {
    ShuimuCourseTheme {
        SeriesShuimuCard(modifier = Modifier.padding(16.dp)) {
            Text(text = "这是系列卡片的内容，允许内容突破右边界")
        }
    }
} 