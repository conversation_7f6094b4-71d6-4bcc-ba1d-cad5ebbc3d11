package com.shuimu.course.domain.model.orders

data class Order(
    val id: String,
    val title: String,
    val status: String,
    val price: Double,
    val date: String,
    val items: List<OrderItem>,
    val paymentMethod: String,
    val shareEarningsApplied: Double
)

data class OrderItem(
    val name: String,
    val price: Double
)

data class ConsumptionStats(
    val totalOrders: Int,
    val totalSpent: Double,
    val totalShareEarnings: Double
) 