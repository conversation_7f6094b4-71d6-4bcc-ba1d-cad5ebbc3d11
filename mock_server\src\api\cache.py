from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from ..utils.user_data import (
    load_user_data, 
    save_user_data, 
    update_video_cache_status,
    get_video_cache_status
)
import json
from pathlib import Path

router = APIRouter()

# 服务端配置
SERVER_CONFIG = {
    "max_concurrent_downloads": 5,  # 默认最大并发下载数
    "cache_directory": "/storage/emulated/0/VideoCache/",
    "supported_formats": ["mp4", "m3u8"]
}

class CacheStateUpdate(BaseModel):
    videoId: str
    isCached: bool
    localPath: Optional[str] = None
    deviceId: Optional[str] = None
    
class CacheProgressUpdate(BaseModel):
    videoId: str
    progress: int  # 0-100
    status: str   # DOWNLOADING, COMPLETED, FAILED, PAUSED
    
class ServerConfigUpdate(BaseModel):
    maxConcurrentDownloads: Optional[int] = None

def get_current_user_id():
    return "user_001"  # Mock 用户

@router.get("/cache/config")
def get_cache_config():
    """获取服务端缓存配置"""
    return {
        "maxConcurrentDownloads": SERVER_CONFIG["max_concurrent_downloads"],
        "cacheDirectory": SERVER_CONFIG["cache_directory"],
        "supportedFormats": SERVER_CONFIG["supported_formats"]
    }

@router.post("/cache/config")
def update_cache_config(config: ServerConfigUpdate):
    """更新服务端缓存配置（管理员功能）"""
    if config.maxConcurrentDownloads is not None:
        if 1 <= config.maxConcurrentDownloads <= 10:
            SERVER_CONFIG["max_concurrent_downloads"] = config.maxConcurrentDownloads
        else:
            raise HTTPException(status_code=400, detail="并发下载数必须在1-10之间")
    
    return {
        "message": "配置更新成功",
        "config": {
            "maxConcurrentDownloads": SERVER_CONFIG["max_concurrent_downloads"]
        }
    }

@router.post("/cache/state")
def sync_cache_state(
    cache_update: CacheStateUpdate, 
    user_id: str = Depends(get_current_user_id)
):
    """同步缓存状态到服务端"""
    try:
        update_video_cache_status(
            user_id=user_id,
            video_id=cache_update.videoId,
            is_cached=cache_update.isCached,
            local_path=cache_update.localPath
        )
        
        return {
            "message": "缓存状态同步成功",
            "videoId": cache_update.videoId,
            "isCached": cache_update.isCached,
            "syncedAt": "2025-06-25T10:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"同步失败: {str(e)}")

@router.get("/cache/state/{video_id}")
def get_cache_state(video_id: str, user_id: str = Depends(get_current_user_id)):
    """获取指定视频的缓存状态"""
    try:
        cache_info = get_video_cache_status(user_id, video_id)
        return cache_info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存状态失败: {str(e)}")

@router.get("/cache/states")
def get_all_cache_states(user_id: str = Depends(get_current_user_id)):
    """获取用户所有视频的缓存状态"""
    try:
        user_data = load_user_data(user_id)
        video_caches = user_data.get("videoCaches", {})
        
        cache_states = []
        for video_id, cache_info in video_caches.items():
            cache_states.append({
                "videoId": video_id,
                "isCached": cache_info.get("status") == "CACHED",
                "localPath": cache_info.get("localPath"),
                "size": cache_info.get("size", 0),
                "updatedAt": cache_info.get("updatedAt")
            })
        
        return {
            "cacheStates": cache_states,
            "totalCached": len(cache_states)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存状态列表失败: {str(e)}")

@router.post("/cache/progress")
def update_download_progress(
    progress_update: CacheProgressUpdate, 
    user_id: str = Depends(get_current_user_id)
):
    """更新下载进度（可选，主要用于多设备同步）"""
    try:
        user_data = load_user_data(user_id)
        video_caches = user_data.get("videoCaches", {})
        
        # 更新进度信息
        if progress_update.videoId not in video_caches:
            video_caches[progress_update.videoId] = {}
        
        video_caches[progress_update.videoId].update({
            "downloadProgress": progress_update.progress,
            "downloadStatus": progress_update.status,
            "lastProgressUpdate": "2025-06-25T10:00:00Z"
        })
        
        user_data["videoCaches"] = video_caches
        save_user_data(user_id, user_data)
        
        return {
            "message": "下载进度更新成功",
            "videoId": progress_update.videoId,
            "progress": progress_update.progress,
            "status": progress_update.status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新下载进度失败: {str(e)}")

@router.delete("/cache/state/{video_id}")
def remove_cache_state(video_id: str, user_id: str = Depends(get_current_user_id)):
    """删除缓存状态记录"""
    try:
        update_video_cache_status(
            user_id=user_id,
            video_id=video_id,
            is_cached=False
        )
        
        return {
            "message": "缓存状态删除成功",
            "videoId": video_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除缓存状态失败: {str(e)}")

@router.post("/cache/batch-sync")
def batch_sync_cache_states(
    cache_updates: List[CacheStateUpdate], 
    user_id: str = Depends(get_current_user_id)
):
    """批量同步缓存状态"""
    try:
        results = []
        for cache_update in cache_updates:
            update_video_cache_status(
                user_id=user_id,
                video_id=cache_update.videoId,
                is_cached=cache_update.isCached,
                local_path=cache_update.localPath
            )
            results.append({
                "videoId": cache_update.videoId,
                "success": True
            })
        
        return {
            "message": f"批量同步完成，成功同步 {len(results)} 个视频",
            "results": results,
            "syncedAt": "2025-06-25T10:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量同步失败: {str(e)}") 