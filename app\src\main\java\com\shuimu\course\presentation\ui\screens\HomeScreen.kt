package com.shuimu.course.presentation.ui.screens

import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.shuimu.course.presentation.navigation.Routes
import com.shuimu.course.presentation.ui.components.state.ErrorMessage
import com.shuimu.course.presentation.ui.components.state.LoadingIndicator
import com.shuimu.course.presentation.ui.components.display.CategoryCard
import com.shuimu.course.presentation.ui.components.display.SeriesCard
import com.shuimu.course.presentation.ui.components.display.VideoItem
import com.shuimu.course.presentation.ui.components.navigation.HomeTopBar
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.HomeViewModel
import com.shuimu.course.presentation.viewmodel.PaymentViewModel
import com.shuimu.course.presentation.viewmodel.PaymentState
import com.shuimu.course.presentation.ui.components.dialog.PaymentSlideUpSheet
import com.shuimu.course.presentation.ui.components.dialog.PaymentResult
import com.shuimu.course.presentation.ui.components.dialog.PaymentResultDialog
import com.shuimu.course.presentation.ui.components.dialog.SearchModal
import com.shuimu.course.presentation.ui.components.dialog.ShareModal
import com.shuimu.course.presentation.ui.components.dialog.CacheConfirmationDialog
import com.shuimu.course.domain.model.PurchaseInfo
import com.shuimu.course.domain.model.Video
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.collect
import androidx.compose.runtime.rememberCoroutineScope

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    navController: NavController,
    viewModel: HomeViewModel = hiltViewModel(),
    paymentViewModel: PaymentViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()
    val paymentState by paymentViewModel.state.collectAsState()
    val context = LocalContext.current

    // 调试：追踪重组次数
    android.util.Log.d("HomeScreen", "HomeScreen重组 - state.isLoading: ${state.isLoading}, paymentState.isLoading: ${paymentState.isLoading}")

    // 页面首次加载时清理支付状态，避免状态泄漏
    LaunchedEffect(Unit) {
        android.util.Log.d("HomeScreen", "HomeScreen首次加载，清理支付状态")
        paymentViewModel.clearPaymentState()
    }

    var searchOpen by remember { mutableStateOf(false) }
    var shareOpen by remember { mutableStateOf(false) }
    var purchaseInfo by remember { mutableStateOf<PurchaseInfo?>(null) }
    var showCacheDialog by remember { mutableStateOf(false) }
    var videoToCache by remember { mutableStateOf<Video?>(null) }
    
    // 🔥 本地展开状态管理，提供立即UI反馈
    val localExpandedStates = remember { mutableStateMapOf<String, Boolean>() }
    
    // Helper函数：获取展开状态（本地优先，然后是ViewModel）
    fun getExpandedState(id: String, defaultExpanded: Boolean): Boolean {
        return localExpandedStates[id] ?: viewModel.getExpandedState(id, defaultExpanded)
    }
    
    // Helper函数：设置展开状态（同时更新本地和ViewModel）
    fun setExpandedState(id: String, isExpanded: Boolean) {
        localExpandedStates[id] = isExpanded
        viewModel.setExpandedState(id, isExpanded)
    }

    // 监听支付状态变化 - 修复：只监听支付结果，避免不必要的重组
    LaunchedEffect(paymentState.paymentResult) {
        paymentState.paymentResult?.let { result ->
            // 支付成功，关闭支付框然后刷新数据
            android.util.Log.d("HomeScreen", "支付成功，刷新数据: $result")
            purchaseInfo = null

            // 延迟一下再刷新，避免UI冲突
            kotlinx.coroutines.delay(100)
            viewModel.getSeries()
        }
    }

    // 单独处理支付错误，避免重复触发
    LaunchedEffect(paymentState.error) {
        paymentState.error?.let { error ->
            android.util.Log.d("HomeScreen", "支付失败: $error")
            Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
        }
    }

    Scaffold(
        topBar = {
            HomeTopBar(
                userName = "张三", // TODO: bind from repository
                onSearchClick = { searchOpen = true },
                onShareClick = { shareOpen = true },
                onUserClick = { navController.navigate(Routes.PROFILE) }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            PullToRefreshBox(
                isRefreshing = state.isLoading,
                onRefresh = { viewModel.getSeries() },
                modifier = Modifier.fillMaxSize()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .graphicsLayer { clip = false }  // LazyColumn外层容器禁用裁剪
                ) {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 80.dp), // Add bottom padding for PaymentSlideUpSheet
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                        // 注意：clipToPadding 参数在当前版本中暂不可用，需要寻找替代方案解决右侧裁剪问题
                    ) {
                    items(state.series) { series ->
                        if (series.isPackage) {
                            // 全套课程：不可折叠，只用于购买
                            SeriesCard(
                                seriesTitle = series.title,
                                price = if (series.price != 0) "¥${series.price / 100}" else null,
                                isFree = series.isFree,
                                iconName = series.icon ?: "crown", // 全套课程默认使用皇冠
                                isExpanded = false,
                                showExpandIcon = false, // 全套系列不显示折叠图标
                                onHeaderClick = {
                                    // 只有"未购买且非免费"才触发PaymentSlideUpSheet
                                    if (!series.isPurchased && !series.isFree) {
                                        purchaseInfo = PurchaseInfo(
                                            id = series.id,
                                            name = series.title, 
                                            price = "¥${series.price / 100}",
                                            type = "series"
                                        )
                                    } else {
                                        // 已购买或免费的，关闭PaymentSlideUpSheet
                                        purchaseInfo = null
                                    }
                                }
                            ) {}
                        } else {
                            // 普通系列：可折叠，包含分类
                            val seriesExpanded = getExpandedState(series.id, series.defaultExpanded)
                            SeriesCard(
                                seriesTitle = series.title,
                                price = if (series.price != 0) "¥${series.price / 100}" else null,
                                isFree = series.isFree,
                                iconName = series.icon ?: when {
                                    series.isFree -> "play"
                                    series.title.contains("恋爱") -> "heart"
                                    series.title.contains("聊天") -> "comment"
                                    else -> "heart"
                                },
                                isExpanded = seriesExpanded,
                                showExpandIcon = true, // 普通系列显示折叠图标
                                onHeaderClick = {
                                    // 切换展开状态
                                    setExpandedState(series.id, !seriesExpanded)
                                    
                                    // PaymentSlideUpSheet逻辑：只有"未购买且非免费"才触发
                                    if (!series.isPurchased && !series.isFree) {
                                        purchaseInfo = PurchaseInfo(
                                            id = series.id,
                                            name = series.title, 
                                            price = "¥${series.price / 100}",
                                            type = "series"
                                        )
                                    } else {
                                        // 已购买或免费的，关闭PaymentSlideUpSheet
                                        purchaseInfo = null
                                    }
                                }
                            ) {
                                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                    series.categories.forEachIndexed { index, category ->
                                        val categoryExpanded = getExpandedState(category.id, category.defaultExpanded)

                                        // 🔥 数据职责分离：移除UI层的业务计算，所有数据由服务器提供

                                        // 分类卡片 - 直接使用服务器提供的数据，不做任何计算
                                        CategoryCard(
                                            category = category, // 🔥 直接传入category，服务器已计算好所有显示数据
                                            isExpanded = categoryExpanded,  // 传递展开状态
                                            isPurchasable = (category.isPurchased == true) || category.isFree,  // 传递购买状态
                                            cacheStatus = state.cacheStatus, // 传递实时缓存状态
                                            onVideoPlay = { video ->
                                                navController.navigate(Routes.VIDEO_PLAYER + "/${video.id}")
                                            },
                                            onVideoShowCacheDialog = { video ->
                                                videoToCache = video
                                                showCacheDialog = true
                                            },
                                            onVideoLockedClick = { video ->
                                                // 对于锁定的视频，显示分类购买弹窗
                                                purchaseInfo = PurchaseInfo(
                                                    id = category.id,
                                                    name = "${series.title}·${category.title}",
                                                    price = category.price?.let { "¥${it / 100}" } ?: "¥0",
                                                    type = "category"
                                                )
                                            },
                                            onClick = {
                                                // 切换展开状态
                                                setExpandedState(category.id, !categoryExpanded)
                                                
                                                // PaymentSlideUpSheet逻辑：完整的if-else
                                                if (category.isPurchased != true && series.isPurchased != true && !category.isFree) {
                                                    // 显示支付弹窗
                                                    purchaseInfo = PurchaseInfo(
                                                        id = category.id,
                                                        name = "${series.title}·${category.title}",
                                                        price = category.price?.let { "¥${it / 100}" } ?: "¥0",
                                                        type = "category"
                                                    )
                                                } else {
                                                    // 🔥 关键修复：已购买或免费的，隐藏支付框
                                                    purchaseInfo = null
                                                }
                                            }
                                        )

                                    }
                                }
                            }
                        }
                    }
                    }
                }
            }

            // ⭐ 统一的 Loading / Error 判断逻辑
            if (state.series.isEmpty()) {
                when {
                    state.isLoading -> LoadingIndicator()
                    state.error != null -> {
                        val errorMessage = state.error!!
                        ErrorMessage(
                            message = errorMessage,
                            onRetry = { viewModel.getSeries() }
                        )
                    }
                    else -> {
                        // 既没有加载也没有错误，但数据为空时，显示空状态
                        ErrorMessage(
                            message = "暂无课程数据",
                            onRetry = { viewModel.getSeries() }
                        )
                    }
                }
            } else {
                // 列表已经渲染，额外的错误提示显示在顶部不遮挡列表
                state.error?.let { error ->
                    Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
                }
            }

            // 缓存确认对话框
            if (showCacheDialog && videoToCache != null) {
                CacheConfirmationDialog(
                    videoTitle = videoToCache!!.title,
                    onDismissRequest = { showCacheDialog = false },
                    onConfirm = {
                        val video = videoToCache!!
                        android.util.Log.d("HomeScreen", "开始缓存视频: ${video.title} (${video.id})")
                        
                        // 立即显示"正在处理"消息
                        Toast.makeText(context, "正在处理...", Toast.LENGTH_SHORT).show()
                        
                        // 使用viewModelScope启动下载，避免compose作用域问题
                        viewModel.startVideoDownload(
                            videoId = video.id,
                            cloudUrl = video.cloudUrl,
                            videoTitle = video.title,
                            onResult = { message ->
                                // 在主线程显示结果
                                Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                            }
                        )
                        
                        // 关闭对话框
                        showCacheDialog = false
                    }
                )
            }

            if (searchOpen) {
                SearchModal(onDismissRequest = { searchOpen = false })
            }

            if (shareOpen) {
                ShareModal(onDismissRequest = { shareOpen = false })
            }

            // 支付滑动弹窗 - 直接放在底部，不创建覆盖层
            purchaseInfo?.let { info ->
                PaymentSlideUpSheet(
                    visible = true,
                    courseTitle = info.name,
                    price = info.price.removePrefix("¥"),
                    onPay = {
                        // 使用真实的支付API而不是硬编码模拟
                        paymentViewModel.processPayment(
                            itemId = info.id,
                            itemType = info.type,
                            amount = info.price.removePrefix("¥").toFloatOrNull() ?: 0f
                        )
                    },
                    onDismiss = { 
                        // 只有在非加载状态时才允许关闭
                        if (!paymentState.isLoading) {
                            purchaseInfo = null 
                        }
                    },
                    loading = paymentState.isLoading,
                    enableDragToClose = !paymentState.isLoading, // 加载时禁用拖拽关闭
                    modifier = Modifier.align(Alignment.BottomCenter)
                )
            }

            // 支付结果弹窗
            val paymentResult = when {
                paymentState.paymentResult != null -> PaymentResult(
                    success = true,
                    orderNumber = "SM${System.currentTimeMillis()}",
                    courseTitle = purchaseInfo?.name ?: "课程",
                    amount = purchaseInfo?.price?.removePrefix("¥") ?: "0"
                )
                paymentState.error != null -> PaymentResult(
                    success = false,
                    message = paymentState.error
                )
                else -> null
            }

            paymentResult?.let { result ->
                PaymentResultDialog(
                    result = result,
                    onDismiss = { 
                        // 清除支付状态
                        paymentViewModel.clearPaymentState()
                        purchaseInfo = null
                    },
                    onViewOrders = { 
                        paymentViewModel.clearPaymentState()
                        purchaseInfo = null
                        navController.navigate(Routes.ORDERS)
                    },
                    onStartStudy = { 
                        paymentViewModel.clearPaymentState()
                        purchaseInfo = null
                        // 导航到学习页面或刷新数据
                        viewModel.getSeries()
                    },
                    onRetry = { 
                        paymentViewModel.clearPaymentState()
                        // 重新显示支付弹窗
                        // purchaseInfo 保持不变，会自动重新显示
                    }
                )
            }
        }
    }
}

@Preview
@Composable
fun HomeScreenPreview() {
    ShuimuCourseTheme {
        val navController = rememberNavController()
        HomeScreen(navController = navController)
    }
} 