package com.shuimu.course.presentation.ui.components.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.components.base.ShuimuTextField
import com.shuimu.course.presentation.ui.components.display.SearchItem
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.SearchViewModel

@Composable
fun SearchModal(
    onDismissRequest: () -> Unit,
    viewModel: SearchViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()

    Dialog(onDismissRequest = onDismissRequest) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            shape = MaterialTheme.shapes.large
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("搜索课程", style = MaterialTheme.typography.titleLarge)
                    IconButton(onClick = onDismissRequest) {
                        Icon(Icons.Default.Close, contentDescription = "Close Search")
                    }
                }

                Spacer(Modifier.height(16.dp))

                // Search Field
                ShuimuTextField(
                    value = state.searchQuery,
                    onValueChange = { viewModel.updateSearchQuery(it) },
                    label = "搜索",
                    placeholder = "搜索视频标题..."
                )

                Spacer(Modifier.height(16.dp))

                if (state.isLoading) {
                    Box(modifier = Modifier.fillMaxWidth()) {
                        CircularProgressIndicator()
                    }
                } else if (state.searchQuery.isNotBlank()) {
                    // Results List
                    LazyColumn(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                        items(state.searchResults) { result ->
                            SearchItem(
                                title = result.title,
                                category = result.description,
                                isPurchased = result.type == "purchased",
                                onClick = { 
                                    viewModel.addToHistory(state.searchQuery)
                                    // TODO: 导航到具体内容
                                }
                            )
                        }
                    }
                } else {
                    // History Section
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("搜索历史", style = MaterialTheme.typography.titleMedium)
                        if (state.searchHistory.isNotEmpty()) {
                            TextButton(onClick = { viewModel.clearHistory() }) {
                                Text("清除")
                            }
                        }
                    }
                    Spacer(Modifier.height(8.dp))
                    
                    if (state.searchHistory.isEmpty()) {
                        Text("暂无搜索历史", style = MaterialTheme.typography.bodyMedium)
                    } else {
                        Column {
                            state.searchHistory.forEach { historyItem ->
                                TextButton(
                                    onClick = { viewModel.updateSearchQuery(historyItem) },
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text(
                                        text = historyItem,
                                        modifier = Modifier.padding(vertical = 4.dp)
                                    )
                                }
                            }
                        }
                    }
                }

                // 错误显示
                state.error?.let { error ->
                    Spacer(Modifier.height(8.dp))
                    Text(
                        text = "错误: $error",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun SearchModalPreview() {
    ShuimuCourseTheme {
        // 预览时使用简化版本，不依赖ViewModel
        Surface(
            modifier = Modifier.fillMaxSize(),
            shape = MaterialTheme.shapes.large
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text("搜索课程", style = MaterialTheme.typography.titleLarge)
                Spacer(Modifier.height(16.dp))
                Text("预览模式 - 搜索功能需要运行时数据")
            }
        }
    }
} 