from pydantic import BaseModel
from typing import List, Optional

class ProductItem(BaseModel):
    id: str
    name: str
    description: Optional[str] = None

class Product(BaseModel):
    id: str
    title: str
    description: str
    price: int  # 价格，单位：分
    originalPrice: Optional[int] = None  # 原价，单位：分（如有促销时使用）
    itemContents: List[str]
    features: List[str]
    duration: str  # 课程总时长描述（如"30小时"）
    studentCount: int  # 学员数量 