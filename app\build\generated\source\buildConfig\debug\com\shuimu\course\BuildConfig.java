/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.shuimu.course;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String APPLICATION_ID = "com.shuimu.course";
  public static final String BUILD_TYPE = "debug";
  public static final int VERSION_CODE = 1;
  public static final String VERSION_NAME = "1.0";
  // Field from default config.
  public static final String API_BASE_URL = "https://api.shuimu.us.kg/api/";
  // Field from default config.
  public static final String API_HOST_NAME = "api.shuimu.us.kg";
  // Field from default config.
  public static final String API_SERVER_IP = "************";
  // Field from default config.
  public static final String MOCK_USER_ID = "user_001";
}
