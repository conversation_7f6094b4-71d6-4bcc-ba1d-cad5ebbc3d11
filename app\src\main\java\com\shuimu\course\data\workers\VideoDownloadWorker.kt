package com.shuimu.course.data.workers

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.*
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.domain.usecase.cache.SyncCacheStateUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL

@HiltWorker
class VideoDownloadWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val cacheRepository: CacheRepository,
    private val syncCacheStateUseCase: SyncCacheStateUseCase,
    private val enhancedDownloadEngine: EnhancedDownloadEngine,
    private val downloadErrorHandler: com.shuimu.course.data.error.DownloadErrorHandler
) : CoroutineWorker(context, workerParams) {

    companion object {
        const val KEY_VIDEO_ID = "video_id"
        const val KEY_CLOUD_URL = "cloud_url"
        const val KEY_VIDEO_TITLE = "video_title"
        
        // 创建下载请求
        fun createDownloadRequest(
            videoId: String,
            cloudUrl: String,
            videoTitle: String = ""
        ): OneTimeWorkRequest {
            val inputData = Data.Builder()
                .putString(KEY_VIDEO_ID, videoId)
                .putString(KEY_CLOUD_URL, cloudUrl)
                .putString(KEY_VIDEO_TITLE, videoTitle)
                .build()

            return OneTimeWorkRequestBuilder<VideoDownloadWorker>()
                .setInputData(inputData)
                .setConstraints(
                    Constraints.Builder()
                        // 暂时移除网络约束，在Worker内部检查网络状态
                        .setRequiresBatteryNotLow(false)  // 允许低电量时下载
                        .setRequiresCharging(false)       // 不要求充电
                        .setRequiresDeviceIdle(false)     // 不要求设备空闲
                        .setRequiresStorageNotLow(false)  // 允许存储空间较低时下载
                        .build()
                )
                .addTag("video_download")
                .addTag("video_$videoId")
                .build()
        }
    }

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        val videoId = inputData.getString(KEY_VIDEO_ID) ?: return@withContext Result.failure()
        val cloudUrl = inputData.getString(KEY_CLOUD_URL) ?: return@withContext Result.failure()
        val videoTitle = inputData.getString(KEY_VIDEO_TITLE) ?: ""

        android.util.Log.d("VideoDownloadWorker", "=== Worker 开始执行 ===")
        android.util.Log.d("VideoDownloadWorker", "videoId: $videoId")
        android.util.Log.d("VideoDownloadWorker", "cloudUrl: $cloudUrl")
        android.util.Log.d("VideoDownloadWorker", "videoTitle: $videoTitle")
        
        // 检查网络连接状态
        val connectivityManager = applicationContext.getSystemService(android.content.Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        if (activeNetwork == null || networkCapabilities == null) {
            android.util.Log.w("VideoDownloadWorker", "无网络连接，任务失败")
            cacheRepository.updateDownloadStatus(videoId, CacheDownloadStatus.FAILED, "无网络连接")
            return@withContext Result.failure()
        }
        
        val hasInternet = networkCapabilities.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET)
        android.util.Log.d("VideoDownloadWorker", "网络状态检查: hasInternet=$hasInternet")

        try {
            // 检查当前状态
            val currentCache = cacheRepository.getCacheInfoSync(videoId)
            android.util.Log.d("VideoDownloadWorker", "当前缓存状态: ${currentCache?.status}")
            
            if (currentCache?.status == com.shuimu.course.domain.model.CacheStatus.DOWNLOADED) {
                android.util.Log.d("VideoDownloadWorker", "视频已下载完成，退出Worker")
                return@withContext Result.success()
            }

            // 更新状态为下载中
            android.util.Log.d("VideoDownloadWorker", "更新状态为DOWNLOADING")
            cacheRepository.updateDownloadStatus(videoId, com.shuimu.course.domain.model.CacheStatus.DOWNLOADING)
            
            // 创建缓存目录
            val cacheDir = File(applicationContext.getExternalFilesDir(null), "VideoCache")
            if (!cacheDir.exists()) {
                android.util.Log.d("VideoDownloadWorker", "创建缓存目录: ${cacheDir.absolutePath}")
                cacheDir.mkdirs()
            }

            // 生成本地文件路径
            val localFile = File(cacheDir, "${videoId}.mp4")
            val localPath = localFile.absolutePath
            android.util.Log.d("VideoDownloadWorker", "本地文件路径: $localPath")

            // 执行增强下载
            android.util.Log.d("VideoDownloadWorker", "开始执行增强下载")
            val downloadResult = downloadVideoEnhanced(videoId, cloudUrl, localFile)
            android.util.Log.d("VideoDownloadWorker", "下载结果: ${downloadResult.isSuccess}")

            if (downloadResult.isSuccess) {
                // 下载成功，更新状态
                android.util.Log.d("VideoDownloadWorker", "下载成功，更新状态为DOWNLOADED")
                cacheRepository.updateDownloadStatus(videoId, com.shuimu.course.domain.model.CacheStatus.DOWNLOADED)
                
                // 更新缓存信息
                val updatedCache = com.shuimu.course.domain.model.CachedVideo(
                    videoId = videoId,
                    title = videoTitle,
                    localPath = localPath,
                    downloadedBytes = localFile.length(),
                    totalBytes = localFile.length(),
                    status = com.shuimu.course.domain.model.CacheStatus.DOWNLOADED,
                    progress = 100,
                    cloudUrl = cloudUrl,
                    updatedAt = System.currentTimeMillis(),
                    priority = 0,
                    retryCount = 0,
                    lastRetryTime = 0L
                )
                cacheRepository.upsertCacheInfo(updatedCache)

                // 同步到服务端
                syncCacheStateUseCase.autoSync(videoId, com.shuimu.course.domain.model.CacheStatus.DOWNLOADED)
                
                // 检查是否有待下载的任务
                processNextPendingDownload()
                
                android.util.Log.d("VideoDownloadWorker", "Worker执行成功")
                Result.success()
            } else {
                // 下载失败
                android.util.Log.e("VideoDownloadWorker", "下载失败: ${downloadResult.exceptionOrNull()?.message}")
                cacheRepository.updateDownloadStatus(
                    videoId,
                    com.shuimu.course.domain.model.CacheStatus.FAILED,
                    downloadResult.exceptionOrNull()?.message ?: "下载失败"
                )
                Result.failure()
            }

        } catch (e: Exception) {
            // 使用增强错误处理
            android.util.Log.e("VideoDownloadWorker", "Worker执行异常", e)

            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            val currentRetryCount = cacheInfo?.retryCount ?: 0

            val errorResult = downloadErrorHandler.handleDownloadError(videoId, e, currentRetryCount)
            if (errorResult.isSuccess) {
                val retryResult = errorResult.getOrNull()
                if (retryResult?.shouldRetry == true) {
                    android.util.Log.d("VideoDownloadWorker", "将重试下载: $videoId")
                    // WorkManager会根据退避策略自动重试
                    return@withContext Result.retry()
                }
            }

            Result.failure()
        }
    }

    /**
     * 使用增强下载引擎下载视频
     */
    private suspend fun downloadVideoEnhanced(videoId: String, cloudUrl: String, localFile: File): Result<Unit> {
        return try {
            android.util.Log.d("VideoDownloadWorker", "开始增强下载")
            android.util.Log.d("VideoDownloadWorker", "URL: $cloudUrl")
            android.util.Log.d("VideoDownloadWorker", "目标文件: ${localFile.absolutePath}")

            // 配置下载参数
            val config = EnhancedDownloadEngine.DownloadConfig(
                enableResume = true,
                maxThreads = 2, // Worker中使用较少线程避免资源竞争
                speedLimitBytesPerSecond = 2 * 1024 * 1024, // 2MB/s
                requireWifi = false, // 允许移动网络下载
                retryCount = 3,
                retryDelay = 2000L
            )

            // 执行下载，带进度回调
            enhancedDownloadEngine.downloadFile(
                url = cloudUrl,
                outputFile = localFile,
                config = config,
                onProgress = { progress ->
                    // 更新下载进度到数据库
                    kotlinx.coroutines.runBlocking {
                        cacheRepository.updateDownloadProgress(
                            videoId = videoId,
                            progress = progress.progress,
                            downloadedBytes = progress.downloadedBytes,
                            totalBytes = progress.totalBytes
                        )
                    }
                    android.util.Log.d("VideoDownloadWorker", "下载进度: ${progress.progress}% (${progress.downloadedBytes}/${progress.totalBytes})")
                },
                onNetworkWaiting = {
                    // 网络等待状态
                    kotlinx.coroutines.runBlocking {
                        cacheRepository.updateDownloadStatus(
                            videoId,
                            com.shuimu.course.domain.model.CacheStatus.WAITING_NETWORK,
                            "等待合适的网络环境"
                        )
                    }
                    android.util.Log.d("VideoDownloadWorker", "等待网络环境")
                }
            )
        } catch (e: Exception) {
            android.util.Log.e("VideoDownloadWorker", "增强下载异常", e)
            Result.failure(e)
        }
    }

    /**
     * 传统下载方法（保留作为备用）
     */
    private suspend fun downloadVideo(videoId: String, cloudUrl: String, localFile: File): Boolean {
        return try {
            android.util.Log.d("VideoDownloadWorker", "downloadVideo开始")
            android.util.Log.d("VideoDownloadWorker", "检查URL: $cloudUrl")

            // 对所有URL都使用真实下载逻辑，让网络请求自然地成功或失败
            android.util.Log.d("VideoDownloadWorker", "开始真实下载")
            realDownload(cloudUrl, localFile, videoId)
        } catch (e: Exception) {
            android.util.Log.e("VideoDownloadWorker", "downloadVideo异常", e)
            false
        }
    }



    private suspend fun realDownload(cloudUrl: String, localFile: File, videoId: String): Boolean {
        return try {
            android.util.Log.d("VideoDownloadWorker", "开始连接: $cloudUrl")
            
            val url = URL(cloudUrl)
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 10000 // 10秒连接超时
            connection.readTimeout = 30000 // 30秒读取超时
            connection.connect()

            val responseCode = connection.responseCode
            android.util.Log.d("VideoDownloadWorker", "HTTP响应码: $responseCode")

            if (responseCode != HttpURLConnection.HTTP_OK) {
                android.util.Log.e("VideoDownloadWorker", "HTTP请求失败，响应码: $responseCode")
                connection.disconnect()
                return false
            }

            val totalSize = connection.contentLength.toLong()
            android.util.Log.d("VideoDownloadWorker", "文件总大小: ${totalSize / 1024 / 1024}MB")
            
            var downloadedSize = 0L

            val inputStream: InputStream = connection.inputStream
            val outputStream = FileOutputStream(localFile)

            try {
                val buffer = ByteArray(8192)
                var bytesRead: Int
                var lastProgressUpdate = 0

                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    if (isStopped) {
                        android.util.Log.w("VideoDownloadWorker", "下载被中断")
                        return false
                    }

                    outputStream.write(buffer, 0, bytesRead)
                    downloadedSize += bytesRead

                    // 计算进度
                    val progress = if (totalSize > 0) {
                        ((downloadedSize * 100) / totalSize).toInt()
                    } else {
                        // 对于未知大小的文件，根据下载量估算
                        minOf(((downloadedSize / 1024 / 1024) * 10).toInt(), 90)
                    }

                    // 只在进度有明显变化时更新（避免过于频繁的更新）
                    if (progress > lastProgressUpdate) {
                        lastProgressUpdate = progress
                        android.util.Log.d("VideoDownloadWorker", "下载进度: $progress% (${downloadedSize / 1024 / 1024}MB)")
                        
                        cacheRepository.updateDownloadProgress(
                            videoId = videoId,
                            progress = progress,
                            downloadedBytes = downloadedSize,
                            totalBytes = totalSize
                        )

                        // 发送进度通知给UI
                        setProgress(
                            Data.Builder()
                                .putInt("progress", progress)
                                .putString("videoId", videoId)
                                .putLong("downloadedBytes", downloadedSize)
                                .putLong("totalBytes", totalSize)
                                .build()
                        )
                    }
                }

                android.util.Log.d("VideoDownloadWorker", "下载完成，总大小: ${downloadedSize / 1024 / 1024}MB")
                return true
            } finally {
                inputStream.close()
                outputStream.close()
                connection.disconnect()
            }
        } catch (e: java.net.UnknownHostException) {
            android.util.Log.e("VideoDownloadWorker", "DNS解析失败，无法找到主机: ${e.message}")
            false
        } catch (e: java.net.ConnectException) {
            android.util.Log.e("VideoDownloadWorker", "连接失败: ${e.message}")
            false
        } catch (e: java.net.SocketTimeoutException) {
            android.util.Log.e("VideoDownloadWorker", "连接超时: ${e.message}")
            false
        } catch (e: java.io.IOException) {
            android.util.Log.e("VideoDownloadWorker", "IO异常: ${e.message}")
            false
        } catch (e: Exception) {
            android.util.Log.e("VideoDownloadWorker", "下载异常: ${e.message}", e)
            false
        }
    }

    /**
     * 处理下一个待下载任务
     */
    private suspend fun processNextPendingDownload() {
        // 获取服务端配置
        val config = cacheRepository.getCacheConfig().getOrNull()
        val maxConcurrent = config?.maxConcurrentDownloads ?: 5
        
        // 检查当前下载数量
        val currentDownloading = cacheRepository.getDownloadingCount()
        
        if (currentDownloading < maxConcurrent) {
            // 获取待下载队列中的第一个任务
            cacheRepository.getPendingCaches().collect { pendingCaches ->
                val nextCache = pendingCaches.firstOrNull()
                if (nextCache != null) {
                    // 更新状态为下载中
                    cacheRepository.updateDownloadStatus(
                        nextCache.videoId,
                        com.shuimu.course.domain.model.CacheStatus.DOWNLOADING
                    )
                    
                    // 创建新的下载任务
                    val newRequest = createDownloadRequest(
                        videoId = nextCache.videoId,
                        cloudUrl = nextCache.cloudUrl ?: "",
                        videoTitle = nextCache.title
                    )
                    
                    // 提交给 WorkManager
                    WorkManager.getInstance(applicationContext)
                        .enqueue(newRequest)
                }
            }
        }
    }
} 