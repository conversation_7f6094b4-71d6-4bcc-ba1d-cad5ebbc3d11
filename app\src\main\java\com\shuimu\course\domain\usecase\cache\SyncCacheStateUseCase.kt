package com.shuimu.course.domain.usecase.cache

import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.CacheDownloadStatus
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * 同步缓存状态到服务端 UseCase
 */
class SyncCacheStateUseCase @Inject constructor(
    private val cacheRepository: CacheRepository
) {
    
    /**
     * 同步单个视频的缓存状态
     */
    suspend operator fun invoke(videoId: String): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo == null) {
                emit(Resource.Error("缓存信息不存在"))
                return@flow
            }
            
            val isCached = cacheInfo.status == CacheDownloadStatus.DOWNLOADED
            val result = cacheRepository.syncCacheStateToServer(
                videoId = videoId,
                isCached = isCached,
                localPath = if (isCached) cacheInfo.localPath else null
            )
            
            if (result.isSuccess) {
                emit(Resource.Success(Unit))
            } else {
                emit(Resource.Error("同步失败：${result.exceptionOrNull()?.message}"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error("同步失败：${e.message}"))
        }
    }
    
    /**
     * 批量同步所有缓存状态
     */
    suspend fun syncAllCacheStates(): Flow<Resource<Unit>> = flow {
        try {
            emit(Resource.Loading())
            
            // 获取所有缓存信息
            val allCaches = mutableListOf<CachedVideo>()
            cacheRepository.getAllCacheInfo().collect { caches ->
                allCaches.clear()
                allCaches.addAll(caches)
            }
            
            if (allCaches.isEmpty()) {
                emit(Resource.Success(Unit))
                return@flow
            }
            
            val result = cacheRepository.batchSyncCacheStates(allCaches)
            
            if (result.isSuccess) {
                emit(Resource.Success(Unit))
            } else {
                emit(Resource.Error("批量同步失败：${result.exceptionOrNull()?.message}"))
            }
            
        } catch (e: Exception) {
            emit(Resource.Error("批量同步失败：${e.message}"))
        }
    }
    
    /**
     * 自动同步状态变化
     * 当本地缓存状态发生变化时自动调用
     */
    suspend fun autoSync(videoId: String, newStatus: CacheDownloadStatus) {
        // 只同步关键状态变化：下载完成或删除缓存
        when (newStatus) {
            CacheDownloadStatus.DOWNLOADED -> {
                val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
                cacheInfo?.let {
                    cacheRepository.syncCacheStateToServer(
                        videoId = videoId,
                        isCached = true,
                        localPath = it.localPath
                    )
                }
            }
            
            CacheDownloadStatus.NOT_CACHED -> {
                cacheRepository.syncCacheStateToServer(
                    videoId = videoId,
                    isCached = false
                )
            }
            
            else -> {
                // 其他状态不需要立即同步
            }
        }
    }
} 