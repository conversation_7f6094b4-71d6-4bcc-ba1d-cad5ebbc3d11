package com.shuimu.course.di

import com.shuimu.course.data.repository.CacheRepositoryImpl
import com.shuimu.course.data.repository.PaymentRepositoryImpl
import com.shuimu.course.data.repository.SeriesRepositoryImpl
import com.shuimu.course.data.repository.UserRepositoryImpl
import com.shuimu.course.data.repository.PlayProgressRepositoryImpl
import com.shuimu.course.data.repository.ShareRepositoryImpl
import com.shuimu.course.data.repository.UserPreferenceRepositoryImpl
import com.shuimu.course.data.repository.VideoRepositoryImpl
import com.shuimu.course.data.repository.SearchRepositoryImpl
import com.shuimu.course.data.repository.PlaylistRepositoryImpl
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.domain.repository.PaymentRepository
import com.shuimu.course.domain.repository.PlayProgressRepository
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.repository.ShareRepository
import com.shuimu.course.domain.repository.UserRepository
import com.shuimu.course.domain.repository.UserPreferenceRepository
import com.shuimu.course.domain.repository.VideoRepository
import com.shuimu.course.domain.repository.SearchRepository
import com.shuimu.course.domain.repository.PlaylistRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindSeriesRepository(impl: SeriesRepositoryImpl): SeriesRepository

    @Binds
    @Singleton
    abstract fun bindUserRepository(impl: UserRepositoryImpl): UserRepository

    @Binds
    @Singleton
    abstract fun bindPaymentRepository(impl: PaymentRepositoryImpl): PaymentRepository

    @Binds
    @Singleton
    abstract fun bindCacheRepository(impl: CacheRepositoryImpl): CacheRepository

    @Binds
    @Singleton
    abstract fun bindPlayProgressRepository(impl: PlayProgressRepositoryImpl): PlayProgressRepository

    @Binds
    @Singleton
    abstract fun bindShareRepository(impl: ShareRepositoryImpl): ShareRepository

    @Binds
    @Singleton
    abstract fun bindUserPreferenceRepository(impl: UserPreferenceRepositoryImpl): UserPreferenceRepository

    @Binds
    @Singleton
    abstract fun bindVideoRepository(impl: VideoRepositoryImpl): VideoRepository

    @Binds
    @Singleton
    abstract fun bindSearchRepository(impl: SearchRepositoryImpl): SearchRepository

    @Binds
    @Singleton
    abstract fun bindPlaylistRepository(impl: PlaylistRepositoryImpl): PlaylistRepository
} 