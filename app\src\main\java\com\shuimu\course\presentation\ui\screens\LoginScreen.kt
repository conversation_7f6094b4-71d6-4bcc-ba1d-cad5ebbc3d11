package com.shuimu.course.presentation.ui.screens

import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.components.base.ShuimuButton
import com.shuimu.course.presentation.ui.components.base.ShuimuTextField
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.LoginViewModel

@Composable
fun LoginScreen(
    viewModel: LoginViewModel = hiltViewModel(),
    onLoginSuccess: () -> Unit
) {
    val state by viewModel.state.collectAsState()
    val context = LocalContext.current

    var phone by remember { mutableStateOf("") }
    var code by remember { mutableStateOf("") }

    LaunchedEffect(key1 = state.loginSuccess) {
        if (state.loginSuccess) {
            onLoginSuccess()
        }
    }

    LaunchedEffect(key1 = state.loginError) {
        state.loginError?.let {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
        }
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier.padding(24.dp),
            elevation = CardDefaults.cardElevation(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text("用户登录", style = MaterialTheme.typography.headlineSmall)

                ShuimuTextField(
                    value = phone,
                    onValueChange = { phone = it },
                    label = "手机号"
                )

                Row(verticalAlignment = Alignment.CenterVertically) {
                    ShuimuTextField(
                        value = code,
                        onValueChange = { code = it },
                        label = "验证码",
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = { viewModel.startVerificationCountdown() },
                        enabled = state.countdownSeconds == 0
                    ) {
                        Text(
                            if (state.countdownSeconds > 0) "${state.countdownSeconds}s" else "获取验证码"
                        )
                    }
                }

                ShuimuButton(
                    onClick = { viewModel.onLogin(phone, code) },
                    text = if (state.isLoading) "登录中..." else "登录",
                    enabled = !state.isLoading
                )

                Text(
                    "登录即表示同意用户协议和隐私政策",
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Preview
@Composable
fun LoginScreenPreview() {
    ShuimuCourseTheme {
        LoginScreen(onLoginSuccess = {})
    }
} 