package com.shuimu.course.data.remote.dto

import com.google.gson.annotations.SerializedName

data class MonthlyEarningDto(
    @SerializedName("month") val month: String,
    @SerializedName("earnings") val earnings: Double
)

data class EarningsResponseDto(
    @SerializedName("total_earnings") val totalEarnings: Double,
    @SerializedName("available_for_withdrawal") val availableForWithdrawal: Double,
    @SerializedName("withdrawn") val withdrawn: Double,
    @SerializedName("shared_users_count") val sharedUsersCount: Int,
    @SerializedName("monthly_earnings") val monthlyEarnings: List<MonthlyEarningDto>
) 