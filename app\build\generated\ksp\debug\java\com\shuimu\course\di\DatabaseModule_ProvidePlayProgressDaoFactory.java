package com.shuimu.course.di;

import com.shuimu.course.data.local.dao.PlayProgressDao;
import com.shuimu.course.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvidePlayProgressDaoFactory implements Factory<PlayProgressDao> {
  private final Provider<AppDatabase> dbProvider;

  public DatabaseModule_ProvidePlayProgressDaoFactory(Provider<AppDatabase> dbProvider) {
    this.dbProvider = dbProvider;
  }

  @Override
  public PlayProgressDao get() {
    return providePlayProgressDao(dbProvider.get());
  }

  public static DatabaseModule_ProvidePlayProgressDaoFactory create(
      Provider<AppDatabase> dbProvider) {
    return new DatabaseModule_ProvidePlayProgressDaoFactory(dbProvider);
  }

  public static PlayProgressDao providePlayProgressDao(AppDatabase db) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePlayProgressDao(db));
  }
}
