<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.shuimu.us.kg</domain>
    </domain-config>

    <!-- 允许通过IP地址访问HTTPS服务 -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="false">************</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>

    <!-- 为本地开发环境添加的规则 -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">********</domain> <!-- 安卓模拟器访问宿主机 -->
        <domain includeSubdomains="true">127.0.0.1</domain> <!-- 本地回环地址 -->
    </domain-config>
</network-security-config>
