package com.shuimu.course.presentation.viewmodel

import android.util.Log
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.PlayProgress
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.PlayProgressRepository
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.repository.VideoRepository
import com.shuimu.course.domain.repository.PlaylistRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

// This represents the state of the player itself, as requested in the instructions
enum class PlayerStatus {
    IDLE,
    BUFFERING,
    PLAYING,
    PAUSED,
    ENDED,
    ERROR
}

data class VideoPlayerState(
    val video: Video? = null,
    val playlist: List<Video> = emptyList(),
    val playerStatus: PlayerStatus = PlayerStatus.IDLE,
    val initialSeekPosition: Long? = null, // To signal initial seek
    val isLoading: Boolean = false,
    val error: String? = null
)

@HiltViewModel
class VideoPlayerViewModel @Inject constructor(
    private val seriesRepository: SeriesRepository,
    private val playProgressRepository: PlayProgressRepository,
    private val videoRepository: VideoRepository,
    private val playlistRepository: PlaylistRepository,
    private val dataLayerManager: com.shuimu.course.data.manager.DataLayerManager,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val _state = MutableStateFlow(VideoPlayerState())
    val state = _state.asStateFlow()

    private val videoId: String? = savedStateHandle.get<String>("videoId")

    init {
        if (videoId != null) {
            loadVideoAndPlaylist(videoId)
        } else {
            _state.value = VideoPlayerState(error = "Video ID not found.")
        }
    }

    private fun loadVideoAndPlaylist(videoId: String) {
        viewModelScope.launch {
            try {
                _state.value = _state.value.copy(isLoading = true)
                
                // 并行加载视频和播放列表数据
                val videoData = videoRepository.getVideoById(videoId)
                val playlistData = playlistRepository.getPlaylistForVideo(videoId)
                
                videoData?.let {
                    _state.value = _state.value.copy(
                        video = it,
                        playlist = playlistData,
                        initialSeekPosition = (it.progress * it.duration).toLong()
                    )
                }
            } catch (e: Exception) {
                Log.e("VideoPlayerViewModel", "Failed to load video: ${e.message}")
                _state.value = _state.value.copy(error = e.message)
            } finally {
                _state.value = _state.value.copy(isLoading = false)
            }
        }
    }

    fun onPlayerStatusChanged(status: PlayerStatus) {
        _state.value = _state.value.copy(playerStatus = status)
    }

    fun saveProgress(currentPosition: Long, totalDuration: Long) {
        if (videoId == null || currentPosition <= 0 || totalDuration <= 0) return
        
        viewModelScope.launch {
            val currentWatchCount = state.value.video?.watchCount ?: 0
            val progress = PlayProgress(
                videoId = videoId,
                watchCount = currentWatchCount + 1,
                lastPositionSeconds = currentPosition / 1000,
                totalDurationSeconds = totalDuration / 1000,
                updatedAt = System.currentTimeMillis()
            )
            playProgressRepository.saveProgress(progress)
        }
    }

    fun onInitialSeekDone() {
        _state.value = _state.value.copy(initialSeekPosition = null)
    }
} 