package com.shuimu.course.data.remote.dto

import com.google.gson.annotations.SerializedName

data class VideoDto(
    @SerializedName("id") val id: String,
    @SerializedName("title") val title: String,
    @SerializedName("duration") val duration: Int, // 视频总时长，单位：秒
    @SerializedName("description") val description: String,
    @SerializedName("categoryId") val categoryId: String, // 所属分类ID
    @SerializedName("watch_count") val watchCount: Int?, // 个人观看次数 (0-10次，用于徽章等级)
    @SerializedName("playCount") val playCount: Long?, // 全局播放量（所有用户观看总次数）
    @SerializedName("cloudUrl") val cloudUrl: String,
    @SerializedName("cache_status") val cacheStatus: String? = null,
    
    // 🔥 统一进度参数：观看完成度 (0.0-1.0，用于UI显示)
    @SerializedName("progress") val progress: Float
) 