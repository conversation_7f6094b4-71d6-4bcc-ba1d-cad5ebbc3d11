from fastapi import APIRouter
from typing import List
from ..models.product import Product, ProductItem

router = APIRouter()

# Mock products data
PRODUCTS_DATA = [
    Product(
        id="love-guide-series",
        title="恋爱宝典系列",
        description="完整的恋爱指导课程",
        price=60000,  # ¥600，单位：分
        originalPrice=80000,  # ¥800，单位：分
        itemContents=[
            "恋爱心理学基础",
            "沟通技巧进阶", 
            "约会策略指南",
            "长期关系维护",
            "冲突处理艺术"
        ],
        features=[
            "永久访问权限",
            "高清视频内容",
            "专家在线答疑",
            "学习进度跟踪",
            "证书颁发"
        ],
        duration="总计8小时课程",
        studentCount=2847  # 学员数量
    ),
    Product(
        id="communication-skills",
        title="沟通技巧大师班",
        description="提升人际沟通能力",
        price=29900,  # ¥299，单位：分
        originalPrice=39900,  # ¥399，单位：分
        itemContents=[
            "基础沟通理论",
            "倾听技巧训练",
            "表达能力提升",
            "冲突化解方法"
        ],
        features=[
            "3个月访问权限",
            "实战案例分析",
            "练习题库",
            "进度报告"
        ],
        duration="总计4小时课程", 
        studentCount=1523  # 学员数量
    )
]

@router.get("/products", response_model=List[Product])
def get_products():
    """获取所有产品列表"""
    return PRODUCTS_DATA

@router.get("/products/{product_id}", response_model=Product)
def get_product(product_id: str):
    """获取指定产品详情"""
    for product in PRODUCTS_DATA:
        if product.id == product_id:
            return product
    return None 