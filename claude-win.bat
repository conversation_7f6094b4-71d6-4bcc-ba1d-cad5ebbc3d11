@echo off
setlocal enabledelayedexpansion

REM Windows原生Claude CLI
REM 使用WSL中的claude命令，但提供Windows友好的接口

if "%~1"=="" (
    REM 交互模式
    wsl claude
) else if "%~1"=="--help" (
    wsl claude --help
) else if "%~1"=="--version" (
    wsl claude --version
) else if "%~1"=="config" (
    wsl claude config %*
) else (
    REM 处理所有参数
    set "args="
    :loop
    if "%~1"=="" goto execute
    if "%~1"=="--print" (
        set "args=!args! --print"
    ) else if "%~1"=="--model" (
        set "args=!args! --model %~2"
        shift
    ) else if "%~1"=="--continue" (
        set "args=!args! --continue"
    ) else if "%~1"=="--resume" (
        set "args=!args! --resume"
    ) else if "%~1"=="--debug" (
        set "args=!args! --debug"
    ) else (
        REM 处理带空格的参数
        set "args=!args! "%~1""
    )
    shift
    goto loop
    
    :execute
    wsl claude!args!
) 