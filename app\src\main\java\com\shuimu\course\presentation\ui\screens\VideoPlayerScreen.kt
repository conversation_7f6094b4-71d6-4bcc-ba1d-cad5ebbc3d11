package com.shuimu.course.presentation.ui.screens

import android.content.ComponentName
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.session.MediaController
import androidx.media3.session.SessionToken
import androidx.media3.ui.PlayerView
import com.google.common.util.concurrent.ListenableFuture
import com.shuimu.course.player.PlaybackService
import com.shuimu.course.presentation.ui.components.video.CustomPlayerController
import com.shuimu.course.presentation.ui.components.video.PlaylistPanel
import com.shuimu.course.presentation.ui.components.video.VideoInfoPanel
import com.shuimu.course.presentation.viewmodel.PlayerStatus
import com.shuimu.course.presentation.viewmodel.VideoPlayerViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import androidx.core.content.ContextCompat
import androidx.media3.common.util.UnstableApi

@androidx.media3.common.util.UnstableApi
@Composable
fun VideoPlayerScreen(
    viewModel: VideoPlayerViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsState()
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    var mediaController by remember { mutableStateOf<MediaController?>(null) }
    var isPlaying by remember { mutableStateOf(false) }

    DisposableEffect(Unit) {
        val sessionToken = SessionToken(context, ComponentName(context, PlaybackService::class.java))
        val controllerFuture = MediaController.Builder(context, sessionToken).buildAsync()
        controllerFuture.addListener({
            mediaController = controllerFuture.get()
            mediaController?.addListener(object : Player.Listener {
                override fun onIsPlayingChanged(playing: Boolean) {
                    isPlaying = playing
                    viewModel.onPlayerStatusChanged(if (playing) PlayerStatus.PLAYING else PlayerStatus.PAUSED)
                }
            })
        }, ContextCompat.getMainExecutor(context))

        onDispose {
            android.util.Log.d("VideoPlayerScreen", "页面销毁，停止播放并释放资源")
            // 先暂停播放，再释放资源
            mediaController?.pause()
            mediaController?.stop()
            mediaController?.release()
        }
    }

    // 监听生命周期事件，确保页面切换时暂停播放
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    android.util.Log.d("VideoPlayerScreen", "页面暂停，暂停播放并同步进度")
                    mediaController?.let { controller ->
                        controller.pause()
                        // 页面暂停时同步进度
                        viewModel.onPlayerClosed(
                            controller.currentPosition,
                            controller.duration
                        )
                    }
                }
                Lifecycle.Event.ON_STOP -> {
                    android.util.Log.d("VideoPlayerScreen", "页面停止，停止播放并同步进度")
                    mediaController?.let { controller ->
                        // 页面停止时同步进度
                        viewModel.onPlayerClosed(
                            controller.currentPosition,
                            controller.duration
                        )
                        controller.pause()
                    }
                }
                Lifecycle.Event.ON_DESTROY -> {
                    android.util.Log.d("VideoPlayerScreen", "页面销毁，释放播放器")
                    mediaController?.let { controller ->
                        // 页面销毁时最后一次同步进度
                        viewModel.onPlayerClosed(
                            controller.currentPosition,
                            controller.duration
                        )
                        controller.stop()
                        controller.release()
                    }
                }
                else -> {}
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    LaunchedEffect(mediaController, state.video) {
        val video = state.video
        if (mediaController != null && video != null) {
            val mediaItem = MediaItem.fromUri(video.cloudUrl)
            mediaController?.setMediaItem(mediaItem)
            mediaController?.prepare()
            mediaController?.playWhenReady = true
        }
    }

    LaunchedEffect(mediaController, state.initialSeekPosition) {
        val seekPos = state.initialSeekPosition
        if (mediaController != null && seekPos != null && seekPos > 0) {
            mediaController?.seekTo(seekPos * 1000)
            viewModel.onInitialSeekDone()
        }
    }

    LaunchedEffect(state.playerStatus) {
        if (state.playerStatus == PlayerStatus.PLAYING) {
            while (true) {
                val currentPosition = mediaController?.currentPosition ?: 0
                val totalDuration = mediaController?.duration ?: 0
                viewModel.saveProgress(currentPosition, totalDuration)
                delay(5000)
            }
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(16 / 9f)
        ) {
            AndroidView(
                factory = { ctx ->
                    PlayerView(ctx).apply {
                        useController = false // Disable default controls
                        player = mediaController
                    }
                },
                update = { playerView ->
                    playerView.player = mediaController
                },
                modifier = Modifier.fillMaxSize()
            )

            CustomPlayerController(
                isPlaying = isPlaying,
                onPlayPause = {
                    if (mediaController?.isPlaying == true) {
                        mediaController?.pause()
                    } else {
                        mediaController?.play()
                    }
                },
                onRewind = { mediaController?.seekBack() },
                onFastForward = { mediaController?.seekForward() },
                onNext = { mediaController?.seekToNextMediaItem() },
                onPrevious = { mediaController?.seekToPreviousMediaItem() }
            )
        }

        state.video?.let { video ->
            VideoInfoPanel(
                title = video.title,
                playCount = video.playCount,
                description = video.description
            )
            // PlaylistPanel(...)
        }
    }
}

// A more complete implementation would require connecting to the MediaSessionService
// which is complex to do correctly in a single step. The code above is a structural placeholder.

@Preview
@Composable
fun VideoPlayerScreenPreview() {
    VideoPlayerScreen()
} 